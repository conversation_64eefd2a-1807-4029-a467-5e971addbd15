{"bsonType": "object", "required": ["from_user_id", "to_user_id", "content", "type"], "permission": {"read": true, "create": true, "update": "'UPDATE_RENTAL_MESSAGES' in auth.permission", "delete": "'DELETE_RENTAL_MESSAGES' in auth.permission"}, "properties": {"_id": {"description": "消息ID，系统自动生成"}, "from_user_id": {"bsonType": "string", "title": "发送者ID", "description": "消息发送者用户ID", "foreignKey": "uni-id-users._id"}, "to_user_id": {"bsonType": "string", "title": "接收者ID", "description": "消息接收者用户ID", "foreignKey": "uni-id-users._id"}, "house_id": {"bsonType": "string", "title": "房源ID", "description": "相关房源ID（可选）", "foreignKey": "rental-houses._id"}, "appointment_id": {"bsonType": "string", "title": "预约ID", "description": "相关预约ID（可选）", "foreignKey": "rental-appointments._id"}, "type": {"bsonType": "string", "title": "消息类型", "description": "消息类型分类", "enum": ["系统通知", "预约消息", "咨询消息", "评价消息", "其他"]}, "title": {"bsonType": "string", "title": "消息标题", "description": "消息标题", "maxLength": 100, "trim": "both"}, "content": {"bsonType": "string", "title": "消息内容", "description": "消息详细内容", "maxLength": 1000, "trim": "both"}, "is_read": {"bsonType": "bool", "title": "是否已读", "description": "消息是否已被阅读", "defaultValue": false}, "read_date": {"bsonType": "timestamp", "title": "阅读时间", "description": "消息被阅读的时间"}, "create_date": {"bsonType": "timestamp", "title": "创建时间", "description": "消息创建时间", "forceDefaultValue": {"$env": "now"}}}}
{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/userinfo/deactivate/deactivate.vue?0539", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/userinfo/deactivate/deactivate.vue?49fa", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/userinfo/deactivate/deactivate.vue?86c2", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/userinfo/deactivate/deactivate.vue?3000", "uni-app:///uni_modules/uni-id-pages/pages/userinfo/deactivate/deactivate.vue", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/userinfo/deactivate/deactivate.vue?413f", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/userinfo/deactivate/deactivate.vue?65bc"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "onLoad", "methods", "cancel", "uni", "nextStep", "content", "complete", "uniIdco", "title", "duration", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACa;;;AAGtE;AACkL;AAClL,gBAAgB,gLAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAkrB,CAAgB,moBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCyBtsB;EACAC;IACA,QAEA;EACA;EACAC;EACAC;IACAC;MACAC;IACA;IACAC;MACAD;QACAE;QACAC;UACA;YACA;YACAC;cACAJ;gBACAK;gBACAC;cACA;cACAN;cACAA;cACAA;gBACAO;cACA;YACA;UACA;YACAP;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5DA;AAAA;AAAA;AAAA;AAAm+B,CAAgB,83BAAG,EAAC,C;;;;;;;;;;;ACAv/B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-id-pages/pages/userinfo/deactivate/deactivate.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './uni_modules/uni-id-pages/pages/userinfo/deactivate/deactivate.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./deactivate.vue?vue&type=template&id=d27b49e2&\"\nvar renderjs\nimport script from \"./deactivate.vue?vue&type=script&lang=js&\"\nexport * from \"./deactivate.vue?vue&type=script&lang=js&\"\nimport style0 from \"./deactivate.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-id-pages/pages/userinfo/deactivate/deactivate.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./deactivate.vue?vue&type=template&id=d27b49e2&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./deactivate.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./deactivate.vue?vue&type=script&lang=js&\"", "<!-- 注销（销毁）账号 -->\n<template>\r\n\t<view class=\"uni-content\">\r\n\t\t<text class=\"words\" space=\"emsp\">\r\n\t\t\t一、注销是不可逆操作，注销后:\\n\r\n\t\t\t1.帐号将无法登录、无法找回。\\n\r\n\t\t\t2.帐号所有信息都会清除(个人身份信息、粉丝数等;发布的作品、评论、点赞等;交易信息等)，你\r\n\t\t\t的朋友将无法通过本应用帐号联系你，请自行备份相关\r\n\t\t\t信息和数据。\\n\r\n\r\n\t\t\t二、重要提示\\n\r\n\t\t\t1.封禁帐号(永久封禁、社交封禁、直播权限封禁)不能申请注销。\\n\r\n\t\t\t2.注销后，你的身份证、三方帐号(微信、QQ、微博、支付宝)、手机号等绑定关系将解除，解除后可以绑定到其他帐号。\\n\r\n\t\t\t3.注销后，手机号可以注册新的帐号，新帐号不会存在之前帐号的任何信息(作品、粉丝、评论、个人信息等)。\\n\r\n\t\t\t4.注销本应用帐号前，需尽快处理帐号下的资金问题。\\n\r\n\t\t\t5.视具体帐号情况而定，注销最多需要7天。\\n\r\n\t\t</text>\r\n\t\t<view class=\"button-group\">\r\n\t\t\t<button @click=\"nextStep\" class=\"next\" type=\"default\">下一步</button>\r\n\t\t\t<button @click=\"cancel\" type=\"warn\">取消</button>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {},\r\n\t\tmethods: {\r\n\t\t\tcancel() {\r\n\t\t\t\tuni.navigateBack()\r\n\t\t\t},\r\n\t\t\tnextStep() {\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\tcontent: '已经仔细阅读注销提示，知晓可能带来的后果，并确认要注销',\r\n\t\t\t\t\tcomplete: (e) => {\r\n\t\t\t\t\t\tif (e.confirm) {\r\n\t\t\t\t\t\t\tconst uniIdco = uniCloud.importObject(\"uni-id-co\");\r\n\t\t\t\t\t\t\tuniIdco.closeAccount().then((e) => {\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '注销成功',\n\t\t\t\t\t\t\t\t\tduration: 3000\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\tuni.removeStorageSync('uni_id_token');\r\n\t\t\t\t\t\t\t\tuni.setStorageSync('uni_id_token_expired', 0)\r\n\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\turl:\"/uni_modules/uni-id-pages/pages/login/login-withoutpwd\"\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.uni-content {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\r\n\t.words {\r\n\t\tpadding: 0 26rpx;\r\n\t\tline-height: 46rpx;\r\n\t\tmargin-top: 20rpx;\r\n\t\tmargin-bottom: 80px;\r\n\t}\r\n\r\n\t.button-group button {\r\n\t\tborder-radius: 100px;\r\n\t\tborder: none;\r\n\t\twidth: 300rpx;\r\n\t\theight: 42px;\r\n\t\tline-height: 42px;\r\n\t\tfont-size: 32rpx;\r\n\t}\r\n\r\n\t.button-group button:after {\r\n\t\tborder: none;\r\n\t}\r\n\r\n\t.button-group button.next {\r\n\t\tcolor: #e64340;\r\n\t\tborder: solid 1px #e64340;\r\n\t}\r\n\t.button-group {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tposition: fixed;\n\t\theight: 50px;\n\t\tbottom: 10px;\n\t\twidth: 750rpx;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tborder-top: solid 1px #e4e6ec;\n\t\tpadding-top: 10px;\n\t\tbackground-color: #FFFFFF;\n\t\tmax-width: 690px;\n\t}\n\t\r\n\t\r\n\t@media screen and (min-width: 690px) {\n\t\t.uni-content{\n\t\t\tmax-width: 690px;\n\t\t\tmargin-left: calc(50% - 345px);\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./deactivate.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./deactivate.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753757589865\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
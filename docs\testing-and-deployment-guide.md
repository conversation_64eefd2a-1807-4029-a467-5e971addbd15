# 租房平台测试与部署指南

## 1. 测试计划

### 1.1 功能测试

#### 1.1.1 用户管理测试
- [ ] 用户注册功能
- [ ] 用户登录功能
- [ ] 用户信息修改
- [ ] 权限控制验证
- [ ] 角色切换测试

#### 1.1.2 房源管理测试
- [ ] 房源发布功能
- [ ] 房源编辑功能
- [ ] 房源删除功能
- [ ] 房源审核流程
- [ ] 房源状态管理

#### 1.1.3 搜索筛选测试
- [ ] 关键词搜索
- [ ] 价格筛选
- [ ] 房型筛选
- [ ] 地区筛选
- [ ] 设施筛选
- [ ] 排序功能

#### 1.1.4 预约管理测试
- [ ] 预约创建功能
- [ ] 预约确认流程
- [ ] 预约状态更新
- [ ] 预约取消功能
- [ ] 预约历史查看

#### 1.1.5 收藏功能测试
- [ ] 添加收藏
- [ ] 取消收藏
- [ ] 收藏列表查看
- [ ] 批量管理收藏

### 1.2 性能测试

#### 1.2.1 页面加载性能
- [ ] 首页加载时间 < 2秒
- [ ] 房源列表加载时间 < 3秒
- [ ] 房源详情加载时间 < 2秒
- [ ] 图片加载优化测试

#### 1.2.2 数据库性能
- [ ] 房源查询性能测试
- [ ] 复杂筛选查询性能
- [ ] 大数据量分页性能
- [ ] 并发访问测试

#### 1.2.3 云函数性能
- [ ] 云函数冷启动时间
- [ ] 云函数执行时间
- [ ] 云函数并发处理能力

### 1.3 兼容性测试

#### 1.3.1 设备兼容性
- [ ] iPhone (iOS 12+)
- [ ] Android (Android 7+)
- [ ] 不同屏幕尺寸适配
- [ ] 横竖屏切换

#### 1.3.2 微信版本兼容性
- [ ] 微信最新版本
- [ ] 微信历史版本
- [ ] 微信开发者工具

### 1.4 安全测试

#### 1.4.1 数据安全
- [ ] 用户数据权限控制
- [ ] 敏感信息加密
- [ ] SQL注入防护
- [ ] XSS攻击防护

#### 1.4.2 业务安全
- [ ] 防刷机制测试
- [ ] 恶意数据过滤
- [ ] 频率限制测试

## 2. 性能优化方案

### 2.1 前端优化

#### 2.1.1 图片优化
```javascript
// 图片懒加载实现
const imageObserver = new IntersectionObserver((entries) => {
  entries.forEach(entry => {
    if (entry.isIntersecting) {
      const img = entry.target
      img.src = img.dataset.src
      imageObserver.unobserve(img)
    }
  })
})

// 图片压缩配置
const imageConfig = {
  quality: 0.8,
  maxWidth: 750,
  maxHeight: 750
}
```

#### 2.1.2 数据缓存
```javascript
// 本地缓存配置
const cacheConfig = {
  houseList: 5 * 60 * 1000, // 5分钟
  houseDetail: 10 * 60 * 1000, // 10分钟
  userInfo: 24 * 60 * 60 * 1000 // 24小时
}

// 缓存实现
function setCache(key, data, expire) {
  const cacheData = {
    data: data,
    expire: Date.now() + expire
  }
  uni.setStorageSync(key, cacheData)
}

function getCache(key) {
  const cacheData = uni.getStorageSync(key)
  if (cacheData && cacheData.expire > Date.now()) {
    return cacheData.data
  }
  return null
}
```

### 2.2 后端优化

#### 2.2.1 数据库索引优化
```javascript
// 房源表索引
db.collection('rental-houses').createIndex({
  "address.city": 1,
  "address.district": 1,
  "rent_price": 1,
  "house_type": 1,
  "status": 1
})

// 预约表索引
db.collection('rental-appointments').createIndex({
  "tenant_id": 1,
  "landlord_id": 1,
  "status": 1,
  "create_date": -1
})
```

#### 2.2.2 云函数优化
```javascript
// 云函数缓存
const cache = new Map()

module.exports = {
  async getHouseList(params) {
    const cacheKey = JSON.stringify(params)
    
    // 检查缓存
    if (cache.has(cacheKey)) {
      const cached = cache.get(cacheKey)
      if (Date.now() - cached.timestamp < 5 * 60 * 1000) {
        return cached.data
      }
    }
    
    // 查询数据
    const result = await this.queryHouses(params)
    
    // 设置缓存
    cache.set(cacheKey, {
      data: result,
      timestamp: Date.now()
    })
    
    return result
  }
}
```

## 3. 部署指南

### 3.1 开发环境搭建

#### 3.1.1 工具安装
1. 下载安装 HBuilderX
2. 安装微信开发者工具
3. 注册uniCloud账号
4. 创建uniCloud服务空间

#### 3.1.2 项目配置
1. 导入项目到HBuilderX
2. 配置uniCloud服务空间
3. 上传云函数
4. 初始化数据库
5. 配置小程序AppID

### 3.2 数据库初始化

#### 3.2.1 创建数据表
```bash
# 在uniCloud控制台执行
1. 创建 rental-houses 表
2. 创建 rental-appointments 表
3. 创建 rental-favorites 表
4. 创建 rental-categories 表
5. 创建 rental-messages 表
```

#### 3.2.2 导入初始数据
```bash
# 导入分类数据
导入 rental-categories.init_data.json

# 导入示例房源数据
导入 rental-houses.init_data.json

# 导入权限数据
导入 uni-id-permissions.init_data.json

# 导入角色数据
导入 uni-id-roles.init_data.json
```

### 3.3 云函数部署

#### 3.3.1 上传云函数
```bash
# 在HBuilderX中右键云函数目录
1. 上传 rental-service 云函数
2. 配置云函数URL化（如需要）
3. 设置云函数定时触发器（如需要）
```

#### 3.3.2 云函数配置
```javascript
// 云函数环境变量配置
{
  "NODE_ENV": "production",
  "DB_CONNECTION": "mongodb://...",
  "REDIS_URL": "redis://...",
  "SECRET_KEY": "your-secret-key"
}
```

### 3.4 小程序发布

#### 3.4.1 开发版本测试
1. 在微信开发者工具中预览
2. 真机调试测试
3. 功能完整性验证

#### 3.4.2 提交审核
1. 上传代码到微信后台
2. 填写版本信息
3. 提交审核
4. 等待审核结果

### 3.5 管理后台部署

#### 3.5.1 Web端部署
```bash
# 构建生产版本
npm run build:h5

# 部署到服务器
# 可以部署到阿里云、腾讯云等云服务器
# 或者使用uniCloud前端网页托管
```

#### 3.5.2 域名配置
1. 配置域名解析
2. 申请SSL证书
3. 配置HTTPS访问

## 4. 监控与维护

### 4.1 性能监控

#### 4.1.1 关键指标
- 页面加载时间
- 接口响应时间
- 错误率统计
- 用户活跃度

#### 4.1.2 监控工具
- uniCloud云函数监控
- 微信小程序数据助手
- 第三方APM工具

### 4.2 日常维护

#### 4.2.1 数据备份
- 定期备份数据库
- 备份云函数代码
- 备份静态资源

#### 4.2.2 安全更新
- 定期更新依赖包
- 修复安全漏洞
- 更新权限配置

### 4.3 用户反馈处理

#### 4.3.1 反馈渠道
- 小程序内反馈功能
- 客服微信群
- 邮件反馈

#### 4.3.2 问题处理流程
1. 收集用户反馈
2. 分析问题原因
3. 制定解决方案
4. 测试验证修复
5. 发布更新版本

## 5. 上线检查清单

### 5.1 功能检查
- [ ] 所有核心功能正常
- [ ] 用户权限控制正确
- [ ] 数据安全措施到位
- [ ] 性能指标达标

### 5.2 配置检查
- [ ] 生产环境配置正确
- [ ] 域名和证书配置
- [ ] 监控和日志配置
- [ ] 备份策略配置

### 5.3 文档检查
- [ ] 用户使用手册
- [ ] 管理员操作手册
- [ ] 技术文档完整
- [ ] 应急处理预案

通过以上测试和部署流程，可以确保租房平台系统的稳定性、安全性和用户体验，为正式上线运营做好充分准备。

{"bsonType": "object", "required": ["house_id", "user_id"], "permission": {"read": true, "create": true, "update": false, "delete": true}, "properties": {"_id": {"description": "收藏ID，系统自动生成"}, "house_id": {"bsonType": "string", "title": "房源ID", "description": "收藏的房源ID", "foreignKey": "rental-houses._id"}, "user_id": {"bsonType": "string", "title": "用户ID", "description": "收藏用户ID", "foreignKey": "uni-id-users._id"}, "create_date": {"bsonType": "timestamp", "title": "收藏时间", "description": "收藏创建时间", "forceDefaultValue": {"$env": "now"}}}}
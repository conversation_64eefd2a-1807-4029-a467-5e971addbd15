<uni-popup vue-id="774fb9b6-1" type="bottom" data-ref="popup" class="data-v-0c5ef59a vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="box data-v-0c5ef59a"><text class="headBox data-v-0c5ef59a">绑定资料</text><text class="tip data-v-0c5ef59a">将一键获取你的手机号码绑定你的个人资料</text><view class="btnBox data-v-0c5ef59a"><text data-event-opts="{{[['tap',[['closeMe',['$event']]]]]}}" class="close data-v-0c5ef59a" bindtap="__e">关闭</text><button class="agree uni-btn data-v-0c5ef59a" type="primary" open-type="getPhoneNumber" data-event-opts="{{[['getphonenumber',[['bindMobileByMpWeixin',['$event']]]]]}}" bindgetphonenumber="__e">获取</button></view></view></uni-popup>
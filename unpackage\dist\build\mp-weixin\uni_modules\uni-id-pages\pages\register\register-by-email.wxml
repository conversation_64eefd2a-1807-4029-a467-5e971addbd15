<view class="uni-content"><match-media min-width="{{690}}"><view class="login-logo"><image src="{{logo}}"></image></view><text class="title title-box">邮箱验证码注册</text></match-media><uni-forms class="vue-ref" vue-id="521afbec-1" value="{{formData}}" rules="{{rules}}" validate-trigger="submit" err-show-type="toast" data-ref="form" bind:__l="__l" vue-slots="{{['default']}}"><uni-forms-item vue-id="{{('521afbec-2')+','+('521afbec-1')}}" name="email" required="{{true}}" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput class="input-box" vue-id="{{('521afbec-3')+','+('521afbec-2')}}" inputBorder="{{false}}" focus="{{focusEmail}}" placeholder="请输入邮箱" trim="both" value="{{formData.email}}" data-event-opts="{{[['^blur',[['e0']]],['^input',[['__set_model',['$0','email','$event',[]],['formData']]]]]}}" bind:blur="__e" bind:input="__e" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item vue-id="{{('521afbec-4')+','+('521afbec-1')}}" name="nickname" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput class="input-box" vue-id="{{('521afbec-5')+','+('521afbec-4')}}" inputBorder="{{false}}" focus="{{focusNickname}}" placeholder="请输入用户昵称" trim="both" value="{{formData.nickname}}" data-event-opts="{{[['^blur',[['e1']]],['^input',[['__set_model',['$0','nickname','$event',[]],['formData']]]]]}}" bind:blur="__e" bind:input="__e" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item bind:input="__e" vue-id="{{('521afbec-6')+','+('521afbec-1')}}" name="password" required="{{true}}" value="{{formData.password}}" data-event-opts="{{[['^input',[['__set_model',['$0','password','$event',[]],['formData']]]]]}}" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput class="input-box" vue-id="{{('521afbec-7')+','+('521afbec-6')}}" inputBorder="{{false}}" focus="{{focusPassword}}" maxlength="20" placeholder="{{'请输入'+(config.passwordStrength=='weak'?'6':'8')+'-16位密码'}}" type="password" trim="both" value="{{formData.password}}" data-event-opts="{{[['^blur',[['e2']]],['^input',[['__set_model',['$0','password','$event',[]],['formData']]]]]}}" bind:blur="__e" bind:input="__e" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item bind:input="__e" vue-id="{{('521afbec-8')+','+('521afbec-1')}}" name="password2" required="{{true}}" value="{{formData.password2}}" data-event-opts="{{[['^input',[['__set_model',['$0','password2','$event',[]],['formData']]]]]}}" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput class="input-box" vue-id="{{('521afbec-9')+','+('521afbec-8')}}" inputBorder="{{false}}" focus="{{focusPassword2}}" placeholder="再次输入密码" maxlength="20" type="password" trim="both" value="{{formData.password2}}" data-event-opts="{{[['^blur',[['e3']]],['^input',[['__set_model',['$0','password2','$event',[]],['formData']]]]]}}" bind:blur="__e" bind:input="__e" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item vue-id="{{('521afbec-10')+','+('521afbec-1')}}" name="code" bind:__l="__l" vue-slots="{{['default']}}"><uni-id-pages-email-form bind:input="__e" class="vue-ref" vue-id="{{('521afbec-11')+','+('521afbec-10')}}" email="{{formData.email}}" type="register" data-ref="shortCode" value="{{formData.code}}" data-event-opts="{{[['^input',[['__set_model',['$0','code','$event',[]],['formData']]]]]}}" bind:__l="__l"></uni-id-pages-email-form></uni-forms-item><uni-id-pages-agreements class="vue-ref" vue-id="{{('521afbec-12')+','+('521afbec-1')}}" scope="register" data-ref="agreements" bind:__l="__l"></uni-id-pages-agreements><button class="uni-btn" type="primary" data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" bindtap="__e">注册</button><button data-event-opts="{{[['tap',[['navigateBack',['$event']]]]]}}" class="register-back" bindtap="__e">返回</button><match-media min-width="{{690}}"><view class="link-box"><text data-event-opts="{{[['tap',[['registerByUserName',['$event']]]]]}}" class="link" bindtap="__e">用户名密码注册</text><text data-event-opts="{{[['tap',[['toLogin',['$event']]]]]}}" class="link" bindtap="__e">已有账号？点此登录</text></view></match-media></uni-forms></view>
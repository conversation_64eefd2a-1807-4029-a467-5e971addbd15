<uni-popup vue-id="adfcd482-1" type="center" data-ref="popup" class="data-v-f7cd1ae2 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup-captcha data-v-f7cd1ae2"><view class="content data-v-f7cd1ae2"><text class="title data-v-f7cd1ae2">{{title}}</text><uni-captcha bind:input="__e" vue-id="{{('adfcd482-2')+','+('adfcd482-1')}}" focus="{{focus}}" scene="{{scene}}" value="{{val}}" data-event-opts="{{[['^input',[['__set_model',['','val','$event',[]]]]]]}}" class="data-v-f7cd1ae2" bind:__l="__l"></uni-captcha></view><view class="button-box data-v-f7cd1ae2"><view data-event-opts="{{[['tap',[['close',['$event']]]]]}}" class="btn data-v-f7cd1ae2" bindtap="__e">取消</view><view data-event-opts="{{[['tap',[['confirm',['$event']]]]]}}" class="btn confirm data-v-f7cd1ae2" bindtap="__e">确认</view></view></view></uni-popup>
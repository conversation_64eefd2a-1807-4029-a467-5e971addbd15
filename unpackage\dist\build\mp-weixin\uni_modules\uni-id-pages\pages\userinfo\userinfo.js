(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uni_modules/uni-id-pages/pages/userinfo/userinfo"],{2616:function(n,e,t){"use strict";t.r(e);var i=t("d9ba"),u=t("6ade");for(var o in u)["default"].indexOf(o)<0&&function(n){t.d(e,n,(function(){return u[n]}))}(o);t("96af");var a=t("828b"),r=Object(a["a"])(u["default"],i["b"],i["c"],!1,null,"5c20cd7a",null,!1,i["a"],void 0);e["default"]=r.exports},"45e0":function(n,e,t){},"47f0":function(n,e,t){"use strict";(function(n,i){var u=t("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=u(t("7eb4")),a=u(t("ee10")),r=t("f12a"),s=n.importObject("uni-id-co"),c={computed:{userInfo:function(){return r.store.userInfo},realNameStatus:function(){return this.userInfo.realNameAuth?this.userInfo.realNameAuth.authStatus:0}},data:function(){return{univerifyStyle:{authButton:{title:"本机号码一键绑定"},otherLoginButton:{title:"其他号码绑定"}},hasPwd:!1,showLoginManage:!1,setNicknameIng:!1}},onShow:function(){var n=this;return(0,a.default)(o.default.mark((function e(){return o.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:n.univerifyStyle.authButton.title="本机号码一键绑定",n.univerifyStyle.otherLoginButton.title="其他号码绑定";case 2:case"end":return e.stop()}}),e)})))()},onLoad:function(n){var e=this;return(0,a.default)(o.default.mark((function t(){var i;return o.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return n.showLoginManage&&(e.showLoginManage=!0),t.next=3,s.getAccountInfo();case 3:i=t.sent,e.hasPwd=i.isPasswordSet;case 5:case"end":return t.stop()}}),t)})))()},methods:{login:function(){i.navigateTo({url:"/uni_modules/uni-id-pages/pages/login/login-withoutpwd",complete:function(n){}})},logout:function(){r.mutations.logout()},bindMobileSuccess:function(){r.mutations.updateUserInfo()},changePassword:function(){i.navigateTo({url:"/uni_modules/uni-id-pages/pages/userinfo/change_pwd/change_pwd",complete:function(n){}})},bindMobile:function(){this.$refs["bind-mobile-by-sms"].open()},univerify:function(){var n=this;i.login({provider:"univerify",univerifyStyle:this.univerifyStyle,success:function(){var n=(0,a.default)(o.default.mark((function n(e){return o.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:s.bindMobileByUniverify(e.authResult).then((function(n){r.mutations.updateUserInfo()})).catch((function(n){console.log(n)})).finally((function(n){i.closeAuthView()}));case 1:case"end":return n.stop()}}),n)})));return function(e){return n.apply(this,arguments)}}(),fail:function(e){console.log(e),"30002"!=e.code&&"30001"!=e.code||n.bindMobileBySmsCode()}})},bindMobileBySmsCode:function(){i.navigateTo({url:"./bind-mobile/bind-mobile"})},setNickname:function(n){n?(r.mutations.updateUserInfo({nickname:n}),this.setNicknameIng=!1,this.$refs.dialog.close()):this.$refs.dialog.open()},deactivate:function(){i.navigateTo({url:"/uni_modules/uni-id-pages/pages/userinfo/deactivate/deactivate"})},bindThirdAccount:function(e){var t=this;return(0,a.default)(o.default.mark((function u(){var s,c;return o.default.wrap((function(u){while(1)switch(u.prev=u.next){case 0:if(s=n.importObject("uni-id-co"),c={weixin:"wx_openid",alipay:"ali_openid",apple:"apple_openid",qq:"qq_openid"}[e.toLowerCase()],!t.userInfo[c]){u.next=9;break}return u.next=5,s["unbind"+e]();case 5:return u.next=7,r.mutations.updateUserInfo();case 7:u.next=10;break;case 9:i.login({provider:e.toLowerCase(),onlyAuthorize:!0,success:function(){var n=(0,a.default)(o.default.mark((function n(t){var u;return o.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,s["bind"+e]({code:t.code});case 2:return u=n.sent,u.errCode&&i.showToast({title:u.errMsg||"绑定失败",duration:3e3}),n.next=6,r.mutations.updateUserInfo();case 6:case"end":return n.stop()}}),n)})));return function(e){return n.apply(this,arguments)}}(),fail:function(){var n=(0,a.default)(o.default.mark((function n(e){return o.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:console.log(e),i.hideLoading();case 2:case"end":return n.stop()}}),n)})));return function(e){return n.apply(this,arguments)}}()});case 10:case"end":return u.stop()}}),u)})))()},realNameVerify:function(){i.navigateTo({url:"/uni_modules/uni-id-pages/pages/userinfo/realname-verify/realname-verify"})}}};e.default=c}).call(this,t("861b")["uniCloud"],t("df3c")["default"])},"4abf":function(n,e,t){"use strict";(function(n,e){var i=t("47a9");t("a019"),t("861b");i(t("3240"));var u=i(t("2616"));n.__webpack_require_UNI_MP_PLUGIN__=t,e(u.default)}).call(this,t("3223")["default"],t("df3c")["createPage"])},"6ade":function(n,e,t){"use strict";t.r(e);var i=t("47f0"),u=t.n(i);for(var o in i)["default"].indexOf(o)<0&&function(n){t.d(e,n,(function(){return i[n]}))}(o);e["default"]=u.a},"96af":function(n,e,t){"use strict";var i=t("45e0"),u=t.n(i);u.a},d9ba:function(n,e,t){"use strict";t.d(e,"b",(function(){return u})),t.d(e,"c",(function(){return o})),t.d(e,"a",(function(){return i}));var i={uniIdPagesAvatar:function(){return t.e("uni_modules/uni-id-pages/components/uni-id-pages-avatar/uni-id-pages-avatar").then(t.bind(null,"bffa"))},uniList:function(){return t.e("uni_modules/uni-list/components/uni-list/uni-list").then(t.bind(null,"0031"))},uniListItem:function(){return t.e("uni_modules/uni-list/components/uni-list-item/uni-list-item").then(t.bind(null,"eb88"))},uniPopup:function(){return t.e("uni_modules/uni-popup/components/uni-popup/uni-popup").then(t.bind(null,"2f8a"))},uniPopupDialog:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uni-popup/components/uni-popup-dialog/uni-popup-dialog")]).then(t.bind(null,"57e5"))},uniIdPagesBindMobile:function(){return t.e("uni_modules/uni-id-pages/components/uni-id-pages-bind-mobile/uni-id-pages-bind-mobile").then(t.bind(null,"93f9"))}},u=function(){var n=this.$createElement;this._self._c},o=[]}},[["4abf","common/runtime","common/vendor"]]]);
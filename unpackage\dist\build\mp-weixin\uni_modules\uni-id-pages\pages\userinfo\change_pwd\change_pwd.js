(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uni_modules/uni-id-pages/pages/userinfo/change_pwd/change_pwd"],{"02f9":function(e,n,t){"use strict";t.d(n,"b",(function(){return r})),t.d(n,"c",(function(){return u})),t.d(n,"a",(function(){return o}));var o={uniForms:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uni-forms/components/uni-forms/uni-forms")]).then(t.bind(null,"bd01"))},uniFormsItem:function(){return t.e("uni_modules/uni-forms/components/uni-forms-item/uni-forms-item").then(t.bind(null,"1097"))},uniEasyinput:function(){return t.e("uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput").then(t.bind(null,"b6bb"))}},r=function(){var e=this,n=e.$createElement;e._self._c;e._isMounted||(e.e0=function(n){e.focusOldPassword=!1},e.e1=function(n){e.focusNewPassword=!1},e.e2=function(n){e.focusNewPassword2=!1})},u=[]},"34b6":function(e,n,t){"use strict";t.r(n);var o=t("02f9"),r=t("c04b");for(var u in r)["default"].indexOf(u)<0&&function(e){t.d(n,e,(function(){return r[e]}))}(u);t("53b3");var s=t("828b"),i=Object(s["a"])(r["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);n["default"]=i.exports},"53b3":function(e,n,t){"use strict";var o=t("ec47"),r=t.n(o);r.a},"71d7":function(e,n,t){"use strict";(function(e,o){var r=t("47a9");Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var u=r(t("7ca3")),s=r(t("f6dd")),i=r(t("4db8"));function a(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);n&&(o=o.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,o)}return t}function c(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?a(Object(t),!0).forEach((function(n){(0,u.default)(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):a(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}var d=e.importObject("uni-id-co",{customUI:!0}),f={mixins:[s.default],data:function(){return{focusOldPassword:!1,focusNewPassword:!1,focusNewPassword2:!1,formData:{oldPassword:"",newPassword:"",newPassword2:""},rules:c({oldPassword:{rules:[{required:!0,errorMessage:"请输入新密码"},{pattern:/^.{6,20}$/,errorMessage:"密码为6 - 20位"}]}},i.default.getPwdRules("newPassword","newPassword2")),logo:"/static/logo.png"}},onReady:function(){this.$refs.form.setRules(this.rules)},onShow:function(){},methods:{submit:function(){var e=this;this.$refs.form.validate().then((function(n){var t=e.formData,r=t.oldPassword,u=t.newPassword;d.updatePwd({oldPassword:r,newPassword:u}).then((function(e){o.removeStorageSync("uni_id_token"),o.setStorageSync("uni_id_token_expired",0),o.redirectTo({url:"/uni_modules/uni-id-pages/pages/login/login-withpwd"})})).catch((function(e){o.showModal({content:e.message,showCancel:!1})}))})).catch((function(n){var t=n[0].key;t=t.replace(t[0],t[0].toUpperCase()),e["focus"+t]=!0}))}}};n.default=f}).call(this,t("861b")["uniCloud"],t("df3c")["default"])},"7aeb":function(e,n,t){"use strict";(function(e,n){var o=t("47a9");t("a019"),t("861b");o(t("3240"));var r=o(t("34b6"));e.__webpack_require_UNI_MP_PLUGIN__=t,n(r.default)}).call(this,t("3223")["default"],t("df3c")["createPage"])},c04b:function(e,n,t){"use strict";t.r(n);var o=t("71d7"),r=t.n(o);for(var u in o)["default"].indexOf(u)<0&&function(e){t.d(n,e,(function(){return o[e]}))}(u);n["default"]=r.a},ec47:function(e,n,t){}},[["7aeb","common/runtime","common/vendor"]]]);
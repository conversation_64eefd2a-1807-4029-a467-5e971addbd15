<view class="uni-content data-v-011d44f1"><match-media min-width="{{690}}" class="data-v-011d44f1"><view class="login-logo data-v-011d44f1"><image src="{{logo}}" class="data-v-011d44f1"></image></view><text class="title title-box data-v-011d44f1">设置密码</text></match-media><uni-forms class="set-password-form data-v-011d44f1 vue-ref" vue-id="6a1108f1-1" value="{{formData}}" err-show-type="toast" data-ref="form" bind:__l="__l" vue-slots="{{['default']}}"><text class="tip data-v-011d44f1">输入密码</text><uni-forms-item vue-id="{{('6a1108f1-2')+','+('6a1108f1-1')}}" name="newPassword" class="data-v-011d44f1" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput class="input-box data-v-011d44f1" vue-id="{{('6a1108f1-3')+','+('6a1108f1-2')}}" focus="{{focusNewPassword}}" type="password" inputBorder="{{false}}" placeholder="请输入密码" value="{{formData.newPassword}}" data-event-opts="{{[['^blur',[['e0']]],['^input',[['__set_model',['$0','newPassword','$event',[]],['formData']]]]]}}" bind:blur="__e" bind:input="__e" bind:__l="__l"></uni-easyinput></uni-forms-item><text class="tip data-v-011d44f1">再次输入密码</text><uni-forms-item vue-id="{{('6a1108f1-4')+','+('6a1108f1-1')}}" name="newPassword2" class="data-v-011d44f1" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput class="input-box data-v-011d44f1" vue-id="{{('6a1108f1-5')+','+('6a1108f1-4')}}" focus="{{focusNewPassword2}}" type="password" inputBorder="{{false}}" placeholder="请再次输入新密码" value="{{formData.newPassword2}}" data-event-opts="{{[['^blur',[['e1']]],['^input',[['__set_model',['$0','newPassword2','$event',[]],['formData']]]]]}}" bind:blur="__e" bind:input="__e" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-id-pages-sms-form bind:input="__e" vue-id="{{('6a1108f1-6')+','+('6a1108f1-1')}}" type="set-pwd-by-sms" phone="{{userInfo.mobile}}" data-ref="smsCode" value="{{formData.code}}" data-event-opts="{{[['^input',[['__set_model',['$0','code','$event',[]],['formData']]]]]}}" class="data-v-011d44f1 vue-ref" bind:__l="__l"></uni-id-pages-sms-form><view class="link-box data-v-011d44f1"><button class="uni-btn send-btn data-v-011d44f1" type="primary" data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" bindtap="__e">确认</button><block wx:if="{{allowSkip}}"><button class="uni-btn send-btn data-v-011d44f1" type="default" data-event-opts="{{[['tap',[['skip',['$event']]]]]}}" bindtap="__e">跳过</button></block></view></uni-forms><uni-popup-captcha vue-id="6a1108f1-7" scene="set-pwd-by-sms" data-ref="popup" value="{{formData.captcha}}" data-event-opts="{{[['^confirm',[['submit']]],['^input',[['__set_model',['$0','captcha','$event',[]],['formData']]]]]}}" bind:confirm="__e" bind:input="__e" class="data-v-011d44f1 vue-ref" bind:__l="__l"></uni-popup-captcha></view>
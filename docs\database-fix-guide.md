# 数据库重复键错误修复指南

## 问题描述

遇到了uni-id-users表mobile字段的重复键错误：
```
[uni-id-co]: InternalServerError:E11000 duplicate key error collection: 
db_UNIWFMGFXK_mp-b90e944b-9431-47d6-84b0-4716b32b1374.uni-id-users 
index: mobile_index dup key: { mobile: null }
```

## 问题原因

1. 数据库中存在多条mobile字段为null的记录
2. 索引配置可能有冲突
3. 数据初始化时产生了重复数据

## 解决方案

### 方案一：清理数据库（推荐）

1. **登录uniCloud控制台**
   - 打开 [uniCloud控制台](https://unicloud.dcloud.net.cn/)
   - 选择您的服务空间

2. **删除有问题的表**
   ```javascript
   // 在云数据库控制台执行
   db.collection('uni-id-users').remove({})
   ```

3. **重新创建索引**
   - 删除现有的mobile索引
   - 重新上传uni-id-users.index.json文件

4. **重新初始化数据**
   - 重新上传schema文件
   - 重新导入初始数据

### 方案二：修复现有数据

1. **查找重复数据**
   ```javascript
   // 在云数据库控制台执行
   db.collection('uni-id-users').aggregate([
     {
       $group: {
         _id: "$mobile",
         count: { $sum: 1 },
         docs: { $push: "$_id" }
       }
     },
     {
       $match: {
         count: { $gt: 1 }
       }
     }
   ])
   ```

2. **删除重复记录**
   ```javascript
   // 保留第一条，删除其他重复记录
   db.collection('uni-id-users').remove({
     mobile: null,
     _id: { $ne: "保留的记录ID" }
   })
   ```

3. **重建索引**
   ```javascript
   // 删除旧索引
   db.collection('uni-id-users').dropIndex("mobile")
   
   // 创建新索引
   db.collection('uni-id-users').createIndex(
     { "mobile": 1 }, 
     { "name": "mobile_index", "unique": false }
   )
   ```

### 方案三：完全重置项目数据库

如果是开发阶段，可以完全重置：

1. **备份重要数据**（如果有）

2. **删除所有表**
   - 在uniCloud控制台删除所有数据表

3. **重新上传schema文件**
   - 上传所有.schema.json文件
   - 上传所有.index.json文件

4. **重新导入初始数据**
   - 上传所有.init_data.json文件

## 预防措施

### 1. 修改mobile字段配置

在uni-id-users.schema.json中，确保mobile字段允许为空但不重复：

```json
"mobile": {
  "bsonType": "string",
  "description": "手机号码",
  "pattern": "^\\+?[0-9-]{3,20}$",
  "title": "手机号码",
  "trim": "both",
  "permission": {
    "read": "doc._id == auth.uid || 'READ_UNI_ID_USERS' in auth.permission",
    "write": "'CREATE_UNI_ID_USERS' in auth.permission || 'UPDATE_UNI_ID_USERS' in auth.permission"
  }
}
```

### 2. 创建稀疏索引

修改uni-id-users.index.json，使用稀疏索引：

```json
{
  "IndexName": "mobile_sparse_index",
  "MgoKeySchema": { 
    "MgoIndexKeys": [{ "Name": "mobile", "Direction": "1" }], 
    "MgoIsUnique": false,
    "MgoIsSparse": true
  }
}
```

### 3. 在云函数中处理空值

```javascript
// 在用户注册时确保mobile字段处理
const userInfo = {
  username: username,
  mobile: mobile || undefined, // 使用undefined而不是null
  // 其他字段...
}
```

## 操作步骤（推荐执行顺序）

### 第一步：备份数据
如果有重要数据，先导出备份。

### 第二步：清理数据库
1. 登录uniCloud控制台
2. 进入云数据库
3. 删除uni-id-users表（如果是开发环境）

### 第三步：重新配置
1. 修改uni-id-users.index.json文件（已完成）
2. 重新上传schema和index文件
3. 重新导入初始数据

### 第四步：测试
1. 尝试重新登录
2. 测试用户注册功能
3. 验证数据库操作正常

## 常见问题

**Q: 为什么会出现mobile为null的重复记录？**
A: 通常是因为用户注册时没有提供手机号，系统自动设置为null，多个null值导致索引冲突。

**Q: 如何避免这个问题？**
A: 使用稀疏索引(sparse index)，或者在应用层面确保mobile字段要么有值要么不存在。

**Q: 数据会丢失吗？**
A: 如果按照方案一操作，会清空用户表。如果是开发阶段，这通常是可以接受的。

## 联系支持

如果问题仍然存在，可以：
1. 查看uniCloud官方文档
2. 在DCloud社区寻求帮助
3. 检查uni-id插件的最新版本

记住：在生产环境中操作数据库前，一定要先备份数据！

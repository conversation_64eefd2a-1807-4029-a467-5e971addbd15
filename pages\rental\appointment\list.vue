<template>
	<view class="fix-top-window">
		<view class="uni-header">
			<uni-stat-breadcrumb class="uni-stat-breadcrumb-on-phone" />
			<view class="uni-group">
				<input class="uni-search" type="text" v-model="query" @confirm="search"
					placeholder="搜索预约人姓名、电话" />
				<button class="uni-button hide-on-phone" type="default" size="mini"
					@click="search">搜索</button>
				<uni-data-select v-model="statusFilter" :localdata="statusOptions" 
					placeholder="筛选状态" @change="filterByStatus"></uni-data-select>
			</view>
		</view>
		<view class="uni-container">
			<unicloud-db ref="udb" collection="rental-appointments,rental-houses,uni-id-users" 
				field="rental-appointments.*,rental-houses.title as house_title,uni-id-users.nickname as tenant_name"
				:where="where" page-data="replace" :orderby="orderby" :getcount="true" 
				:page-size="options.pageSize" :page-current="options.pageCurrent" 
				v-slot:default="{data,pagination,loading,error,options}" :options="options" 
				loadtime="manual" @load="onqueryload"
				foreign-key="rental-appointments.house_id=rental-houses._id,rental-appointments.tenant_id=uni-id-users._id">
				<uni-table ref="table" :loading="loading" :emptyText="error.message || '没有更多数据'"
					:border="true" :stripe="true" :sort="true" :hover="true">
					<uni-tr>
						<uni-th align="center">预约人</uni-th>
						<uni-th align="center">联系电话</uni-th>
						<uni-th align="center">房源标题</uni-th>
						<uni-th align="center" sortable @sort-change="sortChange($event,'appointment_date')">预约时间</uni-th>
						<uni-th align="center">状态</uni-th>
						<uni-th align="center">留言</uni-th>
						<uni-th align="center" sortable @sort-change="sortChange($event,'create_date')">申请时间</uni-th>
						<uni-th width="200" align="center">操作</uni-th>
					</uni-tr>
					<uni-tr v-for="(item,index) in data" :key="index">
						<uni-td align="center">
							<view>{{ item.contact_name || item.tenant_name }}</view>
						</uni-td>
						<uni-td align="center">{{ item.contact_phone }}</uni-td>
						<uni-td>
							<view class="house-title">{{ item.house_title }}</view>
						</uni-td>
						<uni-td align="center">
							<uni-dateformat :date="item.appointment_date" format="MM-dd hh:mm"></uni-dateformat>
						</uni-td>
						<uni-td align="center">
							<uni-tag :text="item.status" :type="getStatusType(item.status)"></uni-tag>
						</uni-td>
						<uni-td>
							<view class="message">{{ item.message || '-' }}</view>
						</uni-td>
						<uni-td align="center">
							<uni-dateformat :date="item.create_date" format="MM-dd hh:mm"></uni-dateformat>
						</uni-td>
						<uni-td align="center">
							<view class="uni-group">
								<button @click="confirmAppointment(item)" class="uni-button" 
									size="mini" type="primary" v-if="item.status === '待确认'">确认</button>
								<button @click="rejectAppointment(item)" class="uni-button" 
									size="mini" type="warn" v-if="item.status === '待确认'">拒绝</button>
								<button @click="completeAppointment(item)" class="uni-button" 
									size="mini" type="default" v-if="item.status === '已确认'">完成</button>
								<button @click="viewDetail(item)" class="uni-button" 
									size="mini" type="default">详情</button>
							</view>
						</uni-td>
					</uni-tr>
				</uni-table>
				<view class="uni-pagination-box">
					<uni-pagination show-icon :page-size="pagination.size" v-model="pagination.current"
						:total="pagination.count" @change="onPageChanged" />
				</view>
			</unicloud-db>
		</view>
	</view>
</template>

<script>
	const db = uniCloud.database()
	const dbCollectionName = 'rental-appointments'

	export default {
		data() {
			return {
				query: '',
				statusFilter: '',
				where: '',
				orderby: 'create_date desc',
				options: {
					pageSize: 20,
					pageCurrent: 1
				},
				statusOptions: [
					{value: '', text: '全部状态'},
					{value: '待确认', text: '待确认'},
					{value: '已确认', text: '已确认'},
					{value: '已完成', text: '已完成'},
					{value: '已取消', text: '已取消'},
					{value: '已拒绝', text: '已拒绝'}
				]
			}
		},
		onReady() {
			this.$refs.udb.loadData()
		},
		methods: {
			getStatusType(status) {
				const statusMap = {
					'待确认': 'warning',
					'已确认': 'primary',
					'已完成': 'success',
					'已取消': 'info',
					'已拒绝': 'error'
				}
				return statusMap[status] || 'default'
			},
			search() {
				let newWhere = ''
				if (this.query) {
					newWhere = `contact_name.indexOf("${this.query}") > -1 || contact_phone.indexOf("${this.query}") > -1`
				}
				if (this.statusFilter) {
					const statusWhere = `status == "${this.statusFilter}"`
					newWhere = newWhere ? `(${newWhere}) && ${statusWhere}` : statusWhere
				}
				this.where = newWhere
				this.$nextTick(() => {
					this.$refs.udb.loadData({
						clear: true
					})
				})
			},
			filterByStatus() {
				this.search()
			},
			onqueryload(data) {
				// 数据加载完成
			},
			onPageChanged(e) {
				this.$refs.udb.loadData({
					current: e.current
				})
			},
			sortChange(e, name) {
				this.orderby = name + ' ' + e.order
				this.$refs.udb.loadData()
			},
			confirmAppointment(item) {
				uni.showModal({
					title: '确认预约',
					content: '确认接受此预约申请吗？',
					success: (res) => {
						if (res.confirm) {
							this.updateAppointmentStatus(item._id, '已确认')
						}
					}
				})
			},
			rejectAppointment(item) {
				uni.showModal({
					title: '拒绝预约',
					content: '确认拒绝此预约申请吗？',
					success: (res) => {
						if (res.confirm) {
							this.updateAppointmentStatus(item._id, '已拒绝')
						}
					}
				})
			},
			completeAppointment(item) {
				uni.showModal({
					title: '完成预约',
					content: '确认此预约已完成看房吗？',
					success: (res) => {
						if (res.confirm) {
							this.updateAppointmentStatus(item._id, '已完成', {
								complete_date: new Date()
							})
						}
					}
				})
			},
			updateAppointmentStatus(id, status, extraData = {}) {
				const updateData = {
					status: status,
					update_date: new Date(),
					...extraData
				}
				
				if (status === '已确认') {
					updateData.confirm_date = new Date()
				}
				
				db.collection(dbCollectionName).doc(id).update(updateData).then(() => {
					uni.showToast({
						title: '操作成功'
					})
					this.$refs.udb.loadData()
				}).catch(err => {
					uni.showModal({
						content: err.message || '操作失败',
						showCancel: false
					})
				})
			},
			viewDetail(item) {
				uni.navigateTo({
					url: `./detail?id=${item._id}`
				})
			}
		}
	}
</script>

<style lang="scss">
	.house-title {
		max-width: 200px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
	
	.message {
		max-width: 150px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
</style>

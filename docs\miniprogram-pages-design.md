# 租房小程序页面设计方案

## 1. 页面结构总览

### 1.1 主要页面分类
- **首页模块**: 首页、搜索页
- **房源模块**: 房源列表、房源详情、地图找房
- **用户模块**: 个人中心、我的收藏、我的预约、消息中心
- **功能模块**: 发布房源、预约看房、在线咨询

### 1.2 页面导航结构
```
├── 首页 (index)
├── 房源列表 (house-list)
├── 房源详情 (house-detail)
├── 地图找房 (map-search)
├── 搜索页面 (search)
├── 个人中心 (profile)
├── 我的收藏 (favorites)
├── 我的预约 (appointments)
├── 消息中心 (messages)
├── 发布房源 (publish)
└── 预约看房 (appointment)
```

## 2. 详细页面设计

### 2.1 首页 (pages/index/index)
**功能描述**: 小程序入口页面，展示推荐房源和快速搜索功能

**页面元素**:
- 顶部搜索栏 (城市选择 + 搜索框)
- 轮播图广告位
- 快速筛选标签 (整租、合租、短租等)
- 推荐房源列表
- 底部导航栏

**主要功能**:
- 城市定位和切换
- 关键词搜索
- 房源快速筛选
- 推荐房源展示
- 页面跳转导航

### 2.2 房源列表页 (pages/house/list)
**功能描述**: 展示符合条件的房源列表，支持筛选和排序

**页面元素**:
- 顶部搜索栏
- 筛选条件栏 (价格、房型、区域等)
- 排序选项 (价格、时间、距离等)
- 房源卡片列表
- 地图/列表切换按钮
- 上拉加载更多

**主要功能**:
- 多条件筛选
- 排序功能
- 收藏房源
- 快速预约
- 地图模式切换

### 2.3 房源详情页 (pages/house/detail)
**功能描述**: 展示房源的详细信息，支持预约看房和收藏

**页面元素**:
- 房源图片轮播
- 基本信息 (价格、房型、面积等)
- 详细描述
- 配套设施标签
- 地理位置地图
- 房东信息
- 相似推荐
- 底部操作栏 (收藏、咨询、预约)

**主要功能**:
- 图片预览
- 收藏/取消收藏
- 预约看房
- 在线咨询
- 分享房源
- 举报功能

### 2.4 地图找房页 (pages/map/index)
**功能描述**: 基于地图的房源搜索和展示

**页面元素**:
- 全屏地图
- 房源标记点
- 筛选浮层
- 房源信息卡片
- 定位按钮
- 搜索框

**主要功能**:
- 地图房源标记
- 区域房源聚合
- 地图筛选
- 房源信息预览
- 位置定位

### 2.5 搜索页面 (pages/search/index)
**功能描述**: 高级搜索功能，支持多维度筛选

**页面元素**:
- 搜索输入框
- 历史搜索记录
- 热门搜索标签
- 筛选条件面板
- 搜索结果列表

**主要功能**:
- 关键词搜索
- 历史记录管理
- 高级筛选
- 搜索建议
- 结果排序

### 2.6 个人中心 (pages/profile/index)
**功能描述**: 用户个人信息和功能入口

**页面元素**:
- 用户头像和基本信息
- 功能菜单列表
  - 我的收藏
  - 我的预约
  - 我的发布
  - 消息中心
  - 设置中心
- 客服联系方式

**主要功能**:
- 用户信息展示
- 功能模块导航
- 登录/注册
- 设置管理

### 2.7 我的收藏 (pages/favorites/index)
**功能描述**: 展示用户收藏的房源列表

**页面元素**:
- 收藏房源列表
- 批量管理按钮
- 空状态提示

**主要功能**:
- 收藏列表展示
- 取消收藏
- 批量删除
- 快速预约

### 2.8 我的预约 (pages/appointments/index)
**功能描述**: 展示用户的看房预约记录

**页面元素**:
- 预约状态标签
- 预约记录列表
- 预约详情
- 操作按钮

**主要功能**:
- 预约记录查看
- 预约状态跟踪
- 取消预约
- 联系房东

### 2.9 消息中心 (pages/messages/index)
**功能描述**: 展示系统消息和聊天记录

**页面元素**:
- 消息分类标签
- 消息列表
- 未读消息提示
- 消息详情

**主要功能**:
- 消息分类展示
- 消息已读/未读
- 消息删除
- 消息回复

### 2.10 发布房源 (pages/publish/index)
**功能描述**: 房东发布房源信息

**页面元素**:
- 房源信息表单
- 图片上传组件
- 地址选择器
- 设施选择器
- 提交按钮

**主要功能**:
- 房源信息填写
- 图片上传
- 地址定位
- 表单验证
- 发布提交

### 2.11 预约看房 (pages/appointment/index)
**功能描述**: 预约看房表单页面

**页面元素**:
- 房源基本信息
- 预约时间选择
- 联系信息填写
- 留言输入框
- 提交按钮

**主要功能**:
- 时间选择
- 信息填写
- 表单验证
- 预约提交

## 3. 底部导航栏设计

### 3.1 导航结构
```
├── 首页 (index)
├── 找房 (house-list)
├── 地图 (map)
├── 消息 (messages)
└── 我的 (profile)
```

### 3.2 导航图标和文字
- 首页: home图标 + "首页"
- 找房: search图标 + "找房"
- 地图: location图标 + "地图"
- 消息: message图标 + "消息" (显示未读数量)
- 我的: user图标 + "我的"

## 4. 页面跳转关系

### 4.1 主要跳转路径
```
首页 → 房源列表 → 房源详情 → 预约看房
首页 → 搜索页面 → 房源列表
地图找房 → 房源详情
个人中心 → 我的收藏 → 房源详情
个人中心 → 我的预约 → 预约详情
```

### 4.2 返回机制
- 使用uni-app的页面栈管理
- 支持手势返回
- 关键页面支持返回首页

## 5. 响应式设计考虑

### 5.1 屏幕适配
- 支持不同尺寸手机屏幕
- 使用rpx单位进行适配
- 考虑安全区域适配

### 5.2 交互优化
- 触摸友好的按钮尺寸
- 合理的间距设计
- 流畅的动画效果
- 加载状态提示

## 6. 性能优化策略

### 6.1 页面加载优化
- 图片懒加载
- 分页加载数据
- 缓存常用数据
- 预加载关键页面

### 6.2 用户体验优化
- 骨架屏加载
- 下拉刷新
- 上拉加载更多
- 网络异常处理

这个设计方案为租房小程序提供了完整的页面结构和功能规划，确保用户能够便捷地浏览房源、预约看房和管理个人信息。

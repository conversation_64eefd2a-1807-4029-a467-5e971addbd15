{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/login/login-withpwd.vue?dde3", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/login/login-withpwd.vue?a08d", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/login/login-withpwd.vue?5922", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/login/login-withpwd.vue?440b", "uni-app:///uni_modules/uni-id-pages/pages/login/login-withpwd.vue", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/login/login-withpwd.vue?e5cb", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/login/login-withpwd.vue?bc64"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "errorOptions", "type", "mixins", "data", "onShow", "methods", "toRetrievePwd", "url", "uni", "pwd<PERSON><PERSON><PERSON>", "title", "icon", "duration", "uniIdCo", "toRegister", "fail", "console"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AAC+K;AAC/K,gBAAgB,gLAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,8RAEN;AACP,KAAK;AACL;AACA,aAAa,oSAEN;AACP,KAAK;AACL;AACA,aAAa,kRAEN;AACP,KAAK;AACL;AACA,aAAa,6VAEN;AACP,KAAK;AACL;AACA,aAAa,uVAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClEA;AAAA;AAAA;AAAA;AAAsqB,CAAgB,soBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACqC1rB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;EACAC;IACAC;EACA;AACA;AAAA,eACA;EACAC;EACAC;IACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;EACA;EACAC,2BASA;EACAC;IACA;IACAC;MACA;MACA;MACA;QACAC;MACA;MACAC;QACAD;MACA;IACA;IACA;AACA;AACA;IACAE;MAAA;MACA;QACA;QACA;UACAC;UACAC;UACAC;QACA;MACA;MACA;QACA;QACA;UACAF;UACAC;UACAC;QACA;MACA;MACA;QACA;QACA;UACAF;UACAC;UACAC;QACA;MACA;MAEA;QACA;MACA;MAEA;QACA;QACA;MACA;MAEA;QACAT;MACA;QACAA;MACA;QACAA;MACA;MAEAU;QACA;MACA;QACA;UACA;QACA;UACA;UACA;QACA;MACA;IACA;IACA,UACAC;MACAN;QACAD,uFACA;QACAQ;UACAC;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClJA;AAAA;AAAA;AAAA;AAAyxC,CAAgB,ypCAAG,EAAC,C;;;;;;;;;;;ACA7yC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-id-pages/pages/login/login-withpwd.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './uni_modules/uni-id-pages/pages/login/login-withpwd.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./login-withpwd.vue?vue&type=template&id=9dec0b32&scoped=true&\"\nvar renderjs\nimport script from \"./login-withpwd.vue?vue&type=script&lang=js&\"\nexport * from \"./login-withpwd.vue?vue&type=script&lang=js&\"\nimport style0 from \"./login-withpwd.vue?vue&type=style&index=0&id=9dec0b32&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"9dec0b32\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-id-pages/pages/login/login-withpwd.vue\"\nexport default component.exports", "export * from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login-withpwd.vue?vue&type=template&id=9dec0b32&scoped=true&\"", "var components\ntry {\n  components = {\n    uniForms: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-forms/components/uni-forms/uni-forms\" */ \"@/uni_modules/uni-forms/components/uni-forms/uni-forms.vue\"\n      )\n    },\n    uniFormsItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-forms/components/uni-forms-item/uni-forms-item\" */ \"@/uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.vue\"\n      )\n    },\n    uniEasyinput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput\" */ \"@/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue\"\n      )\n    },\n    uniCaptcha: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-captcha/components/uni-captcha/uni-captcha\" */ \"@/uni_modules/uni-captcha/components/uni-captcha/uni-captcha.vue\"\n      )\n    },\n    uniIdPagesAgreements: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-id-pages/components/uni-id-pages-agreements/uni-id-pages-agreements\" */ \"@/uni_modules/uni-id-pages/components/uni-id-pages-agreements/uni-id-pages-agreements.vue\"\n      )\n    },\n    uniIdPagesFabLogin: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-id-pages/components/uni-id-pages-fab-login/uni-id-pages-fab-login\" */ \"@/uni_modules/uni-id-pages/components/uni-id-pages-fab-login/uni-id-pages-fab-login.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.focusUsername = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.focusPassword = false\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login-withpwd.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login-withpwd.vue?vue&type=script&lang=js&\"", "<!-- 账号密码登录页 -->\r\n<template>\r\n\t<view class=\"uni-content\">\r\n\t\t<view class=\"login-logo\">\r\n\t\t\t<image :src=\"logo\"></image>\r\n\t\t</view>\r\n\t\t<!-- 顶部文字 -->\r\n\t\t<text class=\"title title-box\">账号密码登录</text>\r\n\t\t<uni-forms>\r\n\t\t\t<uni-forms-item name=\"username\">\r\n\t\t\t\t<uni-easyinput :focus=\"focusUsername\" @blur=\"focusUsername = false\" class=\"input-box\"\r\n\t\t\t\t\t:inputBorder=\"false\" v-model=\"username\" placeholder=\"请输入手机号/用户名/邮箱\" />\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-forms-item name=\"password\">\r\n\t\t\t\t<uni-easyinput :focus=\"focusPassword\" @blur=\"focusPassword = false\" class=\"input-box\" clearable\r\n\t\t\t\t\ttype=\"password\" :inputBorder=\"false\" v-model=\"password\" placeholder=\"请输入密码\" />\r\n\t\t\t</uni-forms-item>\r\n\t\t</uni-forms>\r\n\t\t<uni-captcha v-if=\"needCaptcha\" focus ref=\"captcha\" scene=\"login-by-pwd\" v-model=\"captcha\" />\r\n\t\t<!-- 带选择框的隐私政策协议组件 -->\r\n\t\t<uni-id-pages-agreements scope=\"login\" ref=\"agreements\"></uni-id-pages-agreements>\r\n\t\t<button class=\"uni-btn\" type=\"primary\" @click=\"pwdLogin\">登录</button>\r\n\t\t<!-- 忘记密码 -->\r\n\t\t<view class=\"link-box\">\r\n\t\t\t<view v-if=\"!config.isAdmin\">\r\n\t\t\t\t<text class=\"forget\">忘记了？</text>\r\n\t\t\t\t<text class=\"link\" @click=\"toRetrievePwd\">找回密码</text>\r\n\t\t\t</view>\r\n\t\t\t<text class=\"link\" @click=\"toRegister\">{{config.isAdmin ? '注册管理员账号': '注册账号'}}</text>\r\n\t\t\t<!-- <text class=\"link\" @click=\"toRegister\" v-if=\"!config.isAdmin\">注册账号</text> -->\r\n\t\t</view>\r\n\t\t<!-- 悬浮登录方式组件 -->\r\n\t\t<uni-id-pages-fab-login ref=\"uniFabLogin\"></uni-id-pages-fab-login>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport mixin from '@/uni_modules/uni-id-pages/common/login-page.mixin.js';\r\n\tconst uniIdCo = uniCloud.importObject(\"uni-id-co\", {\r\n\t\terrorOptions: {\r\n\t\t\ttype: 'toast'\r\n\t\t}\r\n\t})\r\n\texport default {\r\n\t\tmixins: [mixin],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t\"password\": \"\",\r\n\t\t\t\t\"username\": \"\",\r\n\t\t\t\t\"captcha\": \"\",\r\n\t\t\t\t\"needCaptcha\": false,\r\n\t\t\t\t\"focusUsername\": false,\r\n\t\t\t\t\"focusPassword\": false,\r\n\t\t\t\t\"logo\": \"/static/logo.png\"\r\n\t\t\t}\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\t// #ifdef H5\r\n\t\t\tdocument.onkeydown = event => {\r\n\t\t\t\tvar e = event || window.event;\r\n\t\t\t\tif (e && e.keyCode == 13) { //回车键的键值为13\r\n\t\t\t\t\tthis.pwdLogin()\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 页面跳转，找回密码\r\n\t\t\ttoRetrievePwd() {\r\n\t\t\t\tlet url = '/uni_modules/uni-id-pages/pages/retrieve/retrieve'\r\n\t\t\t\t//如果刚好用户名输入框的值为手机号码，就把它传到retrieve页面，根据该手机号找回密码\r\n\t\t\t\tif (/^1\\d{10}$/.test(this.username)) {\r\n\t\t\t\t\turl += `?phoneNumber=${this.username}`\r\n\t\t\t\t}\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 密码登录\r\n\t\t\t */\r\n\t\t\tpwdLogin() {\r\n\t\t\t\tif (!this.password.length) {\r\n\t\t\t\t\tthis.focusPassword = true\r\n\t\t\t\t\treturn uni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入密码',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 3000\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\tif (!this.username.length) {\r\n\t\t\t\t\tthis.focusUsername = true\r\n\t\t\t\t\treturn uni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入手机号/用户名/邮箱',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 3000\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\tif (this.needCaptcha && this.captcha.length != 4) {\r\n\t\t\t\t\tthis.$refs.captcha.getImageCaptcha()\r\n\t\t\t\t\treturn uni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入验证码',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 3000\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (this.needAgreements && !this.agree) {\r\n\t\t\t\t\treturn this.$refs.agreements.popup(this.pwdLogin)\r\n\t\t\t\t}\r\n\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\t\"password\": this.password,\r\n\t\t\t\t\t\"captcha\": this.captcha\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (/^1\\d{10}$/.test(this.username)) {\r\n\t\t\t\t\tdata.mobile = this.username\r\n\t\t\t\t} else if (/@/.test(this.username)) {\r\n\t\t\t\t\tdata.email = this.username\r\n\t\t\t\t} else {\r\n\t\t\t\t\tdata.username = this.username\r\n\t\t\t\t}\r\n\r\n\t\t\t\tuniIdCo.login(data).then(e => {\r\n\t\t\t\t\tthis.loginSuccess(e)\r\n\t\t\t\t}).catch(e => {\r\n\t\t\t\t\tif (e.errCode == 'uni-id-captcha-required') {\r\n\t\t\t\t\t\tthis.needCaptcha = true\r\n\t\t\t\t\t} else if (this.needCaptcha) {\r\n\t\t\t\t\t\t//登录失败，自动重新获取验证码\r\n\t\t\t\t\t\tthis.$refs.captcha.getImageCaptcha()\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t/* 前往注册 */\r\n\t\t\ttoRegister() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: this.config.isAdmin ? '/uni_modules/uni-id-pages/pages/register/register-admin' :\r\n\t\t\t\t\t\t'/uni_modules/uni-id-pages/pages/register/register',\r\n\t\t\t\t\tfail(e) {\r\n\t\t\t\t\t\tconsole.error(e);\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import \"@/uni_modules/uni-id-pages/common/login-page.scss\";\r\n\r\n\t@media screen and (min-width: 690px) {\r\n\t\t.uni-content {\r\n\t\t\theight: auto;\r\n\t\t}\r\n\t}\r\n\r\n\t.forget {\r\n\t\tfont-size: 12px;\r\n\t\tcolor: #8a8f8b;\r\n\t}\r\n\r\n\t.link-box {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex-direction: row;\r\n\t\tjustify-content: space-between;\r\n\t\tmargin-top: 20px;\r\n\t}\r\n\r\n\t.link {\r\n\t\tfont-size: 12px;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login-withpwd.vue?vue&type=style&index=0&id=9dec0b32&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login-withpwd.vue?vue&type=style&index=0&id=9dec0b32&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753757591950\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
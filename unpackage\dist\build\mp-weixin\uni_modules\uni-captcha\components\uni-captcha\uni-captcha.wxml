<view class="captcha-box data-v-3e5b7276"><view class="captcha-img-box data-v-3e5b7276"><block wx:if="{{loging}}"><uni-icons class="loding data-v-3e5b7276" vue-id="68d974fd-1" size="20px" color="#BBB" type="spinner-cycle" bind:__l="__l"></uni-icons></block><image class="{{['captcha-img','data-v-3e5b7276',(loging)?'opacity':'']}}" src="{{captchaBase64}}" mode="widthFix" data-event-opts="{{[['tap',[['getImageCaptcha',['$event']]]]]}}" bindtap="__e"></image></view><input class="captcha data-v-3e5b7276" focus="{{focusCaptchaInput}}" type="text" inputBorder="{{false}}" maxlength="4" placeholder="请输入验证码" data-event-opts="{{[['blur',[['e0',['$event']]]],['input',[['__set_model',['','val','$event',[]]]]]]}}" value="{{val}}" bindblur="__e" bindinput="__e"/></view>
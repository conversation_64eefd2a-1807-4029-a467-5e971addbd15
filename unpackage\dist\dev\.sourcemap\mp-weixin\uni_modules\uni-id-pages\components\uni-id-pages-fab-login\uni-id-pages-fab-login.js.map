{"version": 3, "sources": ["webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/components/uni-id-pages-fab-login/uni-id-pages-fab-login.vue?b344", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/components/uni-id-pages-fab-login/uni-id-pages-fab-login.vue?2790", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/components/uni-id-pages-fab-login/uni-id-pages-fab-login.vue?3292", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/components/uni-id-pages-fab-login/uni-id-pages-fab-login.vue?1982", "uni-app:///uni_modules/uni-id-pages/components/uni-id-pages-fab-login/uni-id-pages-fab-login.vue", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/components/uni-id-pages-fab-login/uni-id-pages-fab-login.vue?377d", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/components/uni-id-pages-fab-login/uni-id-pages-fab-login.vue?7272"], "names": ["computed", "agreements", "config", "serviceUrl", "privacyUrl", "url", "title", "agree", "get", "set", "data", "servicesList", "univerifyStyle", "watch", "created", "loginTypes", "id", "logo", "path", "methods", "getParentComponent", "setUserInfo", "console", "getRoute", "toPage", "uni", "animationType", "complete", "login_before", "navigateBack", "options", "icon", "duration", "needAgreements", "type", "agreementsRef", "mask", "closeUniverify", "univerifyManager", "clickAnotherButtons", "onButtonsClickFn", "checkBoxState", "setTimeout", "success", "fail", "phoneCode", "provider", "res", "Object", "code", "login", "params", "customUI", "uniIdCo", "mutations", "catch", "content", "confirmText", "showCancel", "finally", "getUserInfo", "e", "resolve", "reject"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC0E;AACL;AACc;;;AAGnF;AAC+K;AAC/K,gBAAgB,gLAAU;AAC1B,EAAE,4FAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAA+qB,CAAgB,+oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACYnsB;AAEA;AAAA;AAAA;AACA;AAAA,eACA;EACAA;IACAC;MACA;QACA;MACA;MACA,yBAGAC;QAFAC;QACAC;MAEA;QACAC;QACAC;MACA,GACA;QACAD;QACAC;MACA,EACA;IACA;IACAC;MACAC;QACA;MACA;MACAC;QACA;MACA;IACA;EACA;EACAC;IACA;MACAC;QACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;MACA,EAgDA;MACAC;QAAA;QACA;QAAA;QACA;QAAA;QACA;UAAA;UACA;UAAA;UACA;QACA;QACA;UACA;UAAA;UACA;UAAA;UACA;UAAA;UACA;UAAA;UACA;UAAA;UACA;QACA;MACA;IACA;EACA;EACAC;IACAN;MACA;IACA;EACA;EACAO;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cACAH;cACAI;cAEAJ;gBAGA;gBACA;kBACA;gBACA;gBAUA;cACA;cACA;cACA;gBACA;gBACA;gBACAA,qCAIA;kBAAA,IAHAK;oBACAC;oBACAC;kBAEA;oBACA;sBACA;sBACA;sBACAA;oBACA;kBACA;gBACA;cACA;cACA;;cAEA;cACA;gBACA;gBACA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAC;IACAC;MAEA;IAMA;IACAC;MACAC;IACA;IACAC;MAAA;MACA;MACA;QACA;MACA;MACA;IACA;IACAC;MAAA;MACA;MACA,kEACA;QACA;QACA;QACAC;MACA;QAAA;QACAA;MACA;QACA;UACAA;YACApB;YACAqB;YACAC;cACA;YAAA;UAEA;QACA;UACAF;YACApB;YACAqB;YACAC;cACA;YAAA;UAEA;QACA;MACA;QACAL;MACA;IACA;IACAM;MAAA;QAAA;MAAA;QAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAAC;gBACAR;gBACA;gBAAA,KACA,OACA,UACA,aACA,UACA,YACA,UACA,UACA,SACA;kBAAA;kBAAA;gBAAA;gBAAA,kCACAG;kBACAnB;kBACAyB;kBACAC;gBACA;cAAA;gBAAA;gBA8BA;;gBAEA;kBAAA;kBAAA;gBAAA;gBAAA,kCAIAP;kBACAnB;kBACAyB;kBACAC;gBACA;cAAA;gBAGA;gBACAC;gBAAA,MACAC;kBAAA;kBAAA;gBAAA;gBACAC;gBAAA,kCACAA;kBACA;gBACA;cAAA;gBA8CAV;kBACAW;gBACA;gBAAA,MAEAF;kBAAA;kBAAA;gBAAA;gBAuCAG;kBACAZ;kBACAa;kBACA;kBACAA;gBACA,GACA;gBA5CAA;gBACAC;gBACAC;kBAAA;oBAAA;oBAAA;sBAAA;wBAAA;0BAAA;4BACAlB;4BACAiB;4BAAA;4BAAA,OACAd;0BAAA;4BAAAgB;4BACA;;4BAEA;4BAMAvB,OACA,8CADAA;4BAEA;8BACA;gCACA;8BACA;8BACA;8BACAmB;4BACA;8BACA;gCACAA;gCACAK;kCACA;gCACA;8BACA;gCACAjB;kCACAnB;kCACAyB;kCACAC;gCACA;8BACA;4BACA;0BAAA;0BAAA;4BAAA;wBAAA;sBAAA;oBAAA;kBAAA,CACA;kBAAA,gBAlCAQ;oBAAA;kBAAA;gBAAA;gBA2CAF;gBACA;gBAAA,kCACAA;kBACA;kBACAK;oBACA;kBACA;kBACAC;oBACAtB;oBACA;sBACAG;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;kBACA;;kBACAE;oBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BACAF;8BACA;8BACA;8BACA;8BACAa;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CACA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;gBAAA,MAGAJ;kBAAA;kBAAA;gBAAA;gBAAA,kCACA;kBACAW;gBACA;cAAA;gBAGApB;kBACA;kBACA;kBAIAkB;oBAAA;sBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA,MACAT;gCAAA;gCAAA;8BAAA;8BAAA;8BAAA,OACA;gCACAY;8BACA;4BAAA;8BAFAC;8BAGAC;8BACAvB;4BAAA;8BAEA;gCACAwB;8BACA;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CACA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;kBACAL;oBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BACAtB;8BACAG;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CACA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAyB;MAAA;MACA;MACA5B;QAAA6B;QAAAjB;MAAA;MACA;MACA;MACA;QACAkB;MACA;MACAC;QACA5B;UACAnB;UACAyB;UACAC;QACA;QAIAsB;MACA,GACAC;QACA9B;UACA+B;UACAC;UACAC;QACA;MACA,GACAC;QACA;UACAlC;QACA;QACAA;MACA;IACA;IACAmC;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,kCACA;kBACAnC,gDACAoC;oBACAlB;sBACAmB;oBACA;oBACAlB;sBACAnB;wBACA+B;wBACAE;sBACA;sBACAK;oBACA;kBAAA,GACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxfA;AAAA;AAAA;AAAA;AAA0wC,CAAgB,0oCAAG,EAAC,C;;;;;;;;;;;ACA9xC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-id-pages/components/uni-id-pages-fab-login/uni-id-pages-fab-login.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-id-pages-fab-login.vue?vue&type=template&id=5810e2be&\"\nvar renderjs\nimport script from \"./uni-id-pages-fab-login.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-id-pages-fab-login.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-id-pages-fab-login.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-id-pages/components/uni-id-pages-fab-login/uni-id-pages-fab-login.vue\"\nexport default component.exports", "export * from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-id-pages-fab-login.vue?vue&type=template&id=5810e2be&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, item) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        item = _temp2.item\n      var _temp, _temp2\n      item.path ? _vm.toPage(item.path) : _vm.login_before(item.id, false)\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-id-pages-fab-login.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-id-pages-fab-login.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class=\"fab-login-box\">\r\n\t\t\t<view class=\"item\" v-for=\"(item,index) in servicesList\" :key=\"index\"\r\n\t\t\t\t@click=\"item.path?toPage(item.path):login_before(item.id,false)\">\r\n\t\t\t\t<image class=\"logo\" :src=\"item.logo\" mode=\"scaleToFill\"></image>\r\n\t\t\t\t<text class=\"login-title\">{{item.text}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n<script>\r\n\timport config from '@/uni_modules/uni-id-pages/config.js'\r\n\t//前一个窗口的页面地址。控制点击切换快捷登录方式是创建还是返回\r\n\timport {store,mutations} from '@/uni_modules/uni-id-pages/common/store.js'\r\n\tlet allServicesList = []\r\n\texport default {\r\n\t\tcomputed: {\r\n\t\t\tagreements() {\r\n\t\t\t\tif (!config.agreements) {\r\n\t\t\t\t\treturn []\r\n\t\t\t\t}\r\n\t\t\t\tlet {\r\n\t\t\t\t\tserviceUrl,\r\n\t\t\t\t\tprivacyUrl\r\n\t\t\t\t} = config.agreements\r\n\t\t\t\treturn [{\r\n\t\t\t\t\t\turl: serviceUrl,\r\n\t\t\t\t\t\ttitle: \"用户服务协议\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\turl: privacyUrl,\r\n\t\t\t\t\t\ttitle: \"隐私政策条款\"\r\n\t\t\t\t\t}\r\n\t\t\t\t]\r\n\t\t\t},\r\n\t\t\tagree: {\r\n\t\t\t\tget() {\r\n\t\t\t\t\treturn this.getParentComponent().agree\r\n\t\t\t\t},\r\n\t\t\t\tset(agree) {\r\n\t\t\t\t\treturn this.getParentComponent().agree = agree\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tservicesList: [{\r\n\t\t\t\t\t\t\"id\": \"username\",\r\n\t\t\t\t\t\t\"text\": \"账号登录\",\r\n\t\t\t\t\t\t\"logo\": \"/uni_modules/uni-id-pages/static/login/uni-fab-login/user.png\",\r\n\t\t\t\t\t\t\"path\": \"/uni_modules/uni-id-pages/pages/login/login-withpwd\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t\"id\": \"smsCode\",\r\n\t\t\t\t\t\t\"text\": \"短信验证码\",\r\n\t\t\t\t\t\t\"logo\": \"/uni_modules/uni-id-pages/static/login/uni-fab-login/sms.png\",\r\n\t\t\t\t\t\t\"path\": \"/uni_modules/uni-id-pages/pages/login/login-withoutpwd?type=smsCode\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t\"id\": \"weixin\",\r\n\t\t\t\t\t\t\"text\": \"微信登录\",\r\n\t\t\t\t\t\t\"logo\": \"/uni_modules/uni-id-pages/static/login/uni-fab-login/weixin.png\",\r\n\t\t\t\t\t},\r\n\t\t\t\t\t// #ifndef MP-WEIXIN\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t\"id\": \"apple\",\r\n\t\t\t\t\t\t\"text\": \"苹果登录\",\r\n\t\t\t\t\t\t\"logo\": \"/uni_modules/uni-id-pages/static/app-plus/uni-fab-login/apple.png\",\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t\"id\": \"univerify\",\r\n\t\t\t\t\t\t\"text\": \"一键登录\",\r\n\t\t\t\t\t\t\"logo\": \"/uni_modules/uni-id-pages/static/app-plus/uni-fab-login/univerify.png\",\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t\"id\": \"taobao\",\r\n\t\t\t\t\t\t\"text\": \"淘宝登录\", //暂未提供该登录方式的接口示例\r\n\t\t\t\t\t\t\"logo\": \"/uni_modules/uni-id-pages/static/app-plus/uni-fab-login/taobao.png\",\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t\"id\": \"facebook\",\r\n\t\t\t\t\t\t\"text\": \"脸书登录\", //暂未提供该登录方式的接口示例\r\n\t\t\t\t\t\t\"logo\": \"/uni_modules/uni-id-pages/static/app-plus/uni-fab-login/facebook.png\",\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t\"id\": \"alipay\",\r\n\t\t\t\t\t\t\"text\": \"支付宝登录\", //暂未提供该登录方式的接口示例\r\n\t\t\t\t\t\t\"logo\": \"/uni_modules/uni-id-pages/static/app-plus/uni-fab-login/alipay.png\",\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t\"id\": \"qq\",\r\n\t\t\t\t\t\t\"text\": \"QQ登录\", //暂未提供该登录方式的接口示例\r\n\t\t\t\t\t\t\"logo\": \"/uni_modules/uni-id-pages/static/app-plus/uni-fab-login/qq.png\",\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t\"id\": \"google\",\r\n\t\t\t\t\t\t\"text\": \"谷歌登录\", //暂未提供该登录方式的接口示例\r\n\t\t\t\t\t\t\"logo\": \"/uni_modules/uni-id-pages/static/app-plus/uni-fab-login/google.png\",\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t\"id\": \"douyin\",\r\n\t\t\t\t\t\t\"text\": \"抖音登录\", //暂未提供该登录方式的接口示例\r\n\t\t\t\t\t\t\"logo\": \"/uni_modules/uni-id-pages/static/app-plus/uni-fab-login/douyin.png\",\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t\"id\": \"sinaweibo\",\r\n\t\t\t\t\t\t\"text\": \"新浪微博\", //暂未提供该登录方式的接口示例\r\n\t\t\t\t\t\t\"logo\": \"/uni_modules/uni-id-pages/static/app-plus/uni-fab-login/sinaweibo.png\",\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t],\r\n\t\t\t\tuniverifyStyle: { //一键登录弹出窗的样式配置参数\r\n\t\t\t\t\t\"fullScreen\": true, // 是否全屏显示，true表示全屏模式，false表示非全屏模式，默认值为false。\r\n\t\t\t\t\t\"backgroundColor\": \"#ffffff\", // 授权页面背景颜色，默认值：#ffffff\r\n\t\t\t\t\t\"buttons\": { // 自定义登录按钮\r\n\t\t\t\t\t\t\"iconWidth\": \"45px\", // 图标宽度（高度等比例缩放） 默认值：45px\r\n\t\t\t\t\t\t\"list\": []\r\n\t\t\t\t\t},\r\n\t\t\t\t\t\"privacyTerms\": {\r\n\t\t\t\t\t\t\"defaultCheckBoxState\": false, // 条款勾选框初始状态 默认值： true\r\n\t\t\t\t\t\t\"textColor\": \"#BBBBBB\", // 文字颜色 默认值：#BBBBBB\r\n\t\t\t\t\t\t\"termsColor\": \"#5496E3\", //  协议文字颜色 默认值： #5496E3\r\n\t\t\t\t\t\t\"prefix\": \"我已阅读并同意\", // 条款前的文案 默认值：“我已阅读并同意”\r\n\t\t\t\t\t\t\"suffix\": \"并使用本机号码登录\", // 条款后的文案 默认值：“并使用本机号码登录”\r\n\t\t\t\t\t\t\"privacyItems\": []\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tagree(agree) {\r\n\t\t\t\tthis.univerifyStyle.privacyTerms.defaultCheckBoxState = agree\r\n\t\t\t}\r\n\t\t},\r\n\t\tasync created() {\r\n\t\t\tlet servicesList = this.servicesList\r\n\t\t\tlet loginTypes = config.loginTypes\r\n\r\n\t\t\tservicesList = servicesList.filter(item => {\r\n\r\n\t\t\t\t// #ifndef APP\r\n\t\t\t\t//非app端去掉apple登录\r\n\t\t\t\tif (item.id == 'apple') {\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\r\n\t\t\t\t// #ifdef APP\r\n\t\t\t\t//去掉非ios系统上的apple登录\r\n\t\t\t\tif (item.id == 'apple' && uni.getSystemInfoSync().osName != 'ios') {\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\r\n\t\t\t\treturn loginTypes.includes(item.id)\r\n\t\t\t})\r\n\t\t\t//处理一键登录\r\n\t\t\tif (loginTypes.includes('univerify')) {\r\n\t\t\t\tthis.univerifyStyle.privacyTerms.privacyItems = this.agreements\r\n\t\t\t\t//设置一键登录功能底下的快捷登录按钮\r\n\t\t\t\tservicesList.forEach(({\r\n\t\t\t\t\tid,\r\n\t\t\t\t\tlogo,\r\n\t\t\t\t\tpath\r\n\t\t\t\t}) => {\r\n\t\t\t\t\tif (id != 'univerify') {\r\n\t\t\t\t\t\tthis.univerifyStyle.buttons.list.push({\r\n\t\t\t\t\t\t\t\"iconPath\": logo,\r\n\t\t\t\t\t\t\t\"provider\": id,\r\n\t\t\t\t\t\t\tpath //路径用于点击快捷按钮时判断是跳转页面\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\t//\tconsole.log(servicesList);\r\n\r\n\t\t\t//去掉当前页面对应的登录选项\r\n\t\t\tthis.servicesList = servicesList.filter(item => {\r\n\t\t\t\tlet path = item.path ? item.path.split('?')[0] : '';\r\n\t\t\t\treturn path != this.getRoute(1)\r\n\t\t\t})\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetParentComponent(){\r\n\t\t\t\t// #ifndef H5\r\n\t\t\t\treturn this.$parent;\r\n\t\t\t\t// #endif\r\n\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\treturn this.$parent.$parent;\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tsetUserInfo(e) {\r\n\t\t\t\tconsole.log('setUserInfo', e);\r\n\t\t\t},\r\n\t\t\tgetRoute(n = 0) {\r\n\t\t\t\tlet pages = getCurrentPages();\r\n\t\t\t\tif (n > pages.length) {\r\n\t\t\t\t\treturn ''\r\n\t\t\t\t}\r\n\t\t\t\treturn '/' + pages[pages.length - n].route\r\n\t\t\t},\r\n\t\t\ttoPage(path,index = 0) {\r\n\t\t\t\t//console.log('比较', this.getRoute(1),this.getRoute(2), path)\r\n\t\t\t\tif (this.getRoute(1) == path.split('?')[0] && this.getRoute(1) ==\r\n\t\t\t\t\t'/uni_modules/uni-id-pages/pages/login/login-withoutpwd') {\r\n\t\t\t\t\t//如果要被打开的页面已经打开，且这个页面是 /uni_modules/uni-id-pages/pages/index/index 则把类型参数传给他\r\n\t\t\t\t\tlet loginType = path.split('?')[1].split('=')[1]\r\n\t\t\t\t\tuni.$emit('uni-id-pages-setLoginType', loginType)\r\n\t\t\t\t} else if (this.getRoute(2) == path) { // 如果上一个页面就是，马上要打开的页面，直接返回。防止重复开启\r\n\t\t\t\t\tuni.navigateBack();\r\n\t\t\t\t} else if (this.getRoute(1) != path) {\r\n\t\t\t\t\tif(index === 0){\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: path,\n\t\t\t\t\t\t\tanimationType: 'slide-in-left',\n\t\t\t\t\t\t\tcomplete(e) {\n\t\t\t\t\t\t\t\t// console.log(e);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t}else{\n\t\t\t\t\t\tuni.redirectTo({\n\t\t\t\t\t\t\turl: path,\n\t\t\t\t\t\t\tanimationType: 'slide-in-left',\n\t\t\t\t\t\t\tcomplete(e) {\n\t\t\t\t\t\t\t\t// console.log(e);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.log('出乎意料的情况,path：' + path);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync login_before(type, navigateBack = true, options = {}) {\r\n\t\t\t\tconsole.log(type);\r\n\t\t\t\t//提示空实现\r\n\t\t\t\tif ([\"qq\",\r\n\t\t\t\t\t\t\"xiaomi\",\r\n\t\t\t\t\t\t\"sinaweibo\",\r\n\t\t\t\t\t\t\"taobao\",\r\n\t\t\t\t\t\t\"facebook\",\r\n\t\t\t\t\t\t\"google\",\r\n\t\t\t\t\t\t\"alipay\",\r\n\t\t\t\t\t\t\"douyin\",\r\n\t\t\t\t\t].includes(type)) {\r\n\t\t\t\t\treturn uni.showToast({\r\n\t\t\t\t\t\ttitle: '该登录方式暂未实现，欢迎提交pr',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 3000\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\r\n\t\t\t\t//检查当前环境是否支持这种登录方式\r\n\t\t\t\t// #ifdef APP\r\n\t\t\t\tlet isAppExist = true\r\n\t\t\t\tawait new Promise((callback) => {\r\n\t\t\t\t\tplus.oauth.getServices(oauthServices => {\r\n\t\t\t\t\t\tlet index = oauthServices.findIndex(e => e.id == type)\r\n\t\t\t\t\t\tif(index != -1){\r\n\t\t\t\t\t\t\tisAppExist = oauthServices[index].nativeClient\r\n\t\t\t\t\t\t\tcallback()\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\treturn uni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '当前设备不支持此登录，请选择其他登录方式',\r\n\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\tduration: 3000\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, err => {\r\n\t\t\t\t\t\tthrow new Error('获取服务供应商失败：' + JSON.stringify(err))\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\r\n\t\t\t\tif (\r\n\t\t\t\t\t// #ifdef APP\r\n\t\t\t\t\t!isAppExist\r\n\t\t\t\t\t// #endif\r\n\r\n\t\t\t\t\t//非app端使用了，app特有登录方式\r\n\t\t\t\t\t// #ifndef APP\r\n\t\t\t\t\t[\"univerify\",\"apple\"].includes(type)\r\n\t\t\t\t\t// #endif\r\n\r\n\t\t\t\t) {\r\n\t\t\t\t\treturn uni.showToast({\r\n\t\t\t\t\t\ttitle: '当前设备不支持此登录，请选择其他登录方式',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 3000\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\r\n\t\t\t\t//判断是否需要弹出隐私协议授权框\r\n\t\t\t\tlet needAgreements = (config?.agreements?.scope || []).includes('register')\r\n\t\t\t\tif (type != 'univerify' && needAgreements && !this.agree) {\r\n\t\t\t\t\tlet agreementsRef = this.getParentComponent().$refs.agreements\r\n\t\t\t\t\treturn agreementsRef.popup(() => {\r\n\t\t\t\t\t\tthis.login_before(type, navigateBack, options)\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tif(type == 'weixin'){\r\n\t\t\t\t\t\t// console.log('开始微信网页登录');\r\n\t\t\t\t\t\t// let redirectUrl = location.protocol +'//'+\r\n\t\t\t\t\t\t// \t\t\t\tdocument.domain +\r\n\t\t\t\t\t\t// \t\t\t\t(window.location.href.includes('#')?'/#':'') +\r\n\t\t\t\t\t\t// \t\t\t\t'/uni_modules/uni-id-pages/pages/login/login-withoutpwd?is_weixin_redirect=true&type=weixin'\r\n\t\t\t\t\t// #ifdef VUE2\r\n\t\t\t\t\tconst baseUrl = process.env.BASE_URL\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifdef VUE3\r\n\t\t\t\t\tconst baseUrl = import.meta.env.BASE_URL\r\n\t\t\t\t\t// #endif\r\n\r\n\t\t\t\t\tlet redirectUrl = location.protocol +\r\n\t\t\t\t\t\t'//' +\r\n\t\t\t\t\t\tlocation.host +\r\n\t\t\t\t\t\tbaseUrl.replace(/\\/$/, '') +\r\n\t\t\t\t\t\t(window.location.href.includes('#')?'/#':'') +\r\n\t\t\t\t\t\t'/uni_modules/uni-id-pages/pages/login/login-withoutpwd?is_weixin_redirect=true&type=weixin'\r\n\t\t\t\t\t\t\r\n\t\t\t\t\tredirectUrl=redirectUrl.replace(location.host,config.webWXAuthBackDomain)\r\n\r\n\t\t\t\t\t// console.log('redirectUrl----',redirectUrl);\r\n\t\t\t\t\tlet ua = window.navigator.userAgent.toLowerCase();\r\n\t\t\t\t\tif (ua.match(/MicroMessenger/i) == 'micromessenger'){\r\n\t\t\t\t\t\t// console.log('在微信公众号内');\r\n\t\t\t\t\t\treturn window.open(`https://open.weixin.qq.com/connect/oauth2/authorize?\r\n\t\t\t\t\t\t\t\t\tappid=${config.appid.weixin.h5}\r\n\t\t\t\t\t\t\t\t\t&redirect_uri=${encodeURIComponent(redirectUrl)}\r\n\t\t\t\t\t\t\t\t\t&response_type=code\r\n\t\t\t\t\t\t\t\t\t&scope=snsapi_userinfo\r\n\t\t\t\t\t\t\t\t\t&state=STATE&connect_redirect=1#wechat_redirect`);\r\n\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\t// console.log('非微信公众号内');\r\n\t\t\t\t\t\treturn location.href = `https://open.weixin.qq.com/connect/qrconnect?appid=${config.appid.weixin.web}\r\n\t\t\t\t\t\t\t\t\t\t&redirect_uri=${encodeURIComponent(redirectUrl)}\r\n\t\t\t\t\t\t\t\t\t\t&response_type=code&scope=snsapi_login&state=STATE#wechat_redirect`\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\tmask: true\r\n\t\t\t\t})\r\n\r\n\t\t\t\tif (type == 'univerify') {\r\n\t\t\t\t\tlet univerifyManager = uni.getUniverifyManager()\r\n\t\t\t\t\tlet clickAnotherButtons = false\r\n\t\t\t\t\tlet onButtonsClickFn = async res => {\r\n\t\t\t\t\t\tconsole.log('点击了第三方登录，provider：', res, res.provider, this.univerifyStyle.buttons.list);\r\n\t\t\t\t\t\tclickAnotherButtons = true\r\n\t\t\t\t\t\tlet checkBoxState = await uni.getCheckBoxState();\r\n\t\t\t\t\t\t// 同步一键登录弹出层隐私协议框是否打勾\r\n\t\t\t\t\t\t// #ifdef VUE2\r\n\t\t\t\t\t\tthis.agree = checkBoxState[1].state\r\n\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t// #ifdef VUE3\r\n\t\t\t\t\t\tthis.agree = checkBoxState.state\r\n\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\tlet {\r\n\t\t\t\t\t\t\tpath\r\n\t\t\t\t\t\t} = this.univerifyStyle.buttons.list[res.index]\r\n\t\t\t\t\t\tif (path) {\r\n\t\t\t\t\t\t\tif( this.getRoute(1).includes('login-withoutpwd') && path.includes('login-withoutpwd') ){\r\n\t\t\t\t\t\t\t\tthis.getParentComponent().showCurrentWebview()\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tthis.toPage(path,1)\r\n\t\t\t\t\t\t\tcloseUniverify()\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tif (this.agree) {\r\n\t\t\t\t\t\t\t\tcloseUniverify()\r\n\t\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t\tthis.login_before(res.provider)\r\n\t\t\t\t\t\t\t\t}, 500)\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: \"你未同意隐私政策协议\",\r\n\t\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\t\tduration: 3000\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tfunction closeUniverify() {\r\n\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\tuniverifyManager.close()\r\n\t\t\t\t\t\t// 取消订阅自定义按钮点击事件\r\n\t\t\t\t\t\tuniverifyManager.offButtonsClick(onButtonsClickFn)\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 订阅自定义按钮点击事件\r\n\t\t\t\t\tuniverifyManager.onButtonsClick(onButtonsClickFn)\r\n\t\t\t\t\t// 调用一键登录弹框\r\n\t\t\t\t\treturn univerifyManager.login({\r\n\t\t\t\t\t\t\"univerifyStyle\": this.univerifyStyle,\r\n\t\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\t\tthis.login(res.authResult, 'univerify')\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail(err) {\r\n\t\t\t\t\t\t\tconsole.log(err)\r\n\t\t\t\t\t\t\tif(!clickAnotherButtons){\r\n\t\t\t\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t// uni.showToast({\r\n\t\t\t\t\t\t\t// \ttitle: JSON.stringify(err),\r\n\t\t\t\t\t\t\t// \ticon: 'none',\r\n\t\t\t\t\t\t\t// \tduration: 3000\r\n\t\t\t\t\t\t\t// });\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tcomplete: async e => {\r\n\t\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\t\t//同步一键登录弹出层隐私协议框是否打勾\r\n\t\t\t\t\t\t\t// this.agree = (await uni.getCheckBoxState())[1].state\r\n\t\t\t\t\t\t\t// 取消订阅自定义按钮点击事件\r\n\t\t\t\t\t\t\tuniverifyManager.offButtonsClick(onButtonsClickFn)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (type === 'weixinMobile') {\r\n\t\t\t\t\treturn this.login({\r\n\t\t\t\t\t\tphoneCode: options.phoneNumberCode\r\n\t\t\t\t\t}, type)\r\n\t\t\t\t}\r\n\r\n\t\t\t\tuni.login({\r\n\t\t\t\t\t\"provider\": type,\r\n\t\t\t\t\t\"onlyAuthorize\": true,\r\n\t\t\t\t\t// #ifdef APP\r\n\t\t\t\t\t\"univerifyStyle\": this.univerifyStyle,\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\tsuccess: async e => {\r\n\t\t\t\t\t\tif (type == 'apple') {\r\n\t\t\t\t\t\t\tlet res = await this.getUserInfo({\r\n\t\t\t\t\t\t\t\tprovider: \"apple\"\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tObject.assign(e.authResult, res.userInfo)\r\n\t\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.login(type == 'weixin' ? {\r\n\t\t\t\t\t\t\tcode: e.code\r\n\t\t\t\t\t\t} : e.authResult, type)\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: async (err) => {\r\n\t\t\t\t\t\tconsole.log(err);\r\n\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tlogin(params, type) { //联网验证登录\r\n\t\t\t\t// console.log('执行登录开始----');\r\n\t\t\t\tconsole.log({params,type});\r\n\t\t\t\t//toLowerCase\r\n\t\t\t\tlet action = 'loginBy' + type.trim().replace(type[0], type[0].toUpperCase())\r\n\t\t\t\tconst uniIdCo = uniCloud.importObject(\"uni-id-co\",{\r\n\t\t\t\t\tcustomUI:true\r\n\t\t\t\t})\r\n\t\t\t\tuniIdCo[action](params).then(result => {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '登录成功',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t});\r\n\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\tresult.loginType = type\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\tmutations.loginSuccess(result)\r\n\t\t\t\t})\r\n\t\t\t\t.catch(e=>{\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\tcontent: e.message,\r\n\t\t\t\t\t\tconfirmText:\"知道了\",\r\n\t\t\t\t\t\tshowCancel: false\r\n\t\t\t\t\t});\r\n\t\t\t\t})\r\n\t\t\t\t.finally(e => {\r\n\t\t\t\t\tif (type == 'univerify') {\r\n\t\t\t\t\t\tuni.closeAuthView()\r\n\t\t\t\t\t}\r\n\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tasync getUserInfo(e) {\r\n\t\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\t\tuni.getUserInfo({\r\n\t\t\t\t\t\t...e,\r\n\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\tresolve(res);\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\tcontent: JSON.stringify(err),\r\n\t\t\t\t\t\t\t\tshowCancel: false\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\treject(err);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t/* #ifndef APP-NVUE */\r\n\t.fab-login-box,\r\n\t.item {\r\n\t\tdisplay: flex;\r\n\t\tbox-sizing: border-box;\r\n\t\tflex-direction: column;\r\n\t}\r\n\t/* #endif */\r\n\r\n\t.fab-login-box {\r\n\t\tflex-direction: row;\r\n\t\tflex-wrap: wrap;\r\n\t\twidth: 750rpx;\r\n\t\tjustify-content: space-around;\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t}\r\n\r\n\t.item {\r\n\t\tflex-direction: column;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\theight: 200rpx;\r\n\t\tcursor: pointer;\r\n\t}\r\n\r\n\t/* #ifndef APP-NVUE */\r\n\t@media screen and (min-width: 690px) {\r\n\t\t.fab-login-box {\r\n\t\t\tmax-width: 500px;\r\n\t\t\tmargin-left: calc(50% - 250px);\r\n\t\t}\r\n\t\t.item {\r\n\t\t\theight: 160rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t@media screen and (max-width: 690px) {\r\n\t\t.fab-login-box {\r\n\t\t\tbottom: 10rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t/* #endif */\r\n\r\n\t.logo {\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tmax-width: 40px;\r\n\t\tmax-height: 40px;\r\n\t\tborder-radius: 100%;\r\n\t\tborder: solid 1px #F6F6F6;\r\n\t}\r\n\r\n\t.login-title {\r\n\t\ttext-align: center;\r\n\t\tmargin-top: 6px;\r\n\t\tcolor: #999;\r\n\t\tfont-size: 10px;\r\n\t\twidth: 70px;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-id-pages-fab-login.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-id-pages-fab-login.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753757591439\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
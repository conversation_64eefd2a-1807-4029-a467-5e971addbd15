<view class="uni-content"><match-media min-width="{{690}}"><view class="login-logo"><image src="{{logo}}"></image></view><text class="title title-box">绑定手机号</text></match-media><uni-easyinput class="input-box" vue-id="3f7ece9e-1" clearable="{{true}}" focus="{{focusMobile}}" type="number" inputBorder="{{false}}" maxlength="11" placeholder="请输入手机号" value="{{formData.mobile}}" data-event-opts="{{[['^blur',[['e0']]],['^input',[['__set_model',['$0','mobile','$event',[]],['formData']]]]]}}" bind:blur="__e" bind:input="__e" bind:__l="__l"></uni-easyinput><uni-id-pages-sms-form bind:input="__e" class="vue-ref" vue-id="3f7ece9e-2" type="bind-mobile-by-sms" phone="{{formData.mobile}}" data-ref="smsForm" value="{{formData.code}}" data-event-opts="{{[['^input',[['__set_model',['$0','code','$event',[]],['formData']]]]]}}" bind:__l="__l"></uni-id-pages-sms-form><button class="uni-btn send-btn-box" type="primary" data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" bindtap="__e">提交</button><uni-popup-captcha class="vue-ref" vue-id="3f7ece9e-3" scene="bind-mobile-by-sms" data-ref="popup" value="{{formData.captcha}}" data-event-opts="{{[['^confirm',[['submit']]],['^input',[['__set_model',['$0','captcha','$event',[]],['formData']]]]]}}" bind:confirm="__e" bind:input="__e" bind:__l="__l"></uni-popup-captcha></view>
<view class="uni-content data-v-5c20cd7a"><view class="avatar data-v-5c20cd7a"><uni-id-pages-avatar vue-id="5e1635c6-1" width="260rpx" height="260rpx" class="data-v-5c20cd7a" bind:__l="__l"></uni-id-pages-avatar></view><uni-list vue-id="5e1635c6-2" class="data-v-5c20cd7a" bind:__l="__l" vue-slots="{{['default']}}"><uni-list-item class="item data-v-5c20cd7a" vue-id="{{('5e1635c6-3')+','+('5e1635c6-2')}}" title="昵称" rightText="{{userInfo.nickname||'未设置'}}" link="{{true}}" data-event-opts="{{[['^click',[['setNickname',['']]]]]}}" bind:click="__e" bind:__l="__l"></uni-list-item><uni-list-item class="item data-v-5c20cd7a" vue-id="{{('5e1635c6-4')+','+('5e1635c6-2')}}" title="手机号" rightText="{{userInfo.mobile||'未绑定'}}" link="{{true}}" data-event-opts="{{[['^click',[['bindMobile']]]]}}" bind:click="__e" bind:__l="__l"></uni-list-item><block wx:if="{{userInfo.email}}"><uni-list-item class="item data-v-5c20cd7a" vue-id="{{('5e1635c6-5')+','+('5e1635c6-2')}}" title="电子邮箱" rightText="{{userInfo.email}}" bind:__l="__l"></uni-list-item></block><block wx:if="{{hasPwd}}"><uni-list-item class="item data-v-5c20cd7a" vue-id="{{('5e1635c6-6')+','+('5e1635c6-2')}}" title="修改密码" link="{{true}}" data-event-opts="{{[['^click',[['changePassword']]]]}}" bind:click="__e" bind:__l="__l"></uni-list-item></block></uni-list><uni-popup vue-id="5e1635c6-7" type="dialog" data-ref="dialog" class="data-v-5c20cd7a vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('5e1635c6-8')+','+('5e1635c6-7')}}" mode="input" value="{{userInfo.nickname}}" inputType="{{setNicknameIng?'nickname':'text'}}" title="设置昵称" placeholder="请输入要设置的昵称" data-event-opts="{{[['^confirm',[['setNickname']]]]}}" bind:confirm="__e" class="data-v-5c20cd7a" bind:__l="__l"></uni-popup-dialog></uni-popup><uni-id-pages-bind-mobile bind:success="__e" vue-id="5e1635c6-9" data-ref="bind-mobile-by-sms" data-event-opts="{{[['^success',[['bindMobileSuccess']]]]}}" class="data-v-5c20cd7a vue-ref" bind:__l="__l"></uni-id-pages-bind-mobile><block wx:if="{{showLoginManage}}"><block wx:if="{{userInfo._id}}"><button data-event-opts="{{[['tap',[['logout',['$event']]]]]}}" bindtap="__e" class="data-v-5c20cd7a">退出登录</button></block><block wx:else><button data-event-opts="{{[['tap',[['login',['$event']]]]]}}" bindtap="__e" class="data-v-5c20cd7a">去登录</button></block></block></view>
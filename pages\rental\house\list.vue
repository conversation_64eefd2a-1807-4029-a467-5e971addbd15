<template>
	<view class="fix-top-window">
		<view class="uni-header">
			<uni-stat-breadcrumb class="uni-stat-breadcrumb-on-phone" />
			<view class="uni-group">
				<input class="uni-search" type="text" v-model="query" @confirm="search"
					placeholder="搜索房源标题、地址" />
				<button class="uni-button hide-on-phone" type="default" size="mini"
					@click="search">搜索</button>
				<button class="uni-button" type="primary" size="mini"
					@click="navigateTo('./add')">新增房源</button>
				<button class="uni-button" type="warn" size="mini" :disabled="!selectedIndexs.length"
					@click="delTable">批量删除</button>
			</view>
		</view>
		<view class="uni-container">
			<unicloud-db ref="udb" collection="rental-houses" 
				field="title,rent_price,house_type,area,address,status,landlord_id,create_date"
				:where="where" page-data="replace" :orderby="orderby" :getcount="true" 
				:page-size="options.pageSize" :page-current="options.pageCurrent" 
				v-slot:default="{data,pagination,loading,error,options}" :options="options" 
				loadtime="manual" @load="onqueryload">
				<uni-table ref="table" :loading="loading" :emptyText="error.message || '没有更多数据'"
					:border="true" :stripe="true" :sort="true" :hover="true">
					<uni-tr>
						<uni-th width="26" align="center">
							<uni-checkbox v-model="selectedAll" @change="selectedAllChange"></uni-checkbox>
						</uni-th>
						<uni-th align="center" sortable @sort-change="sortChange($event,'title')">房源标题</uni-th>
						<uni-th align="center" sortable @sort-change="sortChange($event,'rent_price')">租金(元/月)</uni-th>
						<uni-th align="center">房型</uni-th>
						<uni-th align="center">面积(㎡)</uni-th>
						<uni-th align="center">地址</uni-th>
						<uni-th align="center">状态</uni-th>
						<uni-th align="center" sortable @sort-change="sortChange($event,'create_date')">发布时间</uni-th>
						<uni-th width="204" align="center">操作</uni-th>
					</uni-tr>
					<uni-tr v-for="(item,index) in data" :key="index">
						<uni-td>
							<uni-checkbox v-model="selectedIndexs" :value="index"></uni-checkbox>
						</uni-td>
						<uni-td>
							<view class="house-title">{{ item.title }}</view>
						</uni-td>
						<uni-td align="center">
							<text class="price">{{ item.rent_price }}</text>
						</uni-td>
						<uni-td align="center">{{ item.house_type }}</uni-td>
						<uni-td align="center">{{ item.area }}</uni-td>
						<uni-td>
							<view class="address">
								{{ item.address.district }} {{ item.address.street }}
							</view>
						</uni-td>
						<uni-td align="center">
							<uni-tag :text="item.status" :type="getStatusType(item.status)"></uni-tag>
						</uni-td>
						<uni-td align="center">
							<uni-dateformat :date="item.create_date" format="yyyy-MM-dd hh:mm:ss"></uni-dateformat>
						</uni-td>
						<uni-td align="center">
							<view class="uni-group">
								<button @click="navigateTo('./edit?id='+item._id, false)" class="uni-button" 
									size="mini" type="primary">修改</button>
								<button @click="confirmDelete(index)" class="uni-button" size="mini" 
									type="warn">删除</button>
								<button @click="auditHouse(item)" class="uni-button" size="mini" 
									type="default" v-if="item.status === '待审核'">审核</button>
							</view>
						</uni-td>
					</uni-tr>
				</uni-table>
				<view class="uni-pagination-box">
					<uni-pagination show-icon :page-size="pagination.size" v-model="pagination.current"
						:total="pagination.count" @change="onPageChanged" />
				</view>
			</unicloud-db>
		</view>
	</view>
</template>

<script>
	import {
		enumConverter,
		filterToWhere
	} from '@/js_sdk/validator/opendb-app-list.js'

	const db = uniCloud.database()
	const dbCollectionName = 'rental-houses'

	export default {
		data() {
			return {
				query: '',
				where: '',
				orderby: 'create_date desc',
				selectedIndexs: [],
				options: {
					pageSize: 20,
					pageCurrent: 1,
					filterData: {},
					...enumConverter
				}
			}
		},
		computed: {
			selectedAll: {
				get() {
					return this.selectedIndexs.length > 0 && this.selectedIndexs.length === this.$refs.table.data.length
				},
				set(val) {
					if (val) {
						this.selectedIndexs = this.$refs.table.data.map((item, index) => index)
					} else {
						this.selectedIndexs = []
					}
				}
			}
		},
		onLoad() {
			this._filter = {}
		},
		onReady() {
			this.$refs.udb.loadData()
		},
		methods: {
			getStatusType(status) {
				const statusMap = {
					'待审核': 'warning',
					'已发布': 'success',
					'已出租': 'info',
					'已下架': 'error',
					'已删除': 'error'
				}
				return statusMap[status] || 'default'
			},
			search() {
				const newWhere = this.query ? `title.indexOf("${this.query}") > -1 || address.street.indexOf("${this.query}") > -1` : ''
				this.where = newWhere
				this.$nextTick(() => {
					this.$refs.udb.loadData({
						clear: true
					})
				})
			},
			onqueryload(data) {
				this.selectedIndexs.length = 0
			},
			onPageChanged(e) {
				this.selectedIndexs.length = 0
				this.$refs.udb.loadData({
					current: e.current
				})
			},
			selectedAllChange(e) {
				this.selectedAll = e.detail.value
			},
			sortChange(e, name) {
				this.orderby = name + ' ' + e.order
				this.$refs.udb.loadData()
			},
			confirmDelete(index) {
				this.$refs.table.selectAll(false)
				this.selectedIndexs = [index]
				this.delTable()
			},
			delTable() {
				this.$refs.table.remove(this.selectedIndexs)
			},
			auditHouse(item) {
				uni.showModal({
					title: '房源审核',
					content: '确认通过该房源审核吗？',
					success: (res) => {
						if (res.confirm) {
							this.updateHouseStatus(item._id, '已发布')
						}
					}
				})
			},
			updateHouseStatus(id, status) {
				db.collection(dbCollectionName).doc(id).update({
					status: status,
					'audit_info.audit_status': status === '已发布' ? '审核通过' : '审核拒绝',
					'audit_info.audit_date': new Date(),
					'audit_info.audit_user_id': this.$uniIdPagesStore.userInfo._id
				}).then(() => {
					uni.showToast({
						title: '操作成功'
					})
					this.$refs.udb.loadData()
				}).catch(err => {
					uni.showModal({
						content: err.message || '操作失败',
						showCancel: false
					})
				})
			},
			navigateTo(url, clear) {
				uni.navigateTo({
					url,
					events: {
						refreshData: () => {
							this.$refs.udb.loadData()
						}
					}
				})
			}
		}
	}
</script>

<style lang="scss">
	.house-title {
		max-width: 200px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
	
	.address {
		max-width: 150px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
	
	.price {
		color: #e74c3c;
		font-weight: bold;
	}
</style>

{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/userinfo/userinfo.vue?c2ba", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/userinfo/userinfo.vue?28d7", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/userinfo/userinfo.vue?b28b", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/userinfo/userinfo.vue?3059", "uni-app:///uni_modules/uni-id-pages/pages/userinfo/userinfo.vue", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/userinfo/userinfo.vue?46bb", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/userinfo/userinfo.vue?e25e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "computed", "userInfo", "realNameStatus", "data", "univerifyStyle", "authButton", "otherLogin<PERSON><PERSON>on", "hasPwd", "showLoginManage", "setNicknameIng", "onShow", "onLoad", "uniIdCo", "res", "methods", "login", "uni", "url", "complete", "logout", "mutations", "bindMobileSuccess", "changePassword", "bindMobile", "univerify", "success", "console", "fail", "bindMobileBySmsCode", "setNickname", "nickname", "deactivate", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>unt", "bind<PERSON>ield", "weixin", "alipay", "apple", "qq", "provider", "onlyAuthorize", "code", "title", "duration", "realNameVerify"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AAC+K;AAC/K,gBAAgB,gLAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qUAEN;AACP,KAAK;AACL;AACA,aAAa,uPAEN;AACP,KAAK;AACL;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,aAAa,gWAEN;AACP,KAAK;AACL;AACA,aAAa,mWAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1DA;AAAA;AAAA;AAAA;AAAiqB,CAAgB,ioBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACwCrrB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AADA;AAAA,eAKA;EACAC;IACAC;MACA;IACA;IACAC;MACA;QACA;MACA;MAEA;IACA;EACA;EACAC;IACA;MACAC;QACAC;UACA;QACA;;QACAC;UACA;QACA;MACA;MACA;MACA;MACA;MACA;MACAC;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cACA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAC;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cACA;gBACA;cACA;cACA;cAAA;cAAA,OACAC;YAAA;cAAAC;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAC;IACAC;MACAC;QACAC;QACAC;UACA;QAAA;MAEA;IACA;IACAC;MACAC;IACA;IACAC;MACAD;IACA;IACAE;MACAN;QACAC;QACAC;UACA;QAAA;MAEA;IACA;IACAK;MAcA;IAOA;IACAC;MAAA;MACAR;QACA;QACA;QACAS;UAAA;YAAA;cAAA;gBAAA;kBAAA;oBACAb;sBACAQ;oBACA;sBACAM;oBACA;sBACA;sBACAV;oBACA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CACA;UAAA;YAAA;UAAA;UAAA;QAAA;QACAW;UACAD;UACA;YACA;UACA;QACA;MACA;IACA;IACAE;MACAZ;QACAC;MACA;IACA;IACAY;MACA;QACAT;UACAU;QACA;QACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACAf;QACAC;MACA;IACA;IACAe;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACApB;gBACAqB;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;gBAAA,KAEA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACAzB;cAAA;gBAAA;gBAAA,OACAQ;cAAA;gBAAA;gBAAA;cAAA;gBAEAJ;kBACAsB;kBACAC;kBACAd;oBAAA;sBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA;8BAAA,OACAb;gCACA4B;8BACA;4BAAA;8BAFA3B;8BAGA;gCACAG;kCACAyB;kCACAC;gCACA;8BACA;8BAAA;8BAAA,OACAtB;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CACA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;kBACAO;oBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BACAD;8BACAV;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CACA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA2B;MACA3B;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzNA;AAAA;AAAA;AAAA;AAAoxC,CAAgB,opCAAG,EAAC,C;;;;;;;;;;;ACAxyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-id-pages/pages/userinfo/userinfo.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './uni_modules/uni-id-pages/pages/userinfo/userinfo.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./userinfo.vue?vue&type=template&id=451985ee&scoped=true&\"\nvar renderjs\nimport script from \"./userinfo.vue?vue&type=script&lang=js&\"\nexport * from \"./userinfo.vue?vue&type=script&lang=js&\"\nimport style0 from \"./userinfo.vue?vue&type=style&index=0&id=451985ee&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"451985ee\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-id-pages/pages/userinfo/userinfo.vue\"\nexport default component.exports", "export * from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userinfo.vue?vue&type=template&id=451985ee&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIdPagesAvatar: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-id-pages/components/uni-id-pages-avatar/uni-id-pages-avatar\" */ \"@/uni_modules/uni-id-pages/components/uni-id-pages-avatar/uni-id-pages-avatar.vue\"\n      )\n    },\n    uniList: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-list/components/uni-list/uni-list\" */ \"@/uni_modules/uni-list/components/uni-list/uni-list.vue\"\n      )\n    },\n    uniListItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-list/components/uni-list-item/uni-list-item\" */ \"@/uni_modules/uni-list/components/uni-list-item/uni-list-item.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniPopupDialog: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup-dialog/uni-popup-dialog\" */ \"@/uni_modules/uni-popup/components/uni-popup-dialog/uni-popup-dialog.vue\"\n      )\n    },\n    uniIdPagesBindMobile: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-id-pages/components/uni-id-pages-bind-mobile/uni-id-pages-bind-mobile\" */ \"@/uni_modules/uni-id-pages/components/uni-id-pages-bind-mobile/uni-id-pages-bind-mobile.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userinfo.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userinfo.vue?vue&type=script&lang=js&\"", "<!-- 用户资料页 -->\r\n<template>\r\n\t<view class=\"uni-content\">\r\n\t\t<view class=\"avatar\">\r\n\t\t\t<uni-id-pages-avatar width=\"260rpx\" height=\"260rpx\"></uni-id-pages-avatar>\r\n\t\t</view>\r\n\t\t<uni-list>\r\n\t\t\t<uni-list-item class=\"item\" @click=\"setNickname('')\" title=\"昵称\" :rightText=\"userInfo.nickname||'未设置'\" link>\r\n\t\t\t</uni-list-item>\r\n\t\t\t<uni-list-item class=\"item\" @click=\"bindMobile\" title=\"手机号\" :rightText=\"userInfo.mobile||'未绑定'\" link>\r\n\t\t\t</uni-list-item>\r\n\t\t\t<uni-list-item v-if=\"userInfo.email\" class=\"item\" title=\"电子邮箱\" :rightText=\"userInfo.email\">\r\n\t\t\t</uni-list-item>\r\n\t\t\t<!-- #ifdef APP -->\r\n      <!-- 如未开通实人认证服务，可以将实名认证入口注释 -->\r\n\t\t\t<uni-list-item class=\"item\" @click=\"realNameVerify\" title=\"实名认证\" :rightText=\"realNameStatus !== 2 ? '未认证': '已认证'\" link>\r\n\t\t\t</uni-list-item>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<uni-list-item v-if=\"hasPwd\" class=\"item\" @click=\"changePassword\" title=\"修改密码\" link>\r\n\t\t\t</uni-list-item>\r\n\t\t</uni-list>\r\n\t\t<!-- #ifndef MP -->\r\n\t\t<uni-list class=\"mt10\">\r\n\t\t\t<uni-list-item @click=\"deactivate\" title=\"注销账号\" link=\"navigateTo\"></uni-list-item>\r\n\t\t</uni-list>\r\n\t\t<!-- #endif -->\r\n\t\t<uni-popup ref=\"dialog\" type=\"dialog\">\r\n\t\t\t<uni-popup-dialog mode=\"input\" :value=\"userInfo.nickname\" @confirm=\"setNickname\" :inputType=\"setNicknameIng?'nickname':'text'\"\r\n\t\t\t\ttitle=\"设置昵称\" placeholder=\"请输入要设置的昵称\">\r\n\t\t\t</uni-popup-dialog>\r\n\t\t</uni-popup>\r\n\t\t<uni-id-pages-bind-mobile ref=\"bind-mobile-by-sms\" @success=\"bindMobileSuccess\"></uni-id-pages-bind-mobile>\r\n\t\t<template v-if=\"showLoginManage\">\r\n\t\t\t<button v-if=\"userInfo._id\" @click=\"logout\">退出登录</button>\r\n\t\t\t<button v-else @click=\"login\">去登录</button>\r\n\t\t</template>\r\n\t</view>\r\n</template>\r\n<script>\r\nconst uniIdCo = uniCloud.importObject(\"uni-id-co\")\r\n  import {\r\n    store,\r\n    mutations\r\n  } from '@/uni_modules/uni-id-pages/common/store.js'\r\n\texport default {\r\n    computed: {\r\n      userInfo() {\r\n        return store.userInfo\r\n      },\r\n\t  realNameStatus () {\r\n\t\t  if (!this.userInfo.realNameAuth) {\r\n\t\t\t  return 0\r\n\t\t  }\r\n\r\n\t\t  return this.userInfo.realNameAuth.authStatus\r\n\t  }\r\n    },\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tuniverifyStyle: {\r\n\t\t\t\t\tauthButton: {\r\n\t\t\t\t\t\t\"title\": \"本机号码一键绑定\", // 授权按钮文案\r\n\t\t\t\t\t},\r\n\t\t\t\t\totherLoginButton: {\r\n\t\t\t\t\t\t\"title\": \"其他号码绑定\",\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\t// userInfo: {\r\n\t\t\t\t// \tmobile:'',\r\n\t\t\t\t// \tnickname:''\r\n\t\t\t\t// },\r\n\t\t\t\thasPwd: false,\r\n\t\t\t\tshowLoginManage: false ,//通过页面传参隐藏登录&退出登录按钮\r\n\t\t\t\tsetNicknameIng:false\r\n\t\t\t}\r\n\t\t},\r\n\t\tasync onShow() {\r\n\t\t\tthis.univerifyStyle.authButton.title = \"本机号码一键绑定\"\r\n\t\t\tthis.univerifyStyle.otherLoginButton.title = \"其他号码绑定\"\r\n\t\t},\r\n\t\tasync onLoad(e) {\r\n\t\t\tif (e.showLoginManage) {\r\n\t\t\t\tthis.showLoginManage = true //通过页面传参隐藏登录&退出登录按钮\r\n\t\t\t}\r\n\t\t\t//判断当前用户是否有密码，否则就不显示密码修改功能\r\n\t\t\tlet res = await uniIdCo.getAccountInfo()\r\n\t\t\tthis.hasPwd = res.isPasswordSet\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tlogin() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/uni_modules/uni-id-pages/pages/login/login-withoutpwd',\r\n\t\t\t\t\tcomplete: (e) => {\r\n\t\t\t\t\t\t// console.log(e);\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tlogout() {\r\n\t\t\t\tmutations.logout()\r\n\t\t\t},\r\n\t\t\tbindMobileSuccess() {\r\n\t\t\t\tmutations.updateUserInfo()\r\n\t\t\t},\r\n\t\t\tchangePassword() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/uni_modules/uni-id-pages/pages/userinfo/change_pwd/change_pwd',\r\n\t\t\t\t\tcomplete: (e) => {\r\n\t\t\t\t\t\t// console.log(e);\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tbindMobile() {\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\tuni.preLogin({\r\n\t\t\t\t\tprovider: 'univerify',\r\n\t\t\t\t\tsuccess: this.univerify(), //预登录成功\r\n\t\t\t\t\tfail: (res) => { // 预登录失败\r\n\t\t\t\t\t\t// 不显示一键登录选项（或置灰）\r\n\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t\tthis.bindMobileBySmsCode()\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\r\n\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\tthis.$refs['bind-mobile-by-sms'].open()\r\n\t\t\t\t// #endif\r\n\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\t//...去用验证码绑定\r\n\t\t\t\tthis.bindMobileBySmsCode()\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tuniverify() {\r\n\t\t\t\tuni.login({\r\n\t\t\t\t\t\"provider\": 'univerify',\r\n\t\t\t\t\t\"univerifyStyle\": this.univerifyStyle,\r\n\t\t\t\t\tsuccess: async e => {\r\n\t\t\t\t\t\tuniIdCo.bindMobileByUniverify(e.authResult).then(res => {\r\n\t\t\t\t\t\t\tmutations.updateUserInfo()\r\n\t\t\t\t\t\t}).catch(e => {\r\n\t\t\t\t\t\t\tconsole.log(e);\r\n\t\t\t\t\t\t}).finally(e => {\r\n\t\t\t\t\t\t\t// console.log(e);\r\n\t\t\t\t\t\t\tuni.closeAuthView()\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tconsole.log(err);\r\n\t\t\t\t\t\tif (err.code == '30002' || err.code == '30001') {\r\n\t\t\t\t\t\t\tthis.bindMobileBySmsCode()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tbindMobileBySmsCode() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: './bind-mobile/bind-mobile'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tsetNickname(nickname) {\r\n\t\t\t\tif (nickname) {\r\n\t\t\t\t\tmutations.updateUserInfo({\r\n\t\t\t\t\t\tnickname\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.setNicknameIng = false\r\n\t\t\t\t\tthis.$refs.dialog.close()\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$refs.dialog.open()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tdeactivate(){\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl:\"/uni_modules/uni-id-pages/pages/userinfo/deactivate/deactivate\"\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tasync bindThirdAccount(provider) {\r\n\t\t\t\tconst uniIdCo = uniCloud.importObject(\"uni-id-co\")\r\n\t\t\t\tconst bindField = {\r\n\t\t\t\t\tweixin: 'wx_openid',\r\n\t\t\t\t\talipay: 'ali_openid',\r\n\t\t\t\t\tapple: 'apple_openid',\r\n\t\t\t\t\tqq: 'qq_openid'\r\n\t\t\t\t}[provider.toLowerCase()]\r\n\r\n\t\t\t\tif (this.userInfo[bindField]) {\r\n\t\t\t\t\tawait uniIdCo['unbind' + provider]()\r\n\t\t\t\t\tawait mutations.updateUserInfo()\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.login({\r\n\t\t\t\t\t\tprovider: provider.toLowerCase(),\r\n\t\t\t\t\t\tonlyAuthorize: true,\r\n\t\t\t\t\t\tsuccess: async e => {\r\n\t\t\t\t\t\t\tconst res = await uniIdCo['bind' + provider]({\r\n\t\t\t\t\t\t\t\tcode: e.code\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tif (res.errCode) {\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: res.errMsg || '绑定失败',\r\n\t\t\t\t\t\t\t\t\tduration: 3000\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tawait mutations.updateUserInfo()\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: async (err) => {\r\n\t\t\t\t\t\t\tconsole.log(err);\r\n\t\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\trealNameVerify () {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: \"/uni_modules/uni-id-pages/pages/userinfo/realname-verify/realname-verify\"\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\t@import \"@/uni_modules/uni-id-pages/common/login-page.scss\";\r\n\r\n\t.uni-content {\r\n\t\tpadding: 0;\r\n\t}\r\n\r\n\t/* #ifndef APP-NVUE */\r\n\tview {\r\n\t\tdisplay: flex;\r\n\t\tbox-sizing: border-box;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t@media screen and (min-width: 690px) {\r\n\t\t.uni-content {\r\n\t\t\tpadding: 0;\r\n\t\t\tmax-width: 690px;\r\n\t\t\tmargin-left: calc(50% - 345px);\r\n\t\t\tborder: none;\r\n\t\t\tmax-height: none;\r\n\t\t\tborder-radius: 0;\r\n\t\t\tbox-shadow: none;\r\n\t\t}\r\n\t}\r\n\r\n\t/* #endif */\r\n\t.avatar {\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin: 22px 0;\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.item {\r\n\t\tflex: 1;\r\n\t\tflex-direction: row;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\tbutton {\r\n\t\tmargin: 10%;\r\n\t\tmargin-top: 40px;\r\n\t\tborder-radius: 0;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\twidth: 80%;\r\n\t}\r\n\r\n\t.mt10 {\r\n\t\tmargin-top: 10px;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userinfo.vue?vue&type=style&index=0&id=451985ee&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userinfo.vue?vue&type=style&index=0&id=451985ee&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753757592018\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
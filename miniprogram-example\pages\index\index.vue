<template>
	<view class="container">
		<!-- 顶部搜索栏 -->
		<view class="search-header">
			<view class="location-selector" @click="selectCity">
				<text class="location-text">{{ currentCity }}</text>
				<uni-icons type="arrowdown" size="12" color="#666"></uni-icons>
			</view>
			<view class="search-box" @click="goToSearch">
				<uni-icons type="search" size="16" color="#999"></uni-icons>
				<text class="search-placeholder">搜索小区、地铁站、学校</text>
			</view>
		</view>

		<!-- 轮播图 -->
		<view class="banner-section">
			<swiper class="banner-swiper" indicator-dots="true" autoplay="true" interval="3000" duration="500">
				<swiper-item v-for="(banner, index) in bannerList" :key="index">
					<image class="banner-image" :src="banner.image" mode="aspectFill" @click="onBannerClick(banner)"></image>
				</swiper-item>
			</swiper>
		</view>

		<!-- 快速筛选 -->
		<view class="quick-filter">
			<view class="filter-item" v-for="(filter, index) in quickFilters" :key="index" @click="onFilterClick(filter)">
				<image class="filter-icon" :src="filter.icon" mode="aspectFit"></image>
				<text class="filter-text">{{ filter.name }}</text>
			</view>
		</view>

		<!-- 推荐房源 -->
		<view class="recommend-section">
			<view class="section-header">
				<text class="section-title">推荐房源</text>
				<view class="more-btn" @click="goToHouseList">
					<text class="more-text">更多</text>
					<uni-icons type="arrowright" size="12" color="#666"></uni-icons>
				</view>
			</view>
			
			<view class="house-list">
				<view class="house-item" v-for="(house, index) in recommendHouses" :key="index" @click="goToHouseDetail(house)">
					<view class="house-image-container">
						<image class="house-image" :src="house.images[0]?.url || '/static/default-house.jpg'" mode="aspectFill"></image>
						<view class="house-tag" v-if="house.rent_type">{{ house.rent_type }}</view>
					</view>
					<view class="house-info">
						<view class="house-title">{{ house.title }}</view>
						<view class="house-desc">{{ house.house_type }} · {{ house.area }}㎡</view>
						<view class="house-address">{{ house.address.district }} {{ house.address.street }}</view>
						<view class="house-bottom">
							<view class="house-price">
								<text class="price-symbol">¥</text>
								<text class="price-number">{{ house.rent_price }}</text>
								<text class="price-unit">/月</text>
							</view>
							<view class="house-actions">
								<view class="action-btn" @click.stop="toggleFavorite(house, index)">
									<uni-icons :type="house.is_favorite ? 'heart-filled' : 'heart'" 
										:color="house.is_favorite ? '#ff4757' : '#999'" size="16"></uni-icons>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 加载更多 -->
		<view class="load-more" v-if="hasMore">
			<uni-load-more :status="loadStatus"></uni-load-more>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				currentCity: '深圳',
				bannerList: [
					{
						id: 1,
						image: '/static/banner1.jpg',
						title: '优质房源推荐',
						url: ''
					},
					{
						id: 2,
						image: '/static/banner2.jpg',
						title: '新用户专享优惠',
						url: ''
					}
				],
				quickFilters: [
					{
						id: 1,
						name: '整租',
						icon: '/static/icons/whole-rent.png',
						type: 'rent_type',
						value: '整租'
					},
					{
						id: 2,
						name: '合租',
						icon: '/static/icons/shared-rent.png',
						type: 'rent_type',
						value: '合租'
					},
					{
						id: 3,
						name: '短租',
						icon: '/static/icons/short-rent.png',
						type: 'rent_type',
						value: '短租'
					},
					{
						id: 4,
						name: '地图找房',
						icon: '/static/icons/map-search.png',
						type: 'page',
						value: 'map'
					}
				],
				recommendHouses: [],
				hasMore: true,
				loadStatus: 'more',
				pageNum: 1,
				pageSize: 10
			}
		},
		onLoad() {
			this.loadRecommendHouses()
		},
		onReachBottom() {
			if (this.hasMore) {
				this.loadMoreHouses()
			}
		},
		onPullDownRefresh() {
			this.pageNum = 1
			this.hasMore = true
			this.loadRecommendHouses(true)
		},
		methods: {
			// 选择城市
			selectCity() {
				uni.showActionSheet({
					itemList: ['北京', '上海', '广州', '深圳', '杭州', '成都'],
					success: (res) => {
						const cities = ['北京', '上海', '广州', '深圳', '杭州', '成都']
						this.currentCity = cities[res.tapIndex]
						this.refreshData()
					}
				})
			},

			// 跳转到搜索页
			goToSearch() {
				uni.navigateTo({
					url: '/pages/search/index'
				})
			},

			// 轮播图点击
			onBannerClick(banner) {
				if (banner.url) {
					uni.navigateTo({
						url: banner.url
					})
				}
			},

			// 快速筛选点击
			onFilterClick(filter) {
				if (filter.type === 'page') {
					if (filter.value === 'map') {
						uni.navigateTo({
							url: '/pages/map/index'
						})
					}
				} else {
					uni.navigateTo({
						url: `/pages/house/list?${filter.type}=${filter.value}`
					})
				}
			},

			// 跳转到房源列表
			goToHouseList() {
				uni.navigateTo({
					url: '/pages/house/list'
				})
			},

			// 跳转到房源详情
			goToHouseDetail(house) {
				uni.navigateTo({
					url: `/pages/house/detail?id=${house._id}`
				})
			},

			// 切换收藏状态
			async toggleFavorite(house, index) {
				try {
					const result = await uniCloud.callFunction({
						name: 'rental-service',
						data: {
							action: 'toggleFavorite',
							houseId: house._id,
							isFavorite: !house.is_favorite
						}
					})

					if (result.result.code === 0) {
						this.recommendHouses[index].is_favorite = !house.is_favorite
						uni.showToast({
							title: house.is_favorite ? '取消收藏成功' : '收藏成功',
							icon: 'success'
						})
					} else {
						uni.showToast({
							title: result.result.message,
							icon: 'none'
						})
					}
				} catch (error) {
					uni.showToast({
						title: '操作失败',
						icon: 'none'
					})
				}
			},

			// 加载推荐房源
			async loadRecommendHouses(refresh = false) {
				if (refresh) {
					this.loadStatus = 'loading'
				}

				try {
					const result = await uniCloud.callFunction({
						name: 'rental-service',
						data: {
							action: 'getHouseList',
							city: this.currentCity,
							pageSize: this.pageSize,
							pageNum: this.pageNum,
							orderBy: 'create_date desc'
						}
					})

					if (result.result.code === 0) {
						const houses = result.result.data.list
						
						if (refresh || this.pageNum === 1) {
							this.recommendHouses = houses
						} else {
							this.recommendHouses = [...this.recommendHouses, ...houses]
						}

						this.hasMore = houses.length === this.pageSize
						this.loadStatus = this.hasMore ? 'more' : 'noMore'
					} else {
						uni.showToast({
							title: result.result.message,
							icon: 'none'
						})
						this.loadStatus = 'more'
					}
				} catch (error) {
					uni.showToast({
						title: '加载失败',
						icon: 'none'
					})
					this.loadStatus = 'more'
				}

				if (refresh) {
					uni.stopPullDownRefresh()
				}
			},

			// 加载更多房源
			loadMoreHouses() {
				this.pageNum++
				this.loadStatus = 'loading'
				this.loadRecommendHouses()
			},

			// 刷新数据
			refreshData() {
				this.pageNum = 1
				this.hasMore = true
				this.loadRecommendHouses(true)
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		background-color: #f5f5f5;
		min-height: 100vh;
	}

	.search-header {
		display: flex;
		align-items: center;
		padding: 20rpx 30rpx;
		background-color: #fff;
		border-bottom: 1rpx solid #eee;

		.location-selector {
			display: flex;
			align-items: center;
			margin-right: 20rpx;

			.location-text {
				font-size: 28rpx;
				color: #333;
				margin-right: 8rpx;
			}
		}

		.search-box {
			flex: 1;
			display: flex;
			align-items: center;
			height: 60rpx;
			padding: 0 20rpx;
			background-color: #f5f5f5;
			border-radius: 30rpx;

			.search-placeholder {
				margin-left: 10rpx;
				font-size: 26rpx;
				color: #999;
			}
		}
	}

	.banner-section {
		margin: 20rpx 30rpx;

		.banner-swiper {
			height: 300rpx;
			border-radius: 12rpx;
			overflow: hidden;

			.banner-image {
				width: 100%;
				height: 100%;
			}
		}
	}

	.quick-filter {
		display: flex;
		justify-content: space-around;
		padding: 30rpx;
		background-color: #fff;
		margin: 20rpx 30rpx;
		border-radius: 12rpx;

		.filter-item {
			display: flex;
			flex-direction: column;
			align-items: center;

			.filter-icon {
				width: 60rpx;
				height: 60rpx;
				margin-bottom: 10rpx;
			}

			.filter-text {
				font-size: 24rpx;
				color: #666;
			}
		}
	}

	.recommend-section {
		margin: 20rpx 30rpx;

		.section-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 20rpx;

			.section-title {
				font-size: 32rpx;
				font-weight: bold;
				color: #333;
			}

			.more-btn {
				display: flex;
				align-items: center;

				.more-text {
					font-size: 26rpx;
					color: #666;
					margin-right: 8rpx;
				}
			}
		}
	}

	.house-list {
		.house-item {
			display: flex;
			background-color: #fff;
			border-radius: 12rpx;
			margin-bottom: 20rpx;
			overflow: hidden;

			.house-image-container {
				position: relative;
				width: 240rpx;
				height: 180rpx;

				.house-image {
					width: 100%;
					height: 100%;
				}

				.house-tag {
					position: absolute;
					top: 10rpx;
					left: 10rpx;
					padding: 4rpx 8rpx;
					background-color: rgba(255, 71, 87, 0.9);
					color: #fff;
					font-size: 20rpx;
					border-radius: 4rpx;
				}
			}

			.house-info {
				flex: 1;
				padding: 20rpx;
				display: flex;
				flex-direction: column;
				justify-content: space-between;

				.house-title {
					font-size: 28rpx;
					color: #333;
					font-weight: bold;
					margin-bottom: 8rpx;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}

				.house-desc {
					font-size: 24rpx;
					color: #666;
					margin-bottom: 8rpx;
				}

				.house-address {
					font-size: 24rpx;
					color: #999;
					margin-bottom: 16rpx;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}

				.house-bottom {
					display: flex;
					justify-content: space-between;
					align-items: center;

					.house-price {
						display: flex;
						align-items: baseline;

						.price-symbol {
							font-size: 24rpx;
							color: #ff4757;
						}

						.price-number {
							font-size: 32rpx;
							color: #ff4757;
							font-weight: bold;
						}

						.price-unit {
							font-size: 24rpx;
							color: #ff4757;
						}
					}

					.house-actions {
						.action-btn {
							padding: 8rpx;
						}
					}
				}
			}
		}
	}

	.load-more {
		padding: 20rpx;
	}
</style>

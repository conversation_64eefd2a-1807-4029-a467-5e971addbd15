<view class="uni-content data-v-bd4980dc"><view class="login-logo data-v-bd4980dc"><image src="{{logo}}" class="data-v-bd4980dc"></image></view><text class="title data-v-bd4980dc">请选择登录方式</text><block wx:if="{{$root.g0}}"><text class="tip data-v-bd4980dc">将根据第三方账号服务平台的授权范围获取你的信息</text><view class="quickLogin data-v-bd4980dc"><block wx:if="{{type!=='weixinMobile'}}"><image class="quickLoginBtn data-v-bd4980dc" src="{{imgSrc}}" mode="widthFix" data-event-opts="{{[['tap',[['quickLogin',['$event']]]]]}}" bindtap="__e"></image></block><block wx:else><button class="uni-btn data-v-bd4980dc" type="primary" open-type="getPhoneNumber" data-event-opts="{{[['getphonenumber',[['quickLogin',['$event']]]]]}}" bindgetphonenumber="__e">微信授权手机号登录</button></block><uni-id-pages-agreements vue-id="711a0fb1-1" scope="register" data-ref="agreements" class="data-v-bd4980dc vue-ref" bind:__l="__l"></uni-id-pages-agreements></view></block><block wx:else><text class="tip data-v-bd4980dc">未注册的账号验证通过后将自动注册</text><view class="phone-box data-v-bd4980dc"><view data-event-opts="{{[['tap',[['chooseArea',['$event']]]]]}}" class="area data-v-bd4980dc" bindtap="__e">+86</view><uni-easyinput class="input-box data-v-bd4980dc" vue-id="711a0fb1-2" focus="{{focusPhone}}" type="number" inputBorder="{{false}}" maxlength="11" placeholder="请输入手机号" value="{{phone}}" data-event-opts="{{[['^blur',[['e0']]],['^input',[['__set_model',['','phone','$event',[]]]]]]}}" bind:blur="__e" bind:input="__e" bind:__l="__l"></uni-easyinput></view><uni-id-pages-agreements vue-id="711a0fb1-3" scope="register" data-ref="agreements" class="data-v-bd4980dc vue-ref" bind:__l="__l"></uni-id-pages-agreements><button class="uni-btn data-v-bd4980dc" type="primary" data-event-opts="{{[['tap',[['toSmsPage',['$event']]]]]}}" bindtap="__e">获取验证码</button></block><uni-id-pages-fab-login vue-id="711a0fb1-4" data-ref="uniFabLogin" class="data-v-bd4980dc vue-ref" bind:__l="__l"></uni-id-pages-fab-login></view>
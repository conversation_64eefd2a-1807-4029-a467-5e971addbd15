(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uni_modules/uni-id-pages/pages/common/webview/webview"],{"0a3c":function(t,n,e){"use strict";e.r(n);var a=e("4705"),u=e("d4fd");for(var i in u)["default"].indexOf(i)<0&&function(t){e.d(n,t,(function(){return u[t]}))}(i);var c=e("828b"),o=Object(c["a"])(u["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);n["default"]=o.exports},4705:function(t,n,e){"use strict";e.d(n,"b",(function(){return a})),e.d(n,"c",(function(){return u})),e.d(n,"a",(function(){}));var a=function(){var t=this.$createElement;this._self._c},u=[]},b201:function(t,n,e){"use strict";(function(t){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var e={onLoad:function(n){var e=n.url,a=n.title;"http"!=e.substring(0,4)?(t.showModal({title:"错误",content:'不是一个有效的网站链接,"'+e+'"',showCancel:!1,confirmText:"知道了",complete:function(){t.navigateBack()}}),a="页面路径错误"):this.url=e,a&&t.setNavigationBarTitle({title:a})},data:function(){return{url:null}}};n.default=e}).call(this,e("df3c")["default"])},c36a:function(t,n,e){"use strict";(function(t,n){var a=e("47a9");e("a019"),e("861b");a(e("3240"));var u=a(e("0a3c"));t.__webpack_require_UNI_MP_PLUGIN__=e,n(u.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},d4fd:function(t,n,e){"use strict";e.r(n);var a=e("b201"),u=e.n(a);for(var i in a)["default"].indexOf(i)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(i);n["default"]=u.a}},[["c36a","common/runtime","common/vendor"]]]);
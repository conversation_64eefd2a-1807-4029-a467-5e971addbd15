(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uni_modules/uni-id-pages/pages/userinfo/realname-verify/realname-verify"],{"217a":function(e,t,n){"use strict";var i=n("735c"),r=n.n(i);r.a},"735c":function(e,t,n){},8668:function(e,t,n){"use strict";n.r(t);var i=n("d11e"),r=n.n(i);for(var u in i)["default"].indexOf(u)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(u);t["default"]=r.a},c6fc:function(e,t,n){"use strict";n.r(t);var i=n("d824"),r=n("8668");for(var u in r)["default"].indexOf(u)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(u);n("217a");var a=n("828b"),o=Object(a["a"])(r["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);t["default"]=o.exports},d11e:function(e,t,n){"use strict";(function(e,i){var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var u=r(n("b7d4")),a=r(n("7eb4")),o=r(n("ee10")),s=(r(n("6f83")),r(n("f6dd"))),c=n("f12a"),f=["errCode"],l=e.importObject("uni-id-co"),d={mixins:[s.default],data:function(){return{realName:"",idCard:"",certifyId:"",verifyFail:!1,verifyFailCode:0,verifyFailTitle:"",verifyFailContent:""}},computed:{userInfo:function(){return c.store.userInfo},certifyIdNext:function(){return Boolean(this.realName)&&Boolean(this.idCard)&&this.needAgreements&&this.agree},isCertify:function(){return this.userInfo.realNameAuth&&2===this.userInfo.realNameAuth.authStatus},isDev:function(){return!1}},onLoad:function(){var e=i.getStorageSync("uni-id-pages-temp-frv");e&&(this.realName=e.realName,this.idCard=e.idCard)},methods:{getCertifyId:function(){var e=this;return(0,o.default)(a.default.mark((function t(){var n,r;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.certifyIdNext){t.next=2;break}return t.abrupt("return");case 2:return t.abrupt("return",i.showModal({content:"暂不支持实名认证",showCancel:!1}));case 6:if(!("string"!==typeof e.realName||e.realName.length<2)&&/^[\u4e00-\u9fa5]{1,10}(·?[\u4e00-\u9fa5]{1,10}){0,5}$/.test(e.realName)){t.next=9;break}return i.showToast({title:"姓名只能是汉字",icon:"none"}),t.abrupt("return");case 9:return i.setStorage({key:"uni-id-pages-temp-frv",data:{realName:e.realName,idCard:e.idCard}}),n=i.getFacialRecognitionMetaInfo(),t.next=13,l.getFrvCertifyId({realName:e.realName,idCard:e.idCard,metaInfo:n});case 13:r=t.sent,e.certifyId=r.certifyId,e.startFacialRecognitionVerify();case 16:case"end":return t.stop()}}),t)})))()},startFacialRecognitionVerify:function(){},getFrvAuthResult:function(){var t=this;return(0,o.default)(a.default.mark((function n(){var r,o,s;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return r=e.importObject("uni-id-co",{customUI:!0}),n.prev=1,i.showLoading({title:"验证中...",mask:!1}),n.next=5,r.getFrvAuthResult({certifyId:t.certifyId});case 5:o=n.sent,o.errCode,s=(0,u.default)(o,f),t.verifyFailContent&&console.log("[frv-debug] 客户端刷脸失败，由实人认证服务查询具体原因，原因：".concat(t.verifyFailContent)),i.showModal({content:"实名认证成功",showCancel:!1,success:function(){c.mutations.setUserInfo({realNameAuth:s}),t.verifyFail=!1}}),i.removeStorage({key:"uni-id-pages-temp-frv"}),n.next=17;break;case 12:n.prev=12,n.t0=n["catch"](1),t.verifyFail=!0,t.verifyFailTitle=n.t0.errMsg,console.error(JSON.stringify(n.t0));case 17:return n.prev=17,i.hideLoading(),n.finish(17);case 20:case"end":return n.stop()}}),n,null,[[1,12,17,20]])})))()},retry:function(){10013!==this.verifyFailCode?this.getCertifyId():this.verifyFail=!1}}};t.default=d}).call(this,n("861b")["uniCloud"],n("df3c")["default"])},d1e4:function(e,t,n){"use strict";(function(e,t){var i=n("47a9");n("a019"),n("861b");i(n("3240"));var r=i(n("c6fc"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(r.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},d824:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return u})),n.d(t,"a",(function(){return i}));var i={uniList:function(){return n.e("uni_modules/uni-list/components/uni-list/uni-list").then(n.bind(null,"0031"))},uniListItem:function(){return n.e("uni_modules/uni-list/components/uni-list-item/uni-list-item").then(n.bind(null,"eb88"))},uniForms:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uni-forms/components/uni-forms/uni-forms")]).then(n.bind(null,"bd01"))},uniFormsItem:function(){return n.e("uni_modules/uni-forms/components/uni-forms-item/uni-forms-item").then(n.bind(null,"1097"))},uniEasyinput:function(){return n.e("uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput").then(n.bind(null,"b6bb"))},uniIdPagesAgreements:function(){return n.e("uni_modules/uni-id-pages/components/uni-id-pages-agreements/uni-id-pages-agreements").then(n.bind(null,"1219"))}},r=function(){var e=this.$createElement;this._self._c},u=[]}},[["d1e4","common/runtime","common/vendor"]]]);
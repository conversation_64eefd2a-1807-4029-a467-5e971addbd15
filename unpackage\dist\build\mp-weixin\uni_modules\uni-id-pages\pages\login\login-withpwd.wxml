<view class="uni-content data-v-d04cbff4"><view class="login-logo data-v-d04cbff4"><image src="{{logo}}" class="data-v-d04cbff4"></image></view><text class="title title-box data-v-d04cbff4">账号密码登录</text><uni-forms vue-id="7035d37f-1" class="data-v-d04cbff4" bind:__l="__l" vue-slots="{{['default']}}"><uni-forms-item vue-id="{{('7035d37f-2')+','+('7035d37f-1')}}" name="username" class="data-v-d04cbff4" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput class="input-box data-v-d04cbff4" vue-id="{{('7035d37f-3')+','+('7035d37f-2')}}" focus="{{focusUsername}}" inputBorder="{{false}}" placeholder="请输入手机号/用户名/邮箱" value="{{username}}" data-event-opts="{{[['^blur',[['e0']]],['^input',[['__set_model',['','username','$event',[]]]]]]}}" bind:blur="__e" bind:input="__e" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item vue-id="{{('7035d37f-4')+','+('7035d37f-1')}}" name="password" class="data-v-d04cbff4" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput class="input-box data-v-d04cbff4" vue-id="{{('7035d37f-5')+','+('7035d37f-4')}}" focus="{{focusPassword}}" clearable="{{true}}" type="password" inputBorder="{{false}}" placeholder="请输入密码" value="{{password}}" data-event-opts="{{[['^blur',[['e1']]],['^input',[['__set_model',['','password','$event',[]]]]]]}}" bind:blur="__e" bind:input="__e" bind:__l="__l"></uni-easyinput></uni-forms-item></uni-forms><block wx:if="{{needCaptcha}}"><uni-captcha bind:input="__e" vue-id="7035d37f-6" focus="{{true}}" scene="login-by-pwd" data-ref="captcha" value="{{captcha}}" data-event-opts="{{[['^input',[['__set_model',['','captcha','$event',[]]]]]]}}" class="data-v-d04cbff4 vue-ref" bind:__l="__l"></uni-captcha></block><uni-id-pages-agreements vue-id="7035d37f-7" scope="login" data-ref="agreements" class="data-v-d04cbff4 vue-ref" bind:__l="__l"></uni-id-pages-agreements><button class="uni-btn data-v-d04cbff4" type="primary" data-event-opts="{{[['tap',[['pwdLogin',['$event']]]]]}}" bindtap="__e">登录</button><view class="link-box data-v-d04cbff4"><block wx:if="{{!config.isAdmin}}"><view class="data-v-d04cbff4"><text class="forget data-v-d04cbff4">忘记了？</text><text data-event-opts="{{[['tap',[['toRetrievePwd',['$event']]]]]}}" class="link data-v-d04cbff4" bindtap="__e">找回密码</text></view></block><text data-event-opts="{{[['tap',[['toRegister',['$event']]]]]}}" class="link data-v-d04cbff4" bindtap="__e">{{config.isAdmin?'注册管理员账号':'注册账号'}}</text></view><uni-id-pages-fab-login vue-id="7035d37f-8" data-ref="uniFabLogin" class="data-v-d04cbff4 vue-ref" bind:__l="__l"></uni-id-pages-fab-login></view>
{"version": 3, "sources": ["webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/components/uni-id-pages-agreements/uni-id-pages-agreements.vue?a578", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/components/uni-id-pages-agreements/uni-id-pages-agreements.vue?eba6", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/components/uni-id-pages-agreements/uni-id-pages-agreements.vue?93d9", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/components/uni-id-pages-agreements/uni-id-pages-agreements.vue?511c", "uni-app:///uni_modules/uni-id-pages/components/uni-id-pages-agreements/uni-id-pages-agreements.vue", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/components/uni-id-pages-agreements/uni-id-pages-agreements.vue?e2bc", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/components/uni-id-pages-agreements/uni-id-pages-agreements.vue?688d"], "names": ["name", "computed", "agreements", "serviceUrl", "privacyUrl", "url", "title", "props", "scope", "type", "default", "methods", "popupConfirm", "retryFun", "popup", "navigateTo", "uni", "success", "fail", "complete", "hasAnd", "setAgree", "created", "data", "isAgree", "needAgreements", "needPopupAgreements"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgJ;AAChJ;AAC2E;AACL;AACsC;;;AAG5G;AAC+K;AAC/K,gBAAgB,gLAAU;AAC1B,EAAE,6FAAM;AACR,EAAE,8GAAM;AACR,EAAE,uHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kHAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,aAAa,gWAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvEA;AAAA;AAAA;AAAA;AAAgrB,CAAgB,gpBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACgCpsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;EAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAJA,gBAKA;EACAA;EACAC;IACAC;MACA;QACA;MACA;MACA;QAAAC;QAAAC;MACA,QACA;QACAC;QACAC;MACA,GACA;QACAD;QACAC;MACA,EACA;IACA;EACA;EACAC;IACAC;MACAC;MACAC;QACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QACA;UACAD;QACA;QACA;MACA;IACA;IACAE,sCAGA;MAAA,IAFAV;QACAC;MAEAU;QACAX;QACAY;QACAC;QACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;EACA;EACAC;IAAA;IACA;EACA;EACAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AChHA;AAAA;AAAA;AAAA;AAAmyC,CAAgB,mqCAAG,EAAC,C;;;;;;;;;;;ACAvzC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-id-pages/components/uni-id-pages-agreements/uni-id-pages-agreements.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-id-pages-agreements.vue?vue&type=template&id=6abcbb91&scoped=true&\"\nvar renderjs\nimport script from \"./uni-id-pages-agreements.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-id-pages-agreements.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-id-pages-agreements.vue?vue&type=style&index=0&id=6abcbb91&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6abcbb91\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-id-pages/components/uni-id-pages-agreements/uni-id-pages-agreements.vue\"\nexport default component.exports", "export * from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-id-pages-agreements.vue?vue&type=template&id=6abcbb91&scoped=true&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniPopupDialog: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup-dialog/uni-popup-dialog\" */ \"@/uni_modules/uni-popup/components/uni-popup-dialog/uni-popup-dialog.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.agreements.length\n  var l0 =\n    g0 && _vm.needAgreements\n      ? _vm.__map(_vm.agreements, function (agreement, index) {\n          var $orig = _vm.__get_orig(agreement)\n          var m0 = _vm.hasAnd(_vm.agreements, index)\n          return {\n            $orig: $orig,\n            m0: m0,\n          }\n        })\n      : null\n  var l1 =\n    g0 && (_vm.needAgreements || _vm.needPopupAgreements)\n      ? _vm.__map(_vm.agreements, function (agreement, index) {\n          var $orig = _vm.__get_orig(agreement)\n          var m1 = _vm.hasAnd(_vm.agreements, index)\n          return {\n            $orig: $orig,\n            m1: m1,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-id-pages-agreements.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-id-pages-agreements.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"root\" v-if=\"agreements.length\">\r\n\t\t<template v-if=\"needAgreements\">\r\n\t\t\t<checkbox-group @change=\"setAgree\">\r\n\t\t\t\t<label class=\"checkbox-box\">\r\n\t\t\t\t\t<checkbox :checked=\"isAgree\" style=\"transform: scale(0.5);margin-right: -6px;\" />\r\n\t\t\t\t\t<text class=\"text\">同意</text>\r\n\t\t\t\t</label>\r\n\t\t\t</checkbox-group>\r\n\t\t\t<view class=\"content\">\r\n\t\t\t\t<view class=\"item\" v-for=\"(agreement,index) in agreements\" :key=\"index\">\r\n\t\t\t\t\t<text class=\"agreement text\" @click=\"navigateTo(agreement)\">{{agreement.title}}</text>\r\n\t\t\t\t\t<text class=\"text and\" v-if=\"hasAnd(agreements,index)\" space=\"nbsp\"> 和 </text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</template>\r\n\t\t<!-- 弹出式 -->\r\n\t\t<uni-popup v-if=\"needAgreements||needPopupAgreements\" ref=\"popupAgreement\" type=\"center\">\r\n\t\t\t<uni-popup-dialog confirmText=\"同意\" @confirm=\"popupConfirm\">\r\n\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t<text class=\"text\">请先阅读并同意</text>\r\n\t\t\t\t\t<view class=\"item\" v-for=\"(agreement,index) in agreements\" :key=\"index\">\r\n\t\t\t\t\t\t<text class=\"agreement text\" @click=\"navigateTo(agreement)\">{{agreement.title}}</text>\r\n\t\t\t\t\t\t<text class=\"text and\" v-if=\"hasAnd(agreements,index)\" space=\"nbsp\"> 和 </text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</uni-popup-dialog>\r\n\t\t</uni-popup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport config from '@/uni_modules/uni-id-pages/config.js'\r\n\tlet retryFun = ()=>console.log('为定义')\r\n\t/**\r\n\t\t* uni-id-pages-agreements \r\n\t\t* @description 用户服务协议和隐私政策条款组件\r\n\t\t* @property {String,Boolean} scope = [register|login]\t作用于哪种场景如：register 注册（包括登录并注册，如：微信登录、苹果登录、短信验证码登录）、login 登录。默认值为：register\r\n\t*/\r\n\texport default {\r\n\t\tname: \"uni-agreements\",\r\n\t\tcomputed: {\r\n\t\t\tagreements() {\r\n\t\t\t\tif(!config.agreements){\r\n\t\t\t\t\treturn []\r\n\t\t\t\t}\r\n\t\t\t\tlet {serviceUrl,privacyUrl} = config.agreements\r\n\t\t\t\treturn [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\turl:serviceUrl,\r\n\t\t\t\t\t\ttitle:\"用户服务协议\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\turl:privacyUrl,\r\n\t\t\t\t\t\ttitle:\"隐私政策条款\"\r\n\t\t\t\t\t}\r\n\t\t\t\t]\r\n\t\t\t}\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tscope: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault(){\r\n\t\t\t\t\treturn 'register'\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tpopupConfirm(){\r\n\t\t\t\tthis.isAgree = true\r\n\t\t\t\tretryFun()\r\n\t\t\t\t// this.$emit('popupConfirm')\r\n\t\t\t},\r\n\t\t\tpopup(Fun){\r\n\t\t\t\tthis.needPopupAgreements = true\r\n\t\t\t\t// this.needAgreements = true\r\n\t\t\t\tthis.$nextTick(()=>{\r\n\t\t\t\t\tif(Fun){\r\n\t\t\t\t\t\tretryFun = Fun\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.$refs.popupAgreement.open()\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tnavigateTo({\r\n\t\t\t\turl,\r\n\t\t\t\ttitle\r\n\t\t\t}) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/uni_modules/uni-id-pages/pages/common/webview/webview?url=' + url + '&title=' + title,\r\n\t\t\t\t\tsuccess: res => {},\r\n\t\t\t\t\tfail: () => {},\r\n\t\t\t\t\tcomplete: () => {}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\thasAnd(agreements, index) {\r\n\t\t\t\treturn agreements.length - 1 > index\r\n\t\t\t},\r\n\t\t\tsetAgree(e) {\r\n\t\t\t\tthis.isAgree = !this.isAgree\r\n\t\t\t\tthis.$emit('setAgree', this.isAgree)\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\n\t\t\tthis.needAgreements = (config?.agreements?.scope || []).includes(this.scope)\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tisAgree: false,\r\n\t\t\t\tneedAgreements:true,\r\n\t\t\t\tneedPopupAgreements:false\r\n\t\t\t};\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t/* #ifndef APP-NVUE */\r\n\tview {\r\n\t\tdisplay: flex;\r\n\t\tbox-sizing: border-box;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t/* #endif */\r\n\t.root {\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tfont-size: 12px;\r\n\t\tcolor: #8a8f8b;\r\n\t}\r\n\r\n\t.checkbox-box ,.uni-label-pointer{\r\n\t\talign-items: center;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t}\r\n\r\n\t.item {\r\n\t\tflex-direction: row;\r\n\t}\r\n\t.text{\r\n\t\tline-height: 26px;\r\n\t}\r\n\t.agreement {\r\n\t\tcolor: #04498c;\r\n\t\tcursor: pointer;\r\n\t}\r\n\t\r\n\t.checkbox-box ::v-deep .uni-checkbox-input{\r\n\t\tborder-radius: 100%;\r\n\t}\r\n\r\n\t.checkbox-box ::v-deep .uni-checkbox-input.uni-checkbox-input-checked{\r\n\t\tborder-color: $uni-color-primary;\r\n\t\tcolor: #FFFFFF !important;\r\n\t\tbackground-color: $uni-color-primary;\r\n\t}\r\n\t\r\n\t.content{\r\n\t\tflex-wrap: wrap;\r\n\t\tflex-direction: row;\r\n\t}\n\t\n\t.root ::v-deep .uni-popup__error{\n\t\tcolor: #333333;\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-id-pages-agreements.vue?vue&type=style&index=0&id=6abcbb91&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-id-pages-agreements.vue?vue&type=style&index=0&id=6abcbb91&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753757591424\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
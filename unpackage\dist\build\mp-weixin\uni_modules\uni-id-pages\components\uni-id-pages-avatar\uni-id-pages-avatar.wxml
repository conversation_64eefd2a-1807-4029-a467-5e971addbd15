<button class="{{['box',(border)?'showBorder':'']}}" style="{{'width:'+(width)+';'+('height:'+(height)+';')+('line-height:'+(height)+';')}}" open-type="chooseAvatar" data-event-opts="{{[['chooseavatar',[['bindchooseavatar',['$event']]]],['tap',[['uploadAvatarImg',['$event']]]]]}}" bindchooseavatar="__e" bindtap="__e"><block wx:if="{{avatar_file}}"><cloud-image vue-id="2fb88435-1" src="{{avatar_file.url}}" width="{{width}}" height="{{height}}" bind:__l="__l"></cloud-image></block><block wx:else><uni-icons class="chooseAvatar" style="{{'width:'+(width)+';'+('height:'+(height)+';')+('line-height:'+(height)+';')}}" vue-id="2fb88435-2" type="plusempty" size="30" color="#dddddd" bind:__l="__l"></uni-icons></block></button>
(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["common/vendor"],{"011a":function(e,t){function n(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>,[],(function(){})))}catch(t){}return(e.exports=n=function(){return!!t},e.exports.__esModule=!0,e.exports["default"]=e.exports)()}e.exports=n,e.exports.__esModule=!0,e.exports["default"]=e.exports},"0bdb":function(e,t,n){var r=n("d551");function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,r(i.key),i)}}e.exports=function(e,t,n){return t&&i(e.prototype,t),n&&i(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports["default"]=e.exports},"0ee4":function(e,t){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(r){"object"===typeof window&&(n=window)}e.exports=n},"20c2":function(e){e.exports=JSON.parse('{"uni-load-more.contentdown":"Pull up to show more","uni-load-more.contentrefresh":"loading...","uni-load-more.contentnomore":"No more data"}')},"23ba":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={pages:[{path:"pages/index/index",style:{navigationBarTitleText:"uni-app"}},{path:"uni_modules/uni-id-pages/pages/userinfo/deactivate/deactivate",style:{navigationBarTitleText:"注销账号"}},{path:"uni_modules/uni-id-pages/pages/userinfo/userinfo",style:{navigationBarTitleText:"个人资料"}},{path:"uni_modules/uni-id-pages/pages/userinfo/bind-mobile/bind-mobile",style:{navigationBarTitleText:"绑定手机号码"}},{path:"uni_modules/uni-id-pages/pages/login/login-withoutpwd",style:{navigationBarTitleText:""}},{path:"uni_modules/uni-id-pages/pages/login/login-withpwd",style:{navigationBarTitleText:""}},{path:"uni_modules/uni-id-pages/pages/login/login-smscode",style:{navigationBarTitleText:"手机验证码登录"}},{path:"uni_modules/uni-id-pages/pages/register/register",style:{navigationBarTitleText:"注册"}},{path:"uni_modules/uni-id-pages/pages/register/register-by-email",style:{navigationBarTitleText:"邮箱验证码注册"}},{path:"uni_modules/uni-id-pages/pages/retrieve/retrieve",style:{navigationBarTitleText:"重置密码"}},{path:"uni_modules/uni-id-pages/pages/retrieve/retrieve-by-email",style:{navigationBarTitleText:"通过邮箱重置密码"}},{path:"uni_modules/uni-id-pages/pages/common/webview/webview",style:{navigationBarTitleText:"",enablePullDownRefresh:!1}},{path:"uni_modules/uni-id-pages/pages/userinfo/change_pwd/change_pwd",style:{navigationBarTitleText:"修改密码",enablePullDownRefresh:!1}},{path:"uni_modules/uni-id-pages/pages/userinfo/set-pwd/set-pwd",style:{navigationBarTitleText:"设置密码",enablePullDownRefresh:!1}},{path:"uni_modules/uni-id-pages/pages/userinfo/realname-verify/realname-verify",style:{navigationBarTitleText:"实名认证",enablePullDownRefresh:!1}}],globalStyle:{navigationBarTextStyle:"black",navigationBarTitleText:"uni-app",navigationBarBackgroundColor:"#FFF",backgroundColor:"#F8F8F8"},condition:{current:0,list:[{name:"",path:"",query:""}]},uniIdRouter:{loginPage:"uni_modules/uni-id-pages/pages/login/login-withoutpwd",needLogin:["pages/index/index","uni_modules/uni-id-pages/pages/userinfo/userinfo"],resToLogin:!0}}},3223:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=["qy","env","error","version","lanDebug","cloud","serviceMarket","router","worklet","__webpack_require_UNI_MP_PLUGIN__"],i=["lanDebug","router","worklet"],o="undefined"!==typeof globalThis?globalThis:function(){return this}(),a=["w","x"].join(""),s=o[a],c=s.getLaunchOptionsSync?s.getLaunchOptionsSync():null;function u(e){return(!c||1154!==c.scene||!i.includes(e))&&(r.indexOf(e)>-1||"function"===typeof s[e])}o[a]=function(){var e={};for(var t in s)u(t)&&(e[t]=s[t]);return e}(),o[a].canIUse("getAppBaseInfo")||(o[a].getAppBaseInfo=o[a].getSystemInfoSync),o[a].canIUse("getWindowInfo")||(o[a].getWindowInfo=o[a].getSystemInfoSync),o[a].canIUse("getDeviceInfo")||(o[a].getDeviceInfo=o[a].getSystemInfoSync);var l=o[a];t.default=l},3240:function(e,t,n){"use strict";n.r(t),function(e){
/*!
 * Vue.js v2.6.11
 * (c) 2014-2024 Evan You
 * Released under the MIT License.
 */
var n=Object.freeze({});function r(e){return void 0===e||null===e}function i(e){return void 0!==e&&null!==e}function o(e){return!0===e}function a(e){return"string"===typeof e||"number"===typeof e||"symbol"===typeof e||"boolean"===typeof e}function s(e){return null!==e&&"object"===typeof e}var c=Object.prototype.toString;function u(e){return"[object Object]"===c.call(e)}function l(e){var t=parseFloat(String(e));return t>=0&&Math.floor(t)===t&&isFinite(e)}function f(e){return i(e)&&"function"===typeof e.then&&"function"===typeof e.catch}function d(e){return null==e?"":Array.isArray(e)||u(e)&&e.toString===c?JSON.stringify(e,null,2):String(e)}function p(e){var t=parseFloat(e);return isNaN(t)?e:t}function h(e,t){for(var n=Object.create(null),r=e.split(","),i=0;i<r.length;i++)n[r[i]]=!0;return t?function(e){return n[e.toLowerCase()]}:function(e){return n[e]}}h("slot,component",!0);var v=h("key,ref,slot,slot-scope,is");function m(e,t){if(e.length){var n=e.indexOf(t);if(n>-1)return e.splice(n,1)}}var g=Object.prototype.hasOwnProperty;function y(e,t){return g.call(e,t)}function _(e){var t=Object.create(null);return function(n){var r=t[n];return r||(t[n]=e(n))}}var b=/-(\w)/g,w=_((function(e){return e.replace(b,(function(e,t){return t?t.toUpperCase():""}))})),k=_((function(e){return e.charAt(0).toUpperCase()+e.slice(1)})),x=/\B([A-Z])/g,O=_((function(e){return e.replace(x,"-$1").toLowerCase()}));var A=Function.prototype.bind?function(e,t){return e.bind(t)}:function(e,t){function n(n){var r=arguments.length;return r?r>1?e.apply(t,arguments):e.call(t,n):e.call(t)}return n._length=e.length,n};function S(e,t){t=t||0;var n=e.length-t,r=new Array(n);while(n--)r[n]=e[n+t];return r}function P(e,t){for(var n in t)e[n]=t[n];return e}function I(e){for(var t={},n=0;n<e.length;n++)e[n]&&P(t,e[n]);return t}function E(e,t,n){}var T=function(e,t,n){return!1},j=function(e){return e};function C(e,t){if(e===t)return!0;var n=s(e),r=s(t);if(!n||!r)return!n&&!r&&String(e)===String(t);try{var i=Array.isArray(e),o=Array.isArray(t);if(i&&o)return e.length===t.length&&e.every((function(e,n){return C(e,t[n])}));if(e instanceof Date&&t instanceof Date)return e.getTime()===t.getTime();if(i||o)return!1;var a=Object.keys(e),c=Object.keys(t);return a.length===c.length&&a.every((function(n){return C(e[n],t[n])}))}catch(u){return!1}}function L(e,t){for(var n=0;n<e.length;n++)if(C(e[n],t))return n;return-1}function $(e){var t=!1;return function(){t||(t=!0,e.apply(this,arguments))}}var D=["component","directive","filter"],N=["beforeCreate","created","beforeMount","mounted","beforeUpdate","updated","beforeDestroy","destroyed","activated","deactivated","errorCaptured","serverPrefetch"],R={optionMergeStrategies:Object.create(null),silent:!1,productionTip:!1,devtools:!1,performance:!1,errorHandler:null,warnHandler:null,ignoredElements:[],keyCodes:Object.create(null),isReservedTag:T,isReservedAttr:T,isUnknownElement:T,getTagNamespace:E,parsePlatformTagName:j,mustUseProp:T,async:!0,_lifecycleHooks:N},M=/a-zA-Z\u00B7\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u037D\u037F-\u1FFF\u200C-\u200D\u203F-\u2040\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD/;function U(e){var t=(e+"").charCodeAt(0);return 36===t||95===t}function F(e,t,n,r){Object.defineProperty(e,t,{value:n,enumerable:!!r,writable:!0,configurable:!0})}var q=new RegExp("[^"+M.source+".$_\\d]");var B,V="__proto__"in{},H="undefined"!==typeof window,K="undefined"!==typeof WXEnvironment&&!!WXEnvironment.platform,z=K&&WXEnvironment.platform.toLowerCase(),J=H&&window.navigator&&window.navigator.userAgent.toLowerCase(),W=J&&/msie|trident/.test(J),G=(J&&J.indexOf("msie 9.0"),J&&J.indexOf("edge/")>0),Y=(J&&J.indexOf("android"),J&&/iphone|ipad|ipod|ios/.test(J)||"ios"===z),Q=(J&&/chrome\/\d+/.test(J),J&&/phantomjs/.test(J),J&&J.match(/firefox\/(\d+)/),{}.watch);if(H)try{var X={};Object.defineProperty(X,"passive",{get:function(){}}),window.addEventListener("test-passive",null,X)}catch(Rn){}var Z=function(){return void 0===B&&(B=!H&&!K&&"undefined"!==typeof e&&(e["process"]&&"server"===e["process"].env.VUE_ENV)),B},ee=H&&window.__VUE_DEVTOOLS_GLOBAL_HOOK__;function te(e){return"function"===typeof e&&/native code/.test(e.toString())}var ne,re="undefined"!==typeof Symbol&&te(Symbol)&&"undefined"!==typeof Reflect&&te(Reflect.ownKeys);ne="undefined"!==typeof Set&&te(Set)?Set:function(){function e(){this.set=Object.create(null)}return e.prototype.has=function(e){return!0===this.set[e]},e.prototype.add=function(e){this.set[e]=!0},e.prototype.clear=function(){this.set=Object.create(null)},e}();var ie=E,oe=0,ae=function(){this.id=oe++,this.subs=[]};function se(e){ae.SharedObject.targetStack.push(e),ae.SharedObject.target=e,ae.target=e}function ce(){ae.SharedObject.targetStack.pop(),ae.SharedObject.target=ae.SharedObject.targetStack[ae.SharedObject.targetStack.length-1],ae.target=ae.SharedObject.target}ae.prototype.addSub=function(e){this.subs.push(e)},ae.prototype.removeSub=function(e){m(this.subs,e)},ae.prototype.depend=function(){ae.SharedObject.target&&ae.SharedObject.target.addDep(this)},ae.prototype.notify=function(){var e=this.subs.slice();for(var t=0,n=e.length;t<n;t++)e[t].update()},ae.SharedObject={},ae.SharedObject.target=null,ae.SharedObject.targetStack=[];var ue=function(e,t,n,r,i,o,a,s){this.tag=e,this.data=t,this.children=n,this.text=r,this.elm=i,this.ns=void 0,this.context=o,this.fnContext=void 0,this.fnOptions=void 0,this.fnScopeId=void 0,this.key=t&&t.key,this.componentOptions=a,this.componentInstance=void 0,this.parent=void 0,this.raw=!1,this.isStatic=!1,this.isRootInsert=!0,this.isComment=!1,this.isCloned=!1,this.isOnce=!1,this.asyncFactory=s,this.asyncMeta=void 0,this.isAsyncPlaceholder=!1},le={child:{configurable:!0}};le.child.get=function(){return this.componentInstance},Object.defineProperties(ue.prototype,le);var fe=function(e){void 0===e&&(e="");var t=new ue;return t.text=e,t.isComment=!0,t};function de(e){return new ue(void 0,void 0,void 0,String(e))}var pe=Array.prototype,he=Object.create(pe);["push","pop","shift","unshift","splice","sort","reverse"].forEach((function(e){var t=pe[e];F(he,e,(function(){var n=[],r=arguments.length;while(r--)n[r]=arguments[r];var i,o=t.apply(this,n),a=this.__ob__;switch(e){case"push":case"unshift":i=n;break;case"splice":i=n.slice(2);break}return i&&a.observeArray(i),a.dep.notify(),o}))}));var ve=Object.getOwnPropertyNames(he),me=!0;function ge(e){me=e}var ye=function(e){this.value=e,this.dep=new ae,this.vmCount=0,F(e,"__ob__",this),Array.isArray(e)?(V?e.push!==e.__proto__.push?_e(e,he,ve):function(e,t){e.__proto__=t}(e,he):_e(e,he,ve),this.observeArray(e)):this.walk(e)};function _e(e,t,n){for(var r=0,i=n.length;r<i;r++){var o=n[r];F(e,o,t[o])}}function be(e,t){var n;if(s(e)&&!(e instanceof ue))return y(e,"__ob__")&&e.__ob__ instanceof ye?n=e.__ob__:!me||Z()||!Array.isArray(e)&&!u(e)||!Object.isExtensible(e)||e._isVue||e.__v_isMPComponent||(n=new ye(e)),t&&n&&n.vmCount++,n}function we(e,t,n,r,i){var o=new ae,a=Object.getOwnPropertyDescriptor(e,t);if(!a||!1!==a.configurable){var s=a&&a.get,c=a&&a.set;s&&!c||2!==arguments.length||(n=e[t]);var u=!i&&be(n);Object.defineProperty(e,t,{enumerable:!0,configurable:!0,get:function(){var t=s?s.call(e):n;return ae.SharedObject.target&&(o.depend(),u&&(u.dep.depend(),Array.isArray(t)&&Oe(t))),t},set:function(t){var r=s?s.call(e):n;t===r||t!==t&&r!==r||s&&!c||(c?c.call(e,t):n=t,u=!i&&be(t),o.notify())}})}}function ke(e,t,n){if(Array.isArray(e)&&l(t))return e.length=Math.max(e.length,t),e.splice(t,1,n),n;if(t in e&&!(t in Object.prototype))return e[t]=n,n;var r=e.__ob__;return e._isVue||r&&r.vmCount?n:r?(we(r.value,t,n),r.dep.notify(),n):(e[t]=n,n)}function xe(e,t){if(Array.isArray(e)&&l(t))e.splice(t,1);else{var n=e.__ob__;e._isVue||n&&n.vmCount||y(e,t)&&(delete e[t],n&&n.dep.notify())}}function Oe(e){for(var t=void 0,n=0,r=e.length;n<r;n++)t=e[n],t&&t.__ob__&&t.__ob__.dep.depend(),Array.isArray(t)&&Oe(t)}ye.prototype.walk=function(e){for(var t=Object.keys(e),n=0;n<t.length;n++)we(e,t[n])},ye.prototype.observeArray=function(e){for(var t=0,n=e.length;t<n;t++)be(e[t])};var Ae=R.optionMergeStrategies;function Se(e,t){if(!t)return e;for(var n,r,i,o=re?Reflect.ownKeys(t):Object.keys(t),a=0;a<o.length;a++)n=o[a],"__ob__"!==n&&(r=e[n],i=t[n],y(e,n)?r!==i&&u(r)&&u(i)&&Se(r,i):ke(e,n,i));return e}function Pe(e,t,n){return n?function(){var r="function"===typeof t?t.call(n,n):t,i="function"===typeof e?e.call(n,n):e;return r?Se(r,i):i}:t?e?function(){return Se("function"===typeof t?t.call(this,this):t,"function"===typeof e?e.call(this,this):e)}:t:e}function Ie(e,t){var n=t?e?e.concat(t):Array.isArray(t)?t:[t]:e;return n?function(e){for(var t=[],n=0;n<e.length;n++)-1===t.indexOf(e[n])&&t.push(e[n]);return t}(n):n}function Ee(e,t,n,r){var i=Object.create(e||null);return t?P(i,t):i}Ae.data=function(e,t,n){return n?Pe(e,t,n):t&&"function"!==typeof t?e:Pe(e,t)},N.forEach((function(e){Ae[e]=Ie})),D.forEach((function(e){Ae[e+"s"]=Ee})),Ae.watch=function(e,t,n,r){if(e===Q&&(e=void 0),t===Q&&(t=void 0),!t)return Object.create(e||null);if(!e)return t;var i={};for(var o in P(i,e),t){var a=i[o],s=t[o];a&&!Array.isArray(a)&&(a=[a]),i[o]=a?a.concat(s):Array.isArray(s)?s:[s]}return i},Ae.props=Ae.methods=Ae.inject=Ae.computed=function(e,t,n,r){if(!e)return t;var i=Object.create(null);return P(i,e),t&&P(i,t),i},Ae.provide=Pe;var Te=function(e,t){return void 0===t?e:t};function je(e,t,n){if("function"===typeof t&&(t=t.options),function(e,t){var n=e.props;if(n){var r,i,o,a={};if(Array.isArray(n)){r=n.length;while(r--)i=n[r],"string"===typeof i&&(o=w(i),a[o]={type:null})}else if(u(n))for(var s in n)i=n[s],o=w(s),a[o]=u(i)?i:{type:i};else 0;e.props=a}}(t),function(e,t){var n=e.inject;if(n){var r=e.inject={};if(Array.isArray(n))for(var i=0;i<n.length;i++)r[n[i]]={from:n[i]};else if(u(n))for(var o in n){var a=n[o];r[o]=u(a)?P({from:o},a):{from:a}}else 0}}(t),function(e){var t=e.directives;if(t)for(var n in t){var r=t[n];"function"===typeof r&&(t[n]={bind:r,update:r})}}(t),!t._base&&(t.extends&&(e=je(e,t.extends,n)),t.mixins))for(var r=0,i=t.mixins.length;r<i;r++)e=je(e,t.mixins[r],n);var o,a={};for(o in e)s(o);for(o in t)y(e,o)||s(o);function s(r){var i=Ae[r]||Te;a[r]=i(e[r],t[r],n,r)}return a}function Ce(e,t,n,r){if("string"===typeof n){var i=e[t];if(y(i,n))return i[n];var o=w(n);if(y(i,o))return i[o];var a=k(o);if(y(i,a))return i[a];var s=i[n]||i[o]||i[a];return s}}function Le(e,t,n,r){var i=t[e],o=!y(n,e),a=n[e],s=Ne(Boolean,i.type);if(s>-1)if(o&&!y(i,"default"))a=!1;else if(""===a||a===O(e)){var c=Ne(String,i.type);(c<0||s<c)&&(a=!0)}if(void 0===a){a=function(e,t,n){if(!y(t,"default"))return;var r=t.default;0;if(e&&e.$options.propsData&&void 0===e.$options.propsData[n]&&void 0!==e._props[n])return e._props[n];return"function"===typeof r&&"Function"!==$e(t.type)?r.call(e):r}(r,i,e);var u=me;ge(!0),be(a),ge(u)}return a}function $e(e){var t=e&&e.toString().match(/^\s*function (\w+)/);return t?t[1]:""}function De(e,t){return $e(e)===$e(t)}function Ne(e,t){if(!Array.isArray(t))return De(t,e)?0:-1;for(var n=0,r=t.length;n<r;n++)if(De(t[n],e))return n;return-1}function Re(e,t,n){se();try{if(t){var r=t;while(r=r.$parent){var i=r.$options.errorCaptured;if(i)for(var o=0;o<i.length;o++)try{var a=!1===i[o].call(r,e,t,n);if(a)return}catch(Rn){Ue(Rn,r,"errorCaptured hook")}}}Ue(e,t,n)}finally{ce()}}function Me(e,t,n,r,i){var o;try{o=n?e.apply(t,n):e.call(t),o&&!o._isVue&&f(o)&&!o._handled&&(o.catch((function(e){return Re(e,r,i+" (Promise/async)")})),o._handled=!0)}catch(Rn){Re(Rn,r,i)}return o}function Ue(e,t,n){if(R.errorHandler)try{return R.errorHandler.call(null,e,t,n)}catch(Rn){Rn!==e&&Fe(Rn,null,"config.errorHandler")}Fe(e,t,n)}function Fe(e,t,n){if(!H&&!K||"undefined"===typeof console)throw e;console.error(e)}var qe,Be=[],Ve=!1;function He(){Ve=!1;var e=Be.slice(0);Be.length=0;for(var t=0;t<e.length;t++)e[t]()}if("undefined"!==typeof Promise&&te(Promise)){var Ke=Promise.resolve();qe=function(){Ke.then(He),Y&&setTimeout(E)}}else if(W||"undefined"===typeof MutationObserver||!te(MutationObserver)&&"[object MutationObserverConstructor]"!==MutationObserver.toString())qe="undefined"!==typeof setImmediate&&te(setImmediate)?function(){setImmediate(He)}:function(){setTimeout(He,0)};else{var ze=1,Je=new MutationObserver(He),We=document.createTextNode(String(ze));Je.observe(We,{characterData:!0}),qe=function(){ze=(ze+1)%2,We.data=String(ze)}}function Ge(e,t){var n;if(Be.push((function(){if(e)try{e.call(t)}catch(Rn){Re(Rn,t,"nextTick")}else n&&n(t)})),Ve||(Ve=!0,qe()),!e&&"undefined"!==typeof Promise)return new Promise((function(e){n=e}))}var Ye=new ne;function Qe(e){(function e(t,n){var r,i,o=Array.isArray(t);if(!o&&!s(t)||Object.isFrozen(t)||t instanceof ue)return;if(t.__ob__){var a=t.__ob__.dep.id;if(n.has(a))return;n.add(a)}if(o){r=t.length;while(r--)e(t[r],n)}else{i=Object.keys(t),r=i.length;while(r--)e(t[i[r]],n)}})(e,Ye),Ye.clear()}var Xe=_((function(e){var t="&"===e.charAt(0);e=t?e.slice(1):e;var n="~"===e.charAt(0);e=n?e.slice(1):e;var r="!"===e.charAt(0);return e=r?e.slice(1):e,{name:e,once:n,capture:r,passive:t}}));function Ze(e,t){function n(){var e=arguments,r=n.fns;if(!Array.isArray(r))return Me(r,null,arguments,t,"v-on handler");for(var i=r.slice(),o=0;o<i.length;o++)Me(i[o],null,e,t,"v-on handler")}return n.fns=e,n}function et(e,t,n,o){var a=t.options.mpOptions&&t.options.mpOptions.properties;if(r(a))return n;var s=t.options.mpOptions.externalClasses||[],c=e.attrs,u=e.props;if(i(c)||i(u))for(var l in a){var f=O(l),d=tt(n,u,l,f,!0)||tt(n,c,l,f,!1);d&&n[l]&&-1!==s.indexOf(f)&&o[w(n[l])]&&(n[l]=o[w(n[l])])}return n}function tt(e,t,n,r,o){if(i(t)){if(y(t,n))return e[n]=t[n],o||delete t[n],!0;if(y(t,r))return e[n]=t[r],o||delete t[r],!0}return!1}function nt(e){return a(e)?[de(e)]:Array.isArray(e)?function e(t,n){var s,c,u,l,f=[];for(s=0;s<t.length;s++)c=t[s],r(c)||"boolean"===typeof c||(u=f.length-1,l=f[u],Array.isArray(c)?c.length>0&&(c=e(c,(n||"")+"_"+s),rt(c[0])&&rt(l)&&(f[u]=de(l.text+c[0].text),c.shift()),f.push.apply(f,c)):a(c)?rt(l)?f[u]=de(l.text+c):""!==c&&f.push(de(c)):rt(c)&&rt(l)?f[u]=de(l.text+c.text):(o(t._isVList)&&i(c.tag)&&r(c.key)&&i(n)&&(c.key="__vlist"+n+"_"+s+"__"),f.push(c)));return f}(e):void 0}function rt(e){return i(e)&&i(e.text)&&function(e){return!1===e}(e.isComment)}function it(e){var t=e.$options.provide;t&&(e._provided="function"===typeof t?t.call(e):t)}function ot(e){var t=at(e.$options.inject,e);t&&(ge(!1),Object.keys(t).forEach((function(n){we(e,n,t[n])})),ge(!0))}function at(e,t){if(e){for(var n=Object.create(null),r=re?Reflect.ownKeys(e):Object.keys(e),i=0;i<r.length;i++){var o=r[i];if("__ob__"!==o){var a=e[o].from,s=t;while(s){if(s._provided&&y(s._provided,a)){n[o]=s._provided[a];break}s=s.$parent}if(!s)if("default"in e[o]){var c=e[o].default;n[o]="function"===typeof c?c.call(t):c}else 0}}return n}}function st(e,t){if(!e||!e.length)return{};for(var n={},r=0,i=e.length;r<i;r++){var o=e[r],a=o.data;if(a&&a.attrs&&a.attrs.slot&&delete a.attrs.slot,o.context!==t&&o.fnContext!==t||!a||null==a.slot)o.asyncMeta&&o.asyncMeta.data&&"page"===o.asyncMeta.data.slot?(n["page"]||(n["page"]=[])).push(o):(n.default||(n.default=[])).push(o);else{var s=a.slot,c=n[s]||(n[s]=[]);"template"===o.tag?c.push.apply(c,o.children||[]):c.push(o)}}for(var u in n)n[u].every(ct)&&delete n[u];return n}function ct(e){return e.isComment&&!e.asyncFactory||" "===e.text}function ut(e,t,r){var i,o=Object.keys(t).length>0,a=e?!!e.$stable:!o,s=e&&e.$key;if(e){if(e._normalized)return e._normalized;if(a&&r&&r!==n&&s===r.$key&&!o&&!r.$hasNormal)return r;for(var c in i={},e)e[c]&&"$"!==c[0]&&(i[c]=lt(t,c,e[c]))}else i={};for(var u in t)u in i||(i[u]=ft(t,u));return e&&Object.isExtensible(e)&&(e._normalized=i),F(i,"$stable",a),F(i,"$key",s),F(i,"$hasNormal",o),i}function lt(e,t,n){var r=function(){var e=arguments.length?n.apply(null,arguments):n({});return e=e&&"object"===typeof e&&!Array.isArray(e)?[e]:nt(e),e&&(0===e.length||1===e.length&&e[0].isComment)?void 0:e};return n.proxy&&Object.defineProperty(e,t,{get:r,enumerable:!0,configurable:!0}),r}function ft(e,t){return function(){return e[t]}}function dt(e,t){var n,r,o,a,c;if(Array.isArray(e)||"string"===typeof e)for(n=new Array(e.length),r=0,o=e.length;r<o;r++)n[r]=t(e[r],r,r,r);else if("number"===typeof e)for(n=new Array(e),r=0;r<e;r++)n[r]=t(r+1,r,r,r);else if(s(e))if(re&&e[Symbol.iterator]){n=[];var u=e[Symbol.iterator](),l=u.next();while(!l.done)n.push(t(l.value,n.length,r,r++)),l=u.next()}else for(a=Object.keys(e),n=new Array(a.length),r=0,o=a.length;r<o;r++)c=a[r],n[r]=t(e[c],c,r,r);return i(n)||(n=[]),n._isVList=!0,n}function pt(e,t,n,r){var i,o=this.$scopedSlots[e];o?(n=n||{},r&&(n=P(P({},r),n)),i=o(n,this,n._i)||t):i=this.$slots[e]||t;var a=n&&n.slot;return a?this.$createElement("template",{slot:a},i):i}function ht(e){return Ce(this.$options,"filters",e)||j}function vt(e,t){return Array.isArray(e)?-1===e.indexOf(t):e!==t}function mt(e,t,n,r,i){var o=R.keyCodes[t]||n;return i&&r&&!R.keyCodes[t]?vt(i,r):o?vt(o,e):r?O(r)!==t:void 0}function gt(e,t,n,r,i){if(n)if(s(n)){var o;Array.isArray(n)&&(n=I(n));var a=function(a){if("class"===a||"style"===a||v(a))o=e;else{var s=e.attrs&&e.attrs.type;o=r||R.mustUseProp(t,s,a)?e.domProps||(e.domProps={}):e.attrs||(e.attrs={})}var c=w(a),u=O(a);if(!(c in o)&&!(u in o)&&(o[a]=n[a],i)){var l=e.on||(e.on={});l["update:"+a]=function(e){n[a]=e}}};for(var c in n)a(c)}else;return e}function yt(e,t){var n=this._staticTrees||(this._staticTrees=[]),r=n[e];return r&&!t||(r=n[e]=this.$options.staticRenderFns[e].call(this._renderProxy,null,this),bt(r,"__static__"+e,!1)),r}function _t(e,t,n){return bt(e,"__once__"+t+(n?"_"+n:""),!0),e}function bt(e,t,n){if(Array.isArray(e))for(var r=0;r<e.length;r++)e[r]&&"string"!==typeof e[r]&&wt(e[r],t+"_"+r,n);else wt(e,t,n)}function wt(e,t,n){e.isStatic=!0,e.key=t,e.isOnce=n}function kt(e,t){if(t)if(u(t)){var n=e.on=e.on?P({},e.on):{};for(var r in t){var i=n[r],o=t[r];n[r]=i?[].concat(i,o):o}}else;return e}function xt(e,t,n,r){t=t||{$stable:!n};for(var i=0;i<e.length;i++){var o=e[i];Array.isArray(o)?xt(o,t,n):o&&(o.proxy&&(o.fn.proxy=!0),t[o.key]=o.fn)}return r&&(t.$key=r),t}function Ot(e,t){for(var n=0;n<t.length;n+=2){var r=t[n];"string"===typeof r&&r&&(e[t[n]]=t[n+1])}return e}function At(e,t){return"string"===typeof e?t+e:e}function St(e){e._o=_t,e._n=p,e._s=d,e._l=dt,e._t=pt,e._q=C,e._i=L,e._m=yt,e._f=ht,e._k=mt,e._b=gt,e._v=de,e._e=fe,e._u=xt,e._g=kt,e._d=Ot,e._p=At}function Pt(e,t,r,i,a){var s,c=this,u=a.options;y(i,"_uid")?(s=Object.create(i),s._original=i):(s=i,i=i._original);var l=o(u._compiled),f=!l;this.data=e,this.props=t,this.children=r,this.parent=i,this.listeners=e.on||n,this.injections=at(u.inject,i),this.slots=function(){return c.$slots||ut(e.scopedSlots,c.$slots=st(r,i)),c.$slots},Object.defineProperty(this,"scopedSlots",{enumerable:!0,get:function(){return ut(e.scopedSlots,this.slots())}}),l&&(this.$options=u,this.$slots=this.slots(),this.$scopedSlots=ut(e.scopedSlots,this.$slots)),u._scopeId?this._c=function(e,t,n,r){var o=$t(s,e,t,n,r,f);return o&&!Array.isArray(o)&&(o.fnScopeId=u._scopeId,o.fnContext=i),o}:this._c=function(e,t,n,r){return $t(s,e,t,n,r,f)}}function It(e,t,n,r,i){var o=function(e){var t=new ue(e.tag,e.data,e.children&&e.children.slice(),e.text,e.elm,e.context,e.componentOptions,e.asyncFactory);return t.ns=e.ns,t.isStatic=e.isStatic,t.key=e.key,t.isComment=e.isComment,t.fnContext=e.fnContext,t.fnOptions=e.fnOptions,t.fnScopeId=e.fnScopeId,t.asyncMeta=e.asyncMeta,t.isCloned=!0,t}(e);return o.fnContext=n,o.fnOptions=r,t.slot&&((o.data||(o.data={})).slot=t.slot),o}function Et(e,t){for(var n in t)e[w(n)]=t[n]}St(Pt.prototype);var Tt={init:function(e,t){if(e.componentInstance&&!e.componentInstance._isDestroyed&&e.data.keepAlive){var n=e;Tt.prepatch(n,n)}else{var r=e.componentInstance=function(e,t){var n={_isComponent:!0,_parentVnode:e,parent:t},r=e.data.inlineTemplate;i(r)&&(n.render=r.render,n.staticRenderFns=r.staticRenderFns);return new e.componentOptions.Ctor(n)}(e,Vt);r.$mount(t?e.elm:void 0,t)}},prepatch:function(e,t){var r=t.componentOptions,i=t.componentInstance=e.componentInstance;(function(e,t,r,i,o){0;var a=i.data.scopedSlots,s=e.$scopedSlots,c=!!(a&&!a.$stable||s!==n&&!s.$stable||a&&e.$scopedSlots.$key!==a.$key),u=!!(o||e.$options._renderChildren||c);e.$options._parentVnode=i,e.$vnode=i,e._vnode&&(e._vnode.parent=i);if(e.$options._renderChildren=o,e.$attrs=i.data.attrs||n,e.$listeners=r||n,t&&e.$options.props){ge(!1);for(var l=e._props,f=e.$options._propKeys||[],d=0;d<f.length;d++){var p=f[d],h=e.$options.props;l[p]=Le(p,h,t,e)}ge(!0),e.$options.propsData=t}e._$updateProperties&&e._$updateProperties(e),r=r||n;var v=e.$options._parentListeners;e.$options._parentListeners=r,Bt(e,r,v),u&&(e.$slots=st(o,i.context),e.$forceUpdate());0})(i,r.propsData,r.listeners,t,r.children)},insert:function(e){var t=e.context,n=e.componentInstance;n._isMounted||(zt(n,"onServiceCreated"),zt(n,"onServiceAttached"),n._isMounted=!0,zt(n,"mounted")),e.data.keepAlive&&(t._isMounted?function(e){e._inactive=!1,Wt.push(e)}(n):Kt(n,!0))},destroy:function(e){var t=e.componentInstance;t._isDestroyed||(e.data.keepAlive?function e(t,n){if(n&&(t._directInactive=!0,Ht(t)))return;if(!t._inactive){t._inactive=!0;for(var r=0;r<t.$children.length;r++)e(t.$children[r]);zt(t,"deactivated")}}(t,!0):t.$destroy())}},jt=Object.keys(Tt);function Ct(e,t,a,c,u){if(!r(e)){var l=a.$options._base;if(s(e)&&(e=l.extend(e)),"function"===typeof e){var d;if(r(e.cid)&&(d=e,e=function(e,t){if(o(e.error)&&i(e.errorComp))return e.errorComp;if(i(e.resolved))return e.resolved;var n=Nt;n&&i(e.owners)&&-1===e.owners.indexOf(n)&&e.owners.push(n);if(o(e.loading)&&i(e.loadingComp))return e.loadingComp;if(n&&!i(e.owners)){var a=e.owners=[n],c=!0,u=null,l=null;n.$on("hook:destroyed",(function(){return m(a,n)}));var d=function(e){for(var t=0,n=a.length;t<n;t++)a[t].$forceUpdate();e&&(a.length=0,null!==u&&(clearTimeout(u),u=null),null!==l&&(clearTimeout(l),l=null))},p=$((function(n){e.resolved=Rt(n,t),c?a.length=0:d(!0)})),h=$((function(t){i(e.errorComp)&&(e.error=!0,d(!0))})),v=e(p,h);return s(v)&&(f(v)?r(e.resolved)&&v.then(p,h):f(v.component)&&(v.component.then(p,h),i(v.error)&&(e.errorComp=Rt(v.error,t)),i(v.loading)&&(e.loadingComp=Rt(v.loading,t),0===v.delay?e.loading=!0:u=setTimeout((function(){u=null,r(e.resolved)&&r(e.error)&&(e.loading=!0,d(!1))}),v.delay||200)),i(v.timeout)&&(l=setTimeout((function(){l=null,r(e.resolved)&&h(null)}),v.timeout)))),c=!1,e.loading?e.loadingComp:e.resolved}}(d,l),void 0===e))return function(e,t,n,r,i){var o=fe();return o.asyncFactory=e,o.asyncMeta={data:t,context:n,children:r,tag:i},o}(d,t,a,c,u);t=t||{},hn(e),i(t.model)&&function(e,t){var n=e.model&&e.model.prop||"value",r=e.model&&e.model.event||"input";(t.attrs||(t.attrs={}))[n]=t.model.value;var o=t.on||(t.on={}),a=o[r],s=t.model.callback;i(a)?(Array.isArray(a)?-1===a.indexOf(s):a!==s)&&(o[r]=[s].concat(a)):o[r]=s}(e.options,t);var p=function(e,t,n,o){var a=t.options.props;if(r(a))return et(e,t,{},o);var s={},c=e.attrs,u=e.props;if(i(c)||i(u))for(var l in a){var f=O(l);tt(s,u,l,f,!0)||tt(s,c,l,f,!1)}return et(e,t,s,o)}(t,e,0,a);if(o(e.options.functional))return function(e,t,r,o,a){var s=e.options,c={},u=s.props;if(i(u))for(var l in u)c[l]=Le(l,u,t||n);else i(r.attrs)&&Et(c,r.attrs),i(r.props)&&Et(c,r.props);var f=new Pt(r,c,a,o,e),d=s.render.call(null,f._c,f);if(d instanceof ue)return It(d,r,f.parent,s,f);if(Array.isArray(d)){for(var p=nt(d)||[],h=new Array(p.length),v=0;v<p.length;v++)h[v]=It(p[v],r,f.parent,s,f);return h}}(e,p,t,a,c);var h=t.on;if(t.on=t.nativeOn,o(e.options.abstract)){var v=t.slot;t={},v&&(t.slot=v)}(function(e){for(var t=e.hook||(e.hook={}),n=0;n<jt.length;n++){var r=jt[n],i=t[r],o=Tt[r];i===o||i&&i._merged||(t[r]=i?Lt(o,i):o)}})(t);var g=e.options.name||u,y=new ue("vue-component-"+e.cid+(g?"-"+g:""),t,void 0,void 0,void 0,a,{Ctor:e,propsData:p,listeners:h,tag:u,children:c},d);return y}}}function Lt(e,t){var n=function(n,r){e(n,r),t(n,r)};return n._merged=!0,n}function $t(e,t,n,c,u,l){return(Array.isArray(n)||a(n))&&(u=c,c=n,n=void 0),o(l)&&(u=2),function(e,t,n,a,c){if(i(n)&&i(n.__ob__))return fe();i(n)&&i(n.is)&&(t=n.is);if(!t)return fe();0;Array.isArray(a)&&"function"===typeof a[0]&&(n=n||{},n.scopedSlots={default:a[0]},a.length=0);2===c?a=nt(a):1===c&&(a=function(e){for(var t=0;t<e.length;t++)if(Array.isArray(e[t]))return Array.prototype.concat.apply([],e);return e}(a));var u,l;if("string"===typeof t){var f;l=e.$vnode&&e.$vnode.ns||R.getTagNamespace(t),u=R.isReservedTag(t)?new ue(R.parsePlatformTagName(t),n,a,void 0,void 0,e):n&&n.pre||!i(f=Ce(e.$options,"components",t))?new ue(t,n,a,void 0,void 0,e):Ct(f,n,e,a,t)}else u=Ct(t,n,e,a);return Array.isArray(u)?u:i(u)?(i(l)&&function e(t,n,a){t.ns=n,"foreignObject"===t.tag&&(n=void 0,a=!0);if(i(t.children))for(var s=0,c=t.children.length;s<c;s++){var u=t.children[s];i(u.tag)&&(r(u.ns)||o(a)&&"svg"!==u.tag)&&e(u,n,a)}}(u,l),i(n)&&function(e){s(e.style)&&Qe(e.style);s(e.class)&&Qe(e.class)}(n),u):fe()}(e,t,n,c,u)}var Dt,Nt=null;function Rt(e,t){return(e.__esModule||re&&"Module"===e[Symbol.toStringTag])&&(e=e.default),s(e)?t.extend(e):e}function Mt(e){return e.isComment&&e.asyncFactory}function Ut(e,t){Dt.$on(e,t)}function Ft(e,t){Dt.$off(e,t)}function qt(e,t){var n=Dt;return function r(){var i=t.apply(null,arguments);null!==i&&n.$off(e,r)}}function Bt(e,t,n){Dt=e,function(e,t,n,i,a,s){var c,u,l,f;for(c in e)u=e[c],l=t[c],f=Xe(c),r(u)||(r(l)?(r(u.fns)&&(u=e[c]=Ze(u,s)),o(f.once)&&(u=e[c]=a(f.name,u,f.capture)),n(f.name,u,f.capture,f.passive,f.params)):u!==l&&(l.fns=u,e[c]=l));for(c in t)r(e[c])&&(f=Xe(c),i(f.name,t[c],f.capture))}(t,n||{},Ut,Ft,qt,e),Dt=void 0}var Vt=null;function Ht(e){while(e&&(e=e.$parent))if(e._inactive)return!0;return!1}function Kt(e,t){if(t){if(e._directInactive=!1,Ht(e))return}else if(e._directInactive)return;if(e._inactive||null===e._inactive){e._inactive=!1;for(var n=0;n<e.$children.length;n++)Kt(e.$children[n]);zt(e,"activated")}}function zt(e,t){se();var n=e.$options[t],r=t+" hook";if(n)for(var i=0,o=n.length;i<o;i++)Me(n[i],e,null,e,r);e._hasHookEvent&&e.$emit("hook:"+t),ce()}var Jt=[],Wt=[],Gt={},Yt=!1,Qt=!1,Xt=0;var Zt=Date.now;if(H&&!W){var en=window.performance;en&&"function"===typeof en.now&&Zt()>document.createEvent("Event").timeStamp&&(Zt=function(){return en.now()})}function tn(){var e,t;for(Zt(),Qt=!0,Jt.sort((function(e,t){return e.id-t.id})),Xt=0;Xt<Jt.length;Xt++)e=Jt[Xt],e.before&&e.before(),t=e.id,Gt[t]=null,e.run();var n=Wt.slice(),r=Jt.slice();(function(){Xt=Jt.length=Wt.length=0,Gt={},Yt=Qt=!1})(),function(e){for(var t=0;t<e.length;t++)e[t]._inactive=!0,Kt(e[t],!0)}(n),function(e){var t=e.length;while(t--){var n=e[t],r=n.vm;r._watcher===n&&r._isMounted&&!r._isDestroyed&&zt(r,"updated")}}(r),ee&&R.devtools&&ee.emit("flush")}var nn=0,rn=function(e,t,n,r,i){this.vm=e,i&&(e._watcher=this),e._watchers.push(this),r?(this.deep=!!r.deep,this.user=!!r.user,this.lazy=!!r.lazy,this.sync=!!r.sync,this.before=r.before):this.deep=this.user=this.lazy=this.sync=!1,this.cb=n,this.id=++nn,this.active=!0,this.dirty=this.lazy,this.deps=[],this.newDeps=[],this.depIds=new ne,this.newDepIds=new ne,this.expression="","function"===typeof t?this.getter=t:(this.getter=function(e){if(!q.test(e)){var t=e.split(".");return function(e){for(var n=0;n<t.length;n++){if(!e)return;e=e[t[n]]}return e}}}(t),this.getter||(this.getter=E)),this.value=this.lazy?void 0:this.get()};rn.prototype.get=function(){var e;se(this);var t=this.vm;try{e=this.getter.call(t,t)}catch(Rn){if(!this.user)throw Rn;Re(Rn,t,'getter for watcher "'+this.expression+'"')}finally{this.deep&&Qe(e),ce(),this.cleanupDeps()}return e},rn.prototype.addDep=function(e){var t=e.id;this.newDepIds.has(t)||(this.newDepIds.add(t),this.newDeps.push(e),this.depIds.has(t)||e.addSub(this))},rn.prototype.cleanupDeps=function(){var e=this.deps.length;while(e--){var t=this.deps[e];this.newDepIds.has(t.id)||t.removeSub(this)}var n=this.depIds;this.depIds=this.newDepIds,this.newDepIds=n,this.newDepIds.clear(),n=this.deps,this.deps=this.newDeps,this.newDeps=n,this.newDeps.length=0},rn.prototype.update=function(){this.lazy?this.dirty=!0:this.sync?this.run():function(e){var t=e.id;if(null==Gt[t]){if(Gt[t]=!0,Qt){var n=Jt.length-1;while(n>Xt&&Jt[n].id>e.id)n--;Jt.splice(n+1,0,e)}else Jt.push(e);Yt||(Yt=!0,Ge(tn))}}(this)},rn.prototype.run=function(){if(this.active){var e=this.get();if(e!==this.value||s(e)||this.deep){var t=this.value;if(this.value=e,this.user)try{this.cb.call(this.vm,e,t)}catch(Rn){Re(Rn,this.vm,'callback for watcher "'+this.expression+'"')}else this.cb.call(this.vm,e,t)}}},rn.prototype.evaluate=function(){this.value=this.get(),this.dirty=!1},rn.prototype.depend=function(){var e=this.deps.length;while(e--)this.deps[e].depend()},rn.prototype.teardown=function(){if(this.active){this.vm._isBeingDestroyed||m(this.vm._watchers,this);var e=this.deps.length;while(e--)this.deps[e].removeSub(this);this.active=!1}};var on={enumerable:!0,configurable:!0,get:E,set:E};function an(e,t,n){on.get=function(){return this[t][n]},on.set=function(e){this[t][n]=e},Object.defineProperty(e,n,on)}function sn(e){e._watchers=[];var t=e.$options;t.props&&function(e,t){var n=e.$options.propsData||{},r=e._props={},i=e.$options._propKeys=[],o=!e.$parent;o||ge(!1);var a=function(o){i.push(o);var a=Le(o,t,n,e);we(r,o,a),o in e||an(e,"_props",o)};for(var s in t)a(s);ge(!0)}(e,t.props),t.methods&&function(e,t){e.$options.props;for(var n in t)e[n]="function"!==typeof t[n]?E:A(t[n],e)}(e,t.methods),t.data?function(e){var t=e.$options.data;t=e._data="function"===typeof t?function(e,t){se();try{return e.call(t,t)}catch(Rn){return Re(Rn,t,"data()"),{}}finally{ce()}}(t,e):t||{},u(t)||(t={});var n=Object.keys(t),r=e.$options.props,i=(e.$options.methods,n.length);while(i--){var o=n[i];0,r&&y(r,o)||U(o)||an(e,"_data",o)}be(t,!0)}(e):be(e._data={},!0),t.computed&&function(e,t){var n=e._computedWatchers=Object.create(null),r=Z();for(var i in t){var o=t[i],a="function"===typeof o?o:o.get;0,r||(n[i]=new rn(e,a||E,E,cn)),i in e||un(e,i,o)}}(e,t.computed),t.watch&&t.watch!==Q&&function(e,t){for(var n in t){var r=t[n];if(Array.isArray(r))for(var i=0;i<r.length;i++)dn(e,n,r[i]);else dn(e,n,r)}}(e,t.watch)}var cn={lazy:!0};function un(e,t,n){var r=!Z();"function"===typeof n?(on.get=r?ln(t):fn(n),on.set=E):(on.get=n.get?r&&!1!==n.cache?ln(t):fn(n.get):E,on.set=n.set||E),Object.defineProperty(e,t,on)}function ln(e){return function(){var t=this._computedWatchers&&this._computedWatchers[e];if(t)return t.dirty&&t.evaluate(),ae.SharedObject.target&&t.depend(),t.value}}function fn(e){return function(){return e.call(this,this)}}function dn(e,t,n,r){return u(n)&&(r=n,n=n.handler),"string"===typeof n&&(n=e[n]),e.$watch(t,n,r)}var pn=0;function hn(e){var t=e.options;if(e.super){var n=hn(e.super),r=e.superOptions;if(n!==r){e.superOptions=n;var i=function(e){var t,n=e.options,r=e.sealedOptions;for(var i in n)n[i]!==r[i]&&(t||(t={}),t[i]=n[i]);return t}(e);i&&P(e.extendOptions,i),t=e.options=je(n,e.extendOptions),t.name&&(t.components[t.name]=e)}}return t}function vn(e){this._init(e)}function mn(e){e.cid=0;var t=1;e.extend=function(e){e=e||{};var n=this,r=n.cid,i=e._Ctor||(e._Ctor={});if(i[r])return i[r];var o=e.name||n.options.name;var a=function(e){this._init(e)};return a.prototype=Object.create(n.prototype),a.prototype.constructor=a,a.cid=t++,a.options=je(n.options,e),a["super"]=n,a.options.props&&function(e){var t=e.options.props;for(var n in t)an(e.prototype,"_props",n)}(a),a.options.computed&&function(e){var t=e.options.computed;for(var n in t)un(e.prototype,n,t[n])}(a),a.extend=n.extend,a.mixin=n.mixin,a.use=n.use,D.forEach((function(e){a[e]=n[e]})),o&&(a.options.components[o]=a),a.superOptions=n.options,a.extendOptions=e,a.sealedOptions=P({},a.options),i[r]=a,a}}function gn(e){return e&&(e.Ctor.options.name||e.tag)}function yn(e,t){return Array.isArray(e)?e.indexOf(t)>-1:"string"===typeof e?e.split(",").indexOf(t)>-1:!!function(e){return"[object RegExp]"===c.call(e)}(e)&&e.test(t)}function _n(e,t){var n=e.cache,r=e.keys,i=e._vnode;for(var o in n){var a=n[o];if(a){var s=gn(a.componentOptions);s&&!t(s)&&bn(n,o,r,i)}}}function bn(e,t,n,r){var i=e[t];!i||r&&i.tag===r.tag||i.componentInstance.$destroy(),e[t]=null,m(n,t)}(function(e){e.prototype._init=function(e){var t=this;t._uid=pn++,t._isVue=!0,e&&e._isComponent?function(e,t){var n=e.$options=Object.create(e.constructor.options),r=t._parentVnode;n.parent=t.parent,n._parentVnode=r;var i=r.componentOptions;n.propsData=i.propsData,n._parentListeners=i.listeners,n._renderChildren=i.children,n._componentTag=i.tag,t.render&&(n.render=t.render,n.staticRenderFns=t.staticRenderFns)}(t,e):t.$options=je(hn(t.constructor),e||{},t),t._renderProxy=t,t._self=t,function(e){var t=e.$options,n=t.parent;if(n&&!t.abstract){while(n.$options.abstract&&n.$parent)n=n.$parent;n.$children.push(e)}e.$parent=n,e.$root=n?n.$root:e,e.$children=[],e.$refs={},e._watcher=null,e._inactive=null,e._directInactive=!1,e._isMounted=!1,e._isDestroyed=!1,e._isBeingDestroyed=!1}(t),function(e){e._events=Object.create(null),e._hasHookEvent=!1;var t=e.$options._parentListeners;t&&Bt(e,t)}(t),function(e){e._vnode=null,e._staticTrees=null;var t=e.$options,r=e.$vnode=t._parentVnode,i=r&&r.context;e.$slots=st(t._renderChildren,i),e.$scopedSlots=n,e._c=function(t,n,r,i){return $t(e,t,n,r,i,!1)},e.$createElement=function(t,n,r,i){return $t(e,t,n,r,i,!0)};var o=r&&r.data;we(e,"$attrs",o&&o.attrs||n,null,!0),we(e,"$listeners",t._parentListeners||n,null,!0)}(t),zt(t,"beforeCreate"),!t._$fallback&&ot(t),sn(t),!t._$fallback&&it(t),!t._$fallback&&zt(t,"created"),t.$options.el&&t.$mount(t.$options.el)}})(vn),function(e){var t={get:function(){return this._data}},n={get:function(){return this._props}};Object.defineProperty(e.prototype,"$data",t),Object.defineProperty(e.prototype,"$props",n),e.prototype.$set=ke,e.prototype.$delete=xe,e.prototype.$watch=function(e,t,n){if(u(t))return dn(this,e,t,n);n=n||{},n.user=!0;var r=new rn(this,e,t,n);if(n.immediate)try{t.call(this,r.value)}catch(i){Re(i,this,'callback for immediate watcher "'+r.expression+'"')}return function(){r.teardown()}}}(vn),function(e){var t=/^hook:/;e.prototype.$on=function(e,n){var r=this;if(Array.isArray(e))for(var i=0,o=e.length;i<o;i++)r.$on(e[i],n);else(r._events[e]||(r._events[e]=[])).push(n),t.test(e)&&(r._hasHookEvent=!0);return r},e.prototype.$once=function(e,t){var n=this;function r(){n.$off(e,r),t.apply(n,arguments)}return r.fn=t,n.$on(e,r),n},e.prototype.$off=function(e,t){var n=this;if(!arguments.length)return n._events=Object.create(null),n;if(Array.isArray(e)){for(var r=0,i=e.length;r<i;r++)n.$off(e[r],t);return n}var o,a=n._events[e];if(!a)return n;if(!t)return n._events[e]=null,n;var s=a.length;while(s--)if(o=a[s],o===t||o.fn===t){a.splice(s,1);break}return n},e.prototype.$emit=function(e){var t=this,n=t._events[e];if(n){n=n.length>1?S(n):n;for(var r=S(arguments,1),i='event handler for "'+e+'"',o=0,a=n.length;o<a;o++)Me(n[o],t,r,t,i)}return t}}(vn),function(e){e.prototype._update=function(e,t){var n=this,r=n.$el,i=n._vnode,o=function(e){var t=Vt;return Vt=e,function(){Vt=t}}(n);n._vnode=e,n.$el=i?n.__patch__(i,e):n.__patch__(n.$el,e,t,!1),o(),r&&(r.__vue__=null),n.$el&&(n.$el.__vue__=n),n.$vnode&&n.$parent&&n.$vnode===n.$parent._vnode&&(n.$parent.$el=n.$el)},e.prototype.$forceUpdate=function(){this._watcher&&this._watcher.update()},e.prototype.$destroy=function(){var e=this;if(!e._isBeingDestroyed){zt(e,"beforeDestroy"),e._isBeingDestroyed=!0;var t=e.$parent;!t||t._isBeingDestroyed||e.$options.abstract||m(t.$children,e),e._watcher&&e._watcher.teardown();var n=e._watchers.length;while(n--)e._watchers[n].teardown();e._data.__ob__&&e._data.__ob__.vmCount--,e._isDestroyed=!0,e.__patch__(e._vnode,null),zt(e,"destroyed"),e.$off(),e.$el&&(e.$el.__vue__=null),e.$vnode&&(e.$vnode.parent=null)}}}(vn),function(e){St(e.prototype),e.prototype.$nextTick=function(e){return Ge(e,this)},e.prototype._render=function(){var e,t=this,n=t.$options,r=n.render,i=n._parentVnode;i&&(t.$scopedSlots=ut(i.data.scopedSlots,t.$slots,t.$scopedSlots)),t.$vnode=i;try{Nt=t,e=r.call(t._renderProxy,t.$createElement)}catch(Rn){Re(Rn,t,"render"),e=t._vnode}finally{Nt=null}return Array.isArray(e)&&1===e.length&&(e=e[0]),e instanceof ue||(e=fe()),e.parent=i,e}}(vn);var wn=[String,RegExp,Array],kn={name:"keep-alive",abstract:!0,props:{include:wn,exclude:wn,max:[String,Number]},created:function(){this.cache=Object.create(null),this.keys=[]},destroyed:function(){for(var e in this.cache)bn(this.cache,e,this.keys)},mounted:function(){var e=this;this.$watch("include",(function(t){_n(e,(function(e){return yn(t,e)}))})),this.$watch("exclude",(function(t){_n(e,(function(e){return!yn(t,e)}))}))},render:function(){var e=this.$slots.default,t=function(e){if(Array.isArray(e))for(var t=0;t<e.length;t++){var n=e[t];if(i(n)&&(i(n.componentOptions)||Mt(n)))return n}}(e),n=t&&t.componentOptions;if(n){var r=gn(n),o=this.include,a=this.exclude;if(o&&(!r||!yn(o,r))||a&&r&&yn(a,r))return t;var s=this.cache,c=this.keys,u=null==t.key?n.Ctor.cid+(n.tag?"::"+n.tag:""):t.key;s[u]?(t.componentInstance=s[u].componentInstance,m(c,u),c.push(u)):(s[u]=t,c.push(u),this.max&&c.length>parseInt(this.max)&&bn(s,c[0],c,this._vnode)),t.data.keepAlive=!0}return t||e&&e[0]}},xn={KeepAlive:kn};(function(e){var t={get:function(){return R}};Object.defineProperty(e,"config",t),e.util={warn:ie,extend:P,mergeOptions:je,defineReactive:we},e.set=ke,e.delete=xe,e.nextTick=Ge,e.observable=function(e){return be(e),e},e.options=Object.create(null),D.forEach((function(t){e.options[t+"s"]=Object.create(null)})),e.options._base=e,P(e.options.components,xn),function(e){e.use=function(e){var t=this._installedPlugins||(this._installedPlugins=[]);if(t.indexOf(e)>-1)return this;var n=S(arguments,1);return n.unshift(this),"function"===typeof e.install?e.install.apply(e,n):"function"===typeof e&&e.apply(null,n),t.push(e),this}}(e),function(e){e.mixin=function(e){return this.options=je(this.options,e),this}}(e),mn(e),function(e){D.forEach((function(t){e[t]=function(e,n){return n?("component"===t&&u(n)&&(n.name=n.name||e,n=this.options._base.extend(n)),"directive"===t&&"function"===typeof n&&(n={bind:n,update:n}),this.options[t+"s"][e]=n,n):this.options[t+"s"][e]}}))}(e)})(vn),Object.defineProperty(vn.prototype,"$isServer",{get:Z}),Object.defineProperty(vn.prototype,"$ssrContext",{get:function(){return this.$vnode&&this.$vnode.ssrContext}}),Object.defineProperty(vn,"FunctionalRenderContext",{value:Pt}),vn.version="2.6.11";var On="[object Array]",An="[object Object]";function Sn(e,t){var n={};return function e(t,n){if(t===n)return;var r=In(t),i=In(n);if(r==An&&i==An){if(Object.keys(t).length>=Object.keys(n).length)for(var o in n){var a=t[o];void 0===a?t[o]=null:e(a,n[o])}}else r==On&&i==On&&t.length>=n.length&&n.forEach((function(n,r){e(t[r],n)}))}(e,t),function e(t,n,r,i){if(t===n)return;var o=In(t),a=In(n);if(o==An)if(a!=An||Object.keys(t).length<Object.keys(n).length)Pn(i,r,t);else{var s=function(o){var a=t[o],s=n[o],c=In(a),u=In(s);if(c!=On&&c!=An)a!==n[o]&&function(e,t){if(("[object Null]"===e||"[object Undefined]"===e)&&("[object Null]"===t||"[object Undefined]"===t))return!1;return!0}(c,u)&&Pn(i,(""==r?"":r+".")+o,a);else if(c==On)u!=On||a.length<s.length?Pn(i,(""==r?"":r+".")+o,a):a.forEach((function(t,n){e(t,s[n],(""==r?"":r+".")+o+"["+n+"]",i)}));else if(c==An)if(u!=An||Object.keys(a).length<Object.keys(s).length)Pn(i,(""==r?"":r+".")+o,a);else for(var l in a)e(a[l],s[l],(""==r?"":r+".")+o+"."+l,i)};for(var c in t)s(c)}else o==On?a!=On||t.length<n.length?Pn(i,r,t):t.forEach((function(t,o){e(t,n[o],r+"["+o+"]",i)})):Pn(i,r,t)}(e,t,"",n),n}function Pn(e,t,n){e[t]=n}function In(e){return Object.prototype.toString.call(e)}function En(e){if(e.__next_tick_callbacks&&e.__next_tick_callbacks.length){if(Object({NODE_ENV:"production",VUE_APP_DARK_MODE:"false",VUE_APP_NAME:"租房小程序",VUE_APP_PLATFORM:"mp-weixin",BASE_URL:"/"}).VUE_APP_DEBUG){var t=e.$scope;console.log("["+ +new Date+"]["+(t.is||t.route)+"]["+e._uid+"]:flushCallbacks["+e.__next_tick_callbacks.length+"]")}var n=e.__next_tick_callbacks.slice(0);e.__next_tick_callbacks.length=0;for(var r=0;r<n.length;r++)n[r]()}}function Tn(e,t){if(!e.__next_tick_pending&&!function(e){return Jt.find((function(t){return e._watcher===t}))}(e)){if(Object({NODE_ENV:"production",VUE_APP_DARK_MODE:"false",VUE_APP_NAME:"租房小程序",VUE_APP_PLATFORM:"mp-weixin",BASE_URL:"/"}).VUE_APP_DEBUG){var n=e.$scope;console.log("["+ +new Date+"]["+(n.is||n.route)+"]["+e._uid+"]:nextVueTick")}return Ge(t,e)}if(Object({NODE_ENV:"production",VUE_APP_DARK_MODE:"false",VUE_APP_NAME:"租房小程序",VUE_APP_PLATFORM:"mp-weixin",BASE_URL:"/"}).VUE_APP_DEBUG){var r=e.$scope;console.log("["+ +new Date+"]["+(r.is||r.route)+"]["+e._uid+"]:nextMPTick")}var i;if(e.__next_tick_callbacks||(e.__next_tick_callbacks=[]),e.__next_tick_callbacks.push((function(){if(t)try{t.call(e)}catch(Rn){Re(Rn,e,"nextTick")}else i&&i(e)})),!t&&"undefined"!==typeof Promise)return new Promise((function(e){i=e}))}function jn(e,t){return t&&(t._isVue||t.__v_isMPComponent)?{}:t}function Cn(){}function Ln(e){return Array.isArray(e)?function(e){for(var t,n="",r=0,o=e.length;r<o;r++)i(t=Ln(e[r]))&&""!==t&&(n&&(n+=" "),n+=t);return n}(e):s(e)?function(e){var t="";for(var n in e)e[n]&&(t&&(t+=" "),t+=n);return t}(e):"string"===typeof e?e:""}var $n=_((function(e){var t={},n=/:(.+)/;return e.split(/;(?![^(]*\))/g).forEach((function(e){if(e){var r=e.split(n);r.length>1&&(t[r[0].trim()]=r[1].trim())}})),t}));var Dn=["createSelectorQuery","createIntersectionObserver","selectAllComponents","selectComponent"];var Nn=["onLaunch","onShow","onHide","onUniNViewMessage","onPageNotFound","onThemeChange","onError","onUnhandledRejection","onInit","onLoad","onReady","onUnload","onPullDownRefresh","onReachBottom","onTabItemTap","onAddToFavorites","onShareTimeline","onShareAppMessage","onResize","onPageScroll","onNavigationBarButtonTap","onBackPress","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputClicked","onUploadDouyinVideo","onNFCReadMessage","onPageShow","onPageHide","onPageResize"];vn.prototype.__patch__=function(e,t){var n=this;if(null!==t&&("page"===this.mpType||"component"===this.mpType)){var r=this.$scope,i=Object.create(null);try{i=function(e){var t=Object.create(null),n=[].concat(Object.keys(e._data||{}),Object.keys(e._computedWatchers||{}));n.reduce((function(t,n){return t[n]=e[n],t}),t);var r=e.__composition_api_state__||e.__secret_vfa_state__,i=r&&r.rawBindings;return i&&Object.keys(i).forEach((function(n){t[n]=e[n]})),Object.assign(t,e.$mp.data||{}),Array.isArray(e.$options.behaviors)&&-1!==e.$options.behaviors.indexOf("uni://form-field")&&(t["name"]=e.name,t["value"]=e.value),JSON.parse(JSON.stringify(t,jn))}(this)}catch(s){console.error(s)}i.__webviewId__=r.data.__webviewId__;var o=Object.create(null);Object.keys(i).forEach((function(e){o[e]=r.data[e]}));var a=!1===this.$shouldDiffData?i:Sn(i,o);Object.keys(a).length?(Object({NODE_ENV:"production",VUE_APP_DARK_MODE:"false",VUE_APP_NAME:"租房小程序",VUE_APP_PLATFORM:"mp-weixin",BASE_URL:"/"}).VUE_APP_DEBUG&&console.log("["+ +new Date+"]["+(r.is||r.route)+"]["+this._uid+"]差量更新",JSON.stringify(a)),this.__next_tick_pending=!0,r.setData(a,(function(){n.__next_tick_pending=!1,En(n)}))):En(this)}},vn.prototype.$mount=function(e,t){return function(e,t,n){return e.mpType?("app"===e.mpType&&(e.$options.render=Cn),e.$options.render||(e.$options.render=Cn),!e._$fallback&&zt(e,"beforeMount"),new rn(e,(function(){e._update(e._render(),n)}),E,{before:function(){e._isMounted&&!e._isDestroyed&&zt(e,"beforeUpdate")}},!0),n=!1,e):e}(this,0,t)},function(e){var t=e.extend;e.extend=function(e){e=e||{};var n=e.methods;return n&&Object.keys(n).forEach((function(t){-1!==Nn.indexOf(t)&&(e[t]=n[t],delete n[t])})),t.call(this,e)};var n=e.config.optionMergeStrategies,r=n.created;Nn.forEach((function(e){n[e]=r})),e.prototype.__lifecycle_hooks__=Nn}(vn),function(e){e.config.errorHandler=function(t,n,r){e.util.warn("Error in "+r+': "'+t.toString()+'"',n),console.error(t);var i="function"===typeof getApp&&getApp();i&&i.onError&&i.onError(t)};var t=e.prototype.$emit;e.prototype.$emit=function(e){if(this.$scope&&e){var n=this.$scope["_triggerEvent"]||this.$scope["triggerEvent"];if(n)try{n.call(this.$scope,e,{__args__:S(arguments,1)})}catch(r){}}return t.apply(this,arguments)},e.prototype.$nextTick=function(e){return Tn(this,e)},Dn.forEach((function(t){e.prototype[t]=function(e){return this.$scope&&this.$scope[t]?this.$scope[t](e):"undefined"!==typeof my?"createSelectorQuery"===t?my.createSelectorQuery(e):"createIntersectionObserver"===t?my.createIntersectionObserver(e):void 0:void 0}})),e.prototype.__init_provide=it,e.prototype.__init_injections=ot,e.prototype.__call_hook=function(e,t){var n=this;se();var r,i=n.$options[e],o=e+" hook";if(i)for(var a=0,s=i.length;a<s;a++)r=Me(i[a],n,t?[t]:null,n,o);return n._hasHookEvent&&n.$emit("hook:"+e,t),ce(),r},e.prototype.__set_model=function(t,n,r,i){Array.isArray(i)&&(-1!==i.indexOf("trim")&&(r=r.trim()),-1!==i.indexOf("number")&&(r=this._n(r))),t||(t=this),e.set(t,n,r)},e.prototype.__set_sync=function(t,n,r){t||(t=this),e.set(t,n,r)},e.prototype.__get_orig=function(e){return u(e)&&e["$orig"]||e},e.prototype.__get_value=function(e,t){return function e(t,n){var r=n.split("."),i=r[0];return 0===i.indexOf("__$n")&&(i=parseInt(i.replace("__$n",""))),1===r.length?t[i]:e(t[i],r.slice(1).join("."))}(t||this,e)},e.prototype.__get_class=function(e,t){return function(e,t){return i(e)||i(t)?function(e,t){return e?t?e+" "+t:e:t||""}(e,Ln(t)):""}(t,e)},e.prototype.__get_style=function(e,t){if(!e&&!t)return"";var n=function(e){return Array.isArray(e)?I(e):"string"===typeof e?$n(e):e}(e),r=t?P(t,n):n;return Object.keys(r).map((function(e){return O(e)+":"+r[e]})).join(";")},e.prototype.__map=function(e,t){var n,r,i,o,a;if(Array.isArray(e)){for(n=new Array(e.length),r=0,i=e.length;r<i;r++)n[r]=t(e[r],r);return n}if(s(e)){for(o=Object.keys(e),n=Object.create(null),r=0,i=o.length;r<i;r++)a=o[r],n[a]=t(e[a],a,r);return n}if("number"===typeof e){for(n=new Array(e),r=0,i=e;r<i;r++)n[r]=t(r,r);return n}return[]}}(vn),t["default"]=vn}.call(this,n("0ee4"))},3352:function(e,t){e.exports=function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e},e.exports.__esModule=!0,e.exports["default"]=e.exports},"34cf":function(e,t,n){var r=n("ed45"),i=n("7172"),o=n("6382"),a=n("dd3e");e.exports=function(e,t){return r(e)||i(e,t)||o(e,t)||a()},e.exports.__esModule=!0,e.exports["default"]=e.exports},"3a0f":function(e){e.exports=JSON.parse('{"uni-popup.cancel":"取消","uni-popup.ok":"確定","uni-popup.placeholder":"請輸入","uni-popup.title":"提示","uni-popup.shareTitle":"分享到"}')},"3b2d":function(e,t){function n(t){return e.exports=n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports["default"]=e.exports,n(t)}e.exports=n,e.exports.__esModule=!0,e.exports["default"]=e.exports},"432d":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r={data:function(){return{}},created:function(){this.popup=this.getParent()},methods:{getParent:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"uniPopup",t=this.$parent,n=t.$options.name;while(n!==e){if(t=t.$parent,!t)return!1;n=t.$options.name}return t}}};t.default=r},"46ca":function(e,t,n){"use strict";var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.typeFilter=t.type=t.setDataValue=t.realName=t.rawData=t.objSet=t.objGet=t.name2arr=t.isRequiredField=t.isRealName=t.isNumber=t.isEqual=t.isBoolean=t.getValue=t.getDataValueType=t.getDataValue=t.deepCopy=void 0;var i=r(n("3b2d"));t.deepCopy=function(e){return JSON.parse(JSON.stringify(e))};var o=function(e){return"int"===e||"double"===e||"number"===e||"timestamp"===e};t.typeFilter=o;t.getValue=function(e,t,n){var r=n.find((function(e){return e.format&&o(e.format)})),i=n.find((function(e){return e.format&&"boolean"===e.format||"bool"===e.format}));return r&&(t=t||0===t?f(Number(t))?Number(t):t:null),i&&(t=!!d(t)&&t),t};t.setDataValue=function(e,t,n){return t[e]=n,n||""};var a=function(e,t){return l(t,e)};t.getDataValue=a;t.getDataValueType=function(e,t){var n=a(e,t);return{type:p(n),value:n}};t.realName=function(e){var t=u(e);if("object"===(0,i.default)(t)&&Array.isArray(t)&&t.length>1){var n=t.reduce((function(e,t){return e+"#".concat(t)}),"_formdata_");return n}return t[0]||e};t.isRealName=function(e){return/^_formdata_#*/.test(e)};t.rawData=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=JSON.parse(JSON.stringify(e)),n={};for(var r in t){var i=s(r);c(n,i,t[r])}return n};var s=function(e){var t=e.replace("_formdata_#","");return t=t.split("#").map((function(e){return f(e)?Number(e):e})),t};t.name2arr=s;var c=function(e,t,n){return"object"!==(0,i.default)(e)||u(t).reduce((function(e,t,r,i){return r===i.length-1?(e[t]=n,null):(t in e||(e[t]=/^[0-9]{1,}$/.test(i[r+1])?[]:{}),e[t])}),e),e};function u(e){return Array.isArray(e)?e:e.replace(/\[/g,".").replace(/\]/g,"").split(".")}t.objSet=c;var l=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"undefined",r=u(t),i=r.reduce((function(e,t){return(e||{})[t]}),e);return i&&void 0===i?n:i};t.objGet=l;var f=function(e){return!isNaN(Number(e))};t.isNumber=f;var d=function(e){return"boolean"===typeof e};t.isBoolean=d;t.isRequiredField=function(e){for(var t=!1,n=0;n<e.length;n++){var r=e[n];if(r.required){t=!0;break}}return t};var p=function(e){var t={};return"Boolean Number String Function Array Date RegExp Object Error".split(" ").map((function(e,n){t["[object "+e+"]"]=e.toLowerCase()})),null==e?e+"":"object"===(0,i.default)(e)||"function"===typeof e?t[Object.prototype.toString.call(e)]||"object":(0,i.default)(e)};t.type=p;t.isEqual=function(e,t){if(e===t)return 0!==e||1/e===1/t;if(null==e||null==t)return e===t;var n=toString.call(e),r=toString.call(t);if(n!==r)return!1;switch(n){case"[object RegExp]":case"[object String]":return""+e===""+t;case"[object Number]":return+e!==+e?+t!==+t:0===+e?1/+e===1/t:+e===+t;case"[object Date]":case"[object Boolean]":return+e===+t}if("[object Object]"==n){var i=Object.getOwnPropertyNames(e),o=Object.getOwnPropertyNames(t);if(i.length!=o.length)return!1;for(var a=0;a<i.length;a++){var s=i[a];if(e[s]!==t[s])return!1}return!0}return"[object Array]"==n?e.toString()==t.toString():void 0}},"47a9":function(e,t){e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports["default"]=e.exports},4965:function(e,t){e.exports=function(e){try{return-1!==Function.toString.call(e).indexOf("[native code]")}catch(t){return"function"===typeof e}},e.exports.__esModule=!0,e.exports["default"]=e.exports},"4db8":function(e,t,n){"use strict";var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=r(n("8708")),o=i.default.passwordStrength,a={super:/^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[~!@#$%^&*_\-+=`|\\(){}[\]:;"'<>,.?/])[0-9a-zA-Z~!@#$%^&*_\-+=`|\\(){}[\]:;"'<>,.?/]{8,16}$/,strong:/^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[~!@#$%^&*_\-+=`|\\(){}[\]:;"'<>,.?/])[0-9a-zA-Z~!@#$%^&*_\-+=`|\\(){}[\]:;"'<>,.?/]{8,16}$/,medium:/^(?![0-9]+$)(?![a-zA-Z]+$)(?![~!@#$%^&*_\-+=`|\\(){}[\]:;"'<>,.?/]+$)[0-9a-zA-Z~!@#$%^&*_\-+=`|\\(){}[\]:;"'<>,.?/]{8,16}$/,weak:/^(?=.*[0-9])(?=.*[a-zA-Z])[0-9a-zA-Z~!@#$%^&*_\-+=`|\\(){}[\]:;"'<>,.?/]{6,16}$/},s={normal:{noPwd:"请输入密码",noRePwd:"再次输入密码",rePwdErr:"两次输入密码不一致"},passwordStrengthError:{super:"密码必须包含大小写字母、数字和特殊符号，密码长度必须在8-16位之间",strong:"密码必须包含字母、数字和特殊符号，密码长度必须在8-16位之间",medium:"密码必须为字母、数字和特殊符号任意两种的组合，密码长度必须在8-16位之间",weak:"密码必须包含字母，密码长度必须在6-16位之间"}};function c(e){return!(o&&a[o]&&!new RegExp(a[o]).test(e))||s.passwordStrengthError[o]}var u={ERROR:s,validPwd:c,getPwdRules:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"password",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"password2",n={};return n[e]={rules:[{required:!0,errorMessage:s.normal.noPwd},{validateFunction:function(e,t,n,r){var i=c(t);return!0!==i&&r(i),!0}}]},t&&(n[t]={rules:[{required:!0,errorMessage:s.normal.noRePwd},{validateFunction:function(t,n,r,i){return n!=r[e]&&i(s.normal.rePwdErr),!0}}]}),n}};t.default=u},"4ffb":function(e,t,n){var r=n("3b2d")["default"],i=n("3352");e.exports=function(e,t){if(t&&("object"===r(t)||"function"===typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return i(e)},e.exports.__esModule=!0,e.exports["default"]=e.exports},6382:function(e,t,n){var r=n("6454");e.exports=function(e,t){if(e){if("string"===typeof e)return r(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(e,t):void 0}},e.exports.__esModule=!0,e.exports["default"]=e.exports},6454:function(e,t){e.exports=function(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r},e.exports.__esModule=!0,e.exports["default"]=e.exports},"67ad":function(e,t){e.exports=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports["default"]=e.exports},"67cf":function(e,t){e.exports=function(e,t){if(null==e)return{};var n,r,i={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(i[n]=e[n]);return i},e.exports.__esModule=!0,e.exports["default"]=e.exports},"6f83":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=function(e){if(!e||"string"!==typeof e||18!==e.length)return!1;for(var t=[7,9,10,5,8,4,2,1,6,3,7,9,10,5,8,4,2],n=e.substring(17),r=0,i=0;i<17;i++)r+=Number(e.charAt(i))*t[i];return[1,0,"x",9,8,7,6,5,4,3,2][r%11].toString()===n.toLowerCase()};t.default=r},7172:function(e,t){e.exports=function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o,a,s=[],c=!0,u=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(s.push(r.value),s.length!==t);c=!0);}catch(e){u=!0,i=e}finally{try{if(!c&&null!=n["return"]&&(a=n["return"](),Object(a)!==a))return}finally{if(u)throw i}}return s}},e.exports.__esModule=!0,e.exports["default"]=e.exports},7647:function(e,t){function n(t,r){return e.exports=n=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},e.exports.__esModule=!0,e.exports["default"]=e.exports,n(t,r)}e.exports=n,e.exports.__esModule=!0,e.exports["default"]=e.exports},"7b83":function(e){e.exports=JSON.parse('{"uni-popup.cancel":"取消","uni-popup.ok":"确定","uni-popup.placeholder":"请输入","uni-popup.title":"提示","uni-popup.shareTitle":"分享到"}')},"7ca3":function(e,t,n){var r=n("d551");e.exports=function(e,t,n){return t=r(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},e.exports.__esModule=!0,e.exports["default"]=e.exports},"7ce1":function(e,t,n){var r=n("b4d2"),i=n("7647"),o=n("4965"),a=n("931d");function s(t){var n="function"===typeof Map?new Map:void 0;return e.exports=s=function(e){if(null===e||!o(e))return e;if("function"!==typeof e)throw new TypeError("Super expression must either be null or a function");if("undefined"!==typeof n){if(n.has(e))return n.get(e);n.set(e,t)}function t(){return a(e,arguments,r(this).constructor)}return t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),i(t,e)},e.exports.__esModule=!0,e.exports["default"]=e.exports,s(t)}e.exports=s,e.exports.__esModule=!0,e.exports["default"]=e.exports},"7eb4":function(e,t,n){var r=n("9fc1")();e.exports=r},"828b":function(e,t,n){"use strict";function r(e,t,n,r,i,o,a,s,c,u){var l,f="function"===typeof e?e.options:e;if(c){f.components||(f.components={});var d=Object.prototype.hasOwnProperty;for(var p in c)d.call(c,p)&&!d.call(f.components,p)&&(f.components[p]=c[p])}if(u&&("function"===typeof u.beforeCreate&&(u.beforeCreate=[u.beforeCreate]),(u.beforeCreate||(u.beforeCreate=[])).unshift((function(){this[u.__module]=this})),(f.mixins||(f.mixins=[])).push(u)),t&&(f.render=t,f.staticRenderFns=n,f._compiled=!0),r&&(f.functional=!0),o&&(f._scopeId="data-v-"+o),a?(l=function(e){e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,e||"undefined"===typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),i&&i.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(a)},f._ssrRegister=l):i&&(l=s?function(){i.call(this,this.$root.$options.shadowRoot)}:i),l)if(f.functional){f._injectStyles=l;var h=f.render;f.render=function(e,t){return l.call(t),h(e,t)}}else{var v=f.beforeCreate;f.beforeCreate=v?[].concat(v,l):[l]}return{exports:e,options:f}}n.d(t,"a",(function(){return r}))},"861b":function(e,t,n){"use strict";(function(e,r,i){var o=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.uniCloud=t.default=t.UniCloudError=void 0;var a=o(n("7eb4")),s=o(n("3352")),c=o(n("34cf")),u=o(n("3b2d")),l=o(n("af34")),f=o(n("ee10")),d=o(n("7ca3")),p=o(n("8ffa")),h=o(n("4ffb")),v=o(n("b4d2")),m=o(n("7ce1")),g=o(n("67ad")),y=o(n("0bdb")),_=o(n("23ba"));function b(e,t){var n="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"===typeof e)return w(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return w(e,t)}(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function w(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function k(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function x(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?k(Object(n),!0).forEach((function(t){(0,d.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):k(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function O(e){var t=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=(0,v.default)(e);if(t){var i=(0,v.default)(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return(0,h.default)(this,n)}}function A(e,t,n){return e(n={path:t,exports:{},require:function(e,t){return function(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}(null==t&&n.path)}},n.exports),n.exports}"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof e||"undefined"!=typeof self&&self;var S=A((function(e,t){var n;e.exports=(n=n||function(e,t){var n=Object.create||function(){function e(){}return function(t){var n;return e.prototype=t,n=new e,e.prototype=null,n}}(),r={},i=r.lib={},o=i.Base={extend:function(e){var t=n(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},a=i.WordArray=o.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=void 0!=t?t:4*e.length},toString:function(e){return(e||c).stringify(this)},concat:function(e){var t=this.words,n=e.words,r=this.sigBytes,i=e.sigBytes;if(this.clamp(),r%4)for(var o=0;o<i;o++){var a=n[o>>>2]>>>24-o%4*8&255;t[r+o>>>2]|=a<<24-(r+o)%4*8}else for(o=0;o<i;o+=4)t[r+o>>>2]=n[o>>>2];return this.sigBytes+=i,this},clamp:function(){var t=this.words,n=this.sigBytes;t[n>>>2]&=4294967295<<32-n%4*8,t.length=e.ceil(n/4)},clone:function(){var e=o.clone.call(this);return e.words=this.words.slice(0),e},random:function(t){for(var n,r=[],i=function(t){var n=987654321,r=4294967295;return function(){var i=((n=36969*(65535&n)+(n>>16)&r)<<16)+(t=18e3*(65535&t)+(t>>16)&r)&r;return i/=4294967296,(i+=.5)*(e.random()>.5?1:-1)}},o=0;o<t;o+=4){var s=i(4294967296*(n||e.random()));n=987654071*s(),r.push(4294967296*s()|0)}return new a.init(r,t)}}),s=r.enc={},c=s.Hex={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],i=0;i<n;i++){var o=t[i>>>2]>>>24-i%4*8&255;r.push((o>>>4).toString(16)),r.push((15&o).toString(16))}return r.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r+=2)n[r>>>3]|=parseInt(e.substr(r,2),16)<<24-r%8*4;return new a.init(n,t/2)}},u=s.Latin1={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],i=0;i<n;i++){var o=t[i>>>2]>>>24-i%4*8&255;r.push(String.fromCharCode(o))}return r.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r++)n[r>>>2]|=(255&e.charCodeAt(r))<<24-r%4*8;return new a.init(n,t)}},l=s.Utf8={stringify:function(e){try{return decodeURIComponent(escape(u.stringify(e)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(e){return u.parse(unescape(encodeURIComponent(e)))}},f=i.BufferedBlockAlgorithm=o.extend({reset:function(){this._data=new a.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=l.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var n=this._data,r=n.words,i=n.sigBytes,o=this.blockSize,s=i/(4*o),c=(s=t?e.ceil(s):e.max((0|s)-this._minBufferSize,0))*o,u=e.min(4*c,i);if(c){for(var l=0;l<c;l+=o)this._doProcessBlock(r,l);var f=r.splice(0,c);n.sigBytes-=u}return new a.init(f,u)},clone:function(){var e=o.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});i.Hasher=f.extend({cfg:o.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){f.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,n){return new e.init(n).finalize(t)}},_createHmacHelper:function(e){return function(t,n){return new d.HMAC.init(e,n).finalize(t)}}});var d=r.algo={};return r}(Math),n)})),P=S,I=(A((function(e,t){var n;e.exports=(n=P,function(e){var t=n,r=t.lib,i=r.WordArray,o=r.Hasher,a=t.algo,s=[];!function(){for(var t=0;t<64;t++)s[t]=4294967296*e.abs(e.sin(t+1))|0}();var c=a.MD5=o.extend({_doReset:function(){this._hash=new i.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var n=0;n<16;n++){var r=t+n,i=e[r];e[r]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8)}var o=this._hash.words,a=e[t+0],c=e[t+1],p=e[t+2],h=e[t+3],v=e[t+4],m=e[t+5],g=e[t+6],y=e[t+7],_=e[t+8],b=e[t+9],w=e[t+10],k=e[t+11],x=e[t+12],O=e[t+13],A=e[t+14],S=e[t+15],P=o[0],I=o[1],E=o[2],T=o[3];P=u(P,I,E,T,a,7,s[0]),T=u(T,P,I,E,c,12,s[1]),E=u(E,T,P,I,p,17,s[2]),I=u(I,E,T,P,h,22,s[3]),P=u(P,I,E,T,v,7,s[4]),T=u(T,P,I,E,m,12,s[5]),E=u(E,T,P,I,g,17,s[6]),I=u(I,E,T,P,y,22,s[7]),P=u(P,I,E,T,_,7,s[8]),T=u(T,P,I,E,b,12,s[9]),E=u(E,T,P,I,w,17,s[10]),I=u(I,E,T,P,k,22,s[11]),P=u(P,I,E,T,x,7,s[12]),T=u(T,P,I,E,O,12,s[13]),E=u(E,T,P,I,A,17,s[14]),P=l(P,I=u(I,E,T,P,S,22,s[15]),E,T,c,5,s[16]),T=l(T,P,I,E,g,9,s[17]),E=l(E,T,P,I,k,14,s[18]),I=l(I,E,T,P,a,20,s[19]),P=l(P,I,E,T,m,5,s[20]),T=l(T,P,I,E,w,9,s[21]),E=l(E,T,P,I,S,14,s[22]),I=l(I,E,T,P,v,20,s[23]),P=l(P,I,E,T,b,5,s[24]),T=l(T,P,I,E,A,9,s[25]),E=l(E,T,P,I,h,14,s[26]),I=l(I,E,T,P,_,20,s[27]),P=l(P,I,E,T,O,5,s[28]),T=l(T,P,I,E,p,9,s[29]),E=l(E,T,P,I,y,14,s[30]),P=f(P,I=l(I,E,T,P,x,20,s[31]),E,T,m,4,s[32]),T=f(T,P,I,E,_,11,s[33]),E=f(E,T,P,I,k,16,s[34]),I=f(I,E,T,P,A,23,s[35]),P=f(P,I,E,T,c,4,s[36]),T=f(T,P,I,E,v,11,s[37]),E=f(E,T,P,I,y,16,s[38]),I=f(I,E,T,P,w,23,s[39]),P=f(P,I,E,T,O,4,s[40]),T=f(T,P,I,E,a,11,s[41]),E=f(E,T,P,I,h,16,s[42]),I=f(I,E,T,P,g,23,s[43]),P=f(P,I,E,T,b,4,s[44]),T=f(T,P,I,E,x,11,s[45]),E=f(E,T,P,I,S,16,s[46]),P=d(P,I=f(I,E,T,P,p,23,s[47]),E,T,a,6,s[48]),T=d(T,P,I,E,y,10,s[49]),E=d(E,T,P,I,A,15,s[50]),I=d(I,E,T,P,m,21,s[51]),P=d(P,I,E,T,x,6,s[52]),T=d(T,P,I,E,h,10,s[53]),E=d(E,T,P,I,w,15,s[54]),I=d(I,E,T,P,c,21,s[55]),P=d(P,I,E,T,_,6,s[56]),T=d(T,P,I,E,S,10,s[57]),E=d(E,T,P,I,g,15,s[58]),I=d(I,E,T,P,O,21,s[59]),P=d(P,I,E,T,v,6,s[60]),T=d(T,P,I,E,k,10,s[61]),E=d(E,T,P,I,p,15,s[62]),I=d(I,E,T,P,b,21,s[63]),o[0]=o[0]+P|0,o[1]=o[1]+I|0,o[2]=o[2]+E|0,o[3]=o[3]+T|0},_doFinalize:function(){var t=this._data,n=t.words,r=8*this._nDataBytes,i=8*t.sigBytes;n[i>>>5]|=128<<24-i%32;var o=e.floor(r/4294967296),a=r;n[15+(i+64>>>9<<4)]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),n[14+(i+64>>>9<<4)]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),t.sigBytes=4*(n.length+1),this._process();for(var s=this._hash,c=s.words,u=0;u<4;u++){var l=c[u];c[u]=16711935&(l<<8|l>>>24)|4278255360&(l<<24|l>>>8)}return s},clone:function(){var e=o.clone.call(this);return e._hash=this._hash.clone(),e}});function u(e,t,n,r,i,o,a){var s=e+(t&n|~t&r)+i+a;return(s<<o|s>>>32-o)+t}function l(e,t,n,r,i,o,a){var s=e+(t&r|n&~r)+i+a;return(s<<o|s>>>32-o)+t}function f(e,t,n,r,i,o,a){var s=e+(t^n^r)+i+a;return(s<<o|s>>>32-o)+t}function d(e,t,n,r,i,o,a){var s=e+(n^(t|~r))+i+a;return(s<<o|s>>>32-o)+t}t.MD5=o._createHelper(c),t.HmacMD5=o._createHmacHelper(c)}(Math),n.MD5)})),A((function(e,t){var n;e.exports=(n=P,void function(){var e=n,t=e.lib.Base,r=e.enc.Utf8;e.algo.HMAC=t.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=r.parse(t));var n=e.blockSize,i=4*n;t.sigBytes>i&&(t=e.finalize(t)),t.clamp();for(var o=this._oKey=t.clone(),a=this._iKey=t.clone(),s=o.words,c=a.words,u=0;u<n;u++)s[u]^=1549556828,c[u]^=909522486;o.sigBytes=a.sigBytes=i,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,n=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(n))}})}())})),A((function(e,t){e.exports=P.HmacMD5}))),E=A((function(e,t){e.exports=P.enc.Utf8})),T=A((function(e,t){var n;e.exports=(n=P,function(){var e=n,t=e.lib.WordArray;function r(e,n,r){for(var i=[],o=0,a=0;a<n;a++)if(a%4){var s=r[e.charCodeAt(a-1)]<<a%4*2,c=r[e.charCodeAt(a)]>>>6-a%4*2;i[o>>>2]|=(s|c)<<24-o%4*8,o++}return t.create(i,o)}e.enc.Base64={stringify:function(e){var t=e.words,n=e.sigBytes,r=this._map;e.clamp();for(var i=[],o=0;o<n;o+=3)for(var a=(t[o>>>2]>>>24-o%4*8&255)<<16|(t[o+1>>>2]>>>24-(o+1)%4*8&255)<<8|t[o+2>>>2]>>>24-(o+2)%4*8&255,s=0;s<4&&o+.75*s<n;s++)i.push(r.charAt(a>>>6*(3-s)&63));var c=r.charAt(64);if(c)for(;i.length%4;)i.push(c);return i.join("")},parse:function(e){var t=e.length,n=this._map,i=this._reverseMap;if(!i){i=this._reverseMap=[];for(var o=0;o<n.length;o++)i[n.charCodeAt(o)]=o}var a=n.charAt(64);if(a){var s=e.indexOf(a);-1!==s&&(t=s)}return r(e,t,i)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}}(),n.enc.Base64)})),j="uni_id_token",C="uni_id_token_expired",L={DEFAULT:"FUNCTION",FUNCTION:"FUNCTION",OBJECT:"OBJECT",CLIENT_DB:"CLIENT_DB"},$="pending",D="fulfilled",N="rejected";function R(e){return Object.prototype.toString.call(e).slice(8,-1).toLowerCase()}function M(e){return"object"===R(e)}function U(e){return"function"==typeof e}function F(e){return function(){try{return e.apply(e,arguments)}catch(e){console.error(e)}}}var q="REJECTED",B="NOT_PENDING",V=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.createPromise,r=t.retryRule,i=void 0===r?q:r;(0,g.default)(this,e),this.createPromise=n,this.status=null,this.promise=null,this.retryRule=i}return(0,y.default)(e,[{key:"needRetry",get:function(){if(!this.status)return!0;switch(this.retryRule){case q:return this.status===N;case B:return this.status!==$}}},{key:"exec",value:function(){var e=this;return this.needRetry?(this.status=$,this.promise=this.createPromise().then((function(t){return e.status=D,Promise.resolve(t)}),(function(t){return e.status=N,Promise.reject(t)})),this.promise):this.promise}}]),e}(),H=function(){function e(){(0,g.default)(this,e),this._callback={}}return(0,y.default)(e,[{key:"addListener",value:function(e,t){this._callback[e]||(this._callback[e]=[]),this._callback[e].push(t)}},{key:"on",value:function(e,t){return this.addListener(e,t)}},{key:"removeListener",value:function(e,t){if(!t)throw new Error('The "listener" argument must be of type function. Received undefined');var n=this._callback[e];if(n){var r=function(e,t){for(var n=e.length-1;n>=0;n--)if(e[n]===t)return n;return-1}(n,t);n.splice(r,1)}}},{key:"off",value:function(e,t){return this.removeListener(e,t)}},{key:"removeAllListener",value:function(e){delete this._callback[e]}},{key:"emit",value:function(e){for(var t=this._callback[e],n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];if(t)for(var o=0;o<t.length;o++)t[o].apply(t,r)}}]),e}();function K(e){return e&&"string"==typeof e?JSON.parse(e):e}var z="mp-weixin",J=K([]),W=z,G=(K(void 0),K([{provider:"aliyun",spaceName:"zwj",spaceId:"mp-b90e944b-9431-47d6-84b0-4716b32b1374",clientSecret:"WTcCQcDGsDDXkhobrrWDFg==",endpoint:"https://api.next.bspapp.com"}])||[]);try{(n("d080").default||n("d080")).appid}catch(Pr){}var Y,Q={};function X(e){var t,n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return t=Q,n=e,Object.prototype.hasOwnProperty.call(t,n)||(Q[e]=r),Q[e]}"app"===W&&(Q=r._globalUniCloudObj?r._globalUniCloudObj:r._globalUniCloudObj={});var Z=["invoke","success","fail","complete"],ee=X("_globalUniCloudInterceptor");function te(e,t){ee[e]||(ee[e]={}),M(t)&&Object.keys(t).forEach((function(n){Z.indexOf(n)>-1&&function(e,t,n){var r=ee[e][t];r||(r=ee[e][t]=[]),-1===r.indexOf(n)&&U(n)&&r.push(n)}(e,n,t[n])}))}function ne(e,t){ee[e]||(ee[e]={}),M(t)?Object.keys(t).forEach((function(n){Z.indexOf(n)>-1&&function(e,t,n){var r=ee[e][t];if(r){var i=r.indexOf(n);i>-1&&r.splice(i,1)}}(e,n,t[n])})):delete ee[e]}function re(e,t){return e&&0!==e.length?e.reduce((function(e,n){return e.then((function(){return n(t)}))}),Promise.resolve()):Promise.resolve()}function ie(e,t){return ee[e]&&ee[e][t]||[]}function oe(e){te("callObject",e)}var ae=X("_globalUniCloudListener"),se={RESPONSE:"response",NEED_LOGIN:"needLogin",REFRESH_TOKEN:"refreshToken"},ce={CLIENT_DB:"clientdb",CLOUD_FUNCTION:"cloudfunction",CLOUD_OBJECT:"cloudobject"};function ue(e){return ae[e]||(ae[e]=[]),ae[e]}function le(e,t){var n=ue(e);n.includes(t)||n.push(t)}function fe(e,t){var n=ue(e),r=n.indexOf(t);-1!==r&&n.splice(r,1)}function de(e,t){for(var n=ue(e),r=0;r<n.length;r++)(0,n[r])(t)}var pe,he=!1;function ve(){return pe||(pe=new Promise((function(e){he&&e(),function t(){if("function"==typeof getCurrentPages){var n=getCurrentPages();n&&n[0]&&(he=!0,e())}he||setTimeout((function(){t()}),30)}()})),pe)}function me(e){var t={};for(var n in e){var r=e[n];U(r)&&(t[n]=F(r))}return t}var ge=function(e){(0,p.default)(n,e);var t=O(n);function n(e){var r;(0,g.default)(this,n);var i=e.message||e.errMsg||"unknown system error";return r=t.call(this,i),r.errMsg=i,r.code=r.errCode=e.code||e.errCode||"SYSTEM_ERROR",r.errSubject=r.subject=e.subject||e.errSubject,r.cause=e.cause,r.requestId=e.requestId,r}return(0,y.default)(n,[{key:"toJson",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;if(!(e>=10))return e++,{errCode:this.errCode,errMsg:this.errMsg,errSubject:this.errSubject,cause:this.cause&&this.cause.toJson?this.cause.toJson(e):this.cause}}}]),n}((0,m.default)(Error));t.UniCloudError=ge;var ye,_e,be={request:function(e){return r.request(e)},uploadFile:function(e){return r.uploadFile(e)},setStorageSync:function(e,t){return r.setStorageSync(e,t)},getStorageSync:function(e){return r.getStorageSync(e)},removeStorageSync:function(e){return r.removeStorageSync(e)},clearStorageSync:function(){return r.clearStorageSync()},connectSocket:function(e){return r.connectSocket(e)}};function we(){return{token:be.getStorageSync(j)||be.getStorageSync("uniIdToken"),tokenExpired:be.getStorageSync(C)}}function ke(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.token,n=e.tokenExpired;t&&be.setStorageSync(j,t),n&&be.setStorageSync(C,n)}function xe(){return ye||(ye="mp-weixin"===W&&i.canIUse("getAppBaseInfo")&&i.canIUse("getDeviceInfo")?x(x({},r.getAppBaseInfo()),r.getDeviceInfo()):r.getSystemInfoSync()),ye}var Oe={};function Ae(){var e=r.getLocale&&r.getLocale()||"en";if(_e)return x(x(x({},Oe),_e),{},{locale:e,LOCALE:e});var t=xe(),n=t.deviceId,i=t.osName,o=t.uniPlatform,a=t.appId,s=["appId","appLanguage","appName","appVersion","appVersionCode","appWgtVersion","browserName","browserVersion","deviceBrand","deviceId","deviceModel","deviceType","osName","osVersion","romName","romVersion","ua","hostName","hostVersion","uniPlatform","uniRuntimeVersion","uniRuntimeVersionCode","uniCompilerVersion","uniCompilerVersionCode"];for(var c in t)Object.hasOwnProperty.call(t,c)&&-1===s.indexOf(c)&&delete t[c];return _e=x(x({PLATFORM:o,OS:i,APPID:a,DEVICEID:n},function(){var e,t;try{if(r.getLaunchOptionsSync){if(r.getLaunchOptionsSync.toString().indexOf("not yet implemented")>-1)return;var n=r.getLaunchOptionsSync(),i=n.scene,o=n.channel;e=o,t=i}}catch(e){}return{channel:e,scene:t}}()),t),x(x(x({},Oe),_e),{},{locale:e,LOCALE:e})}var Se,Pe={sign:function(e,t){var n="";return Object.keys(e).sort().forEach((function(t){e[t]&&(n=n+"&"+t+"="+e[t])})),n=n.slice(1),I(n,t).toString()},wrappedRequest:function(e,t){return new Promise((function(n,r){t(Object.assign(e,{complete:function(e){e||(e={});var t=e.data&&e.data.header&&e.data.header["x-serverless-request-id"]||e.header&&e.header["request-id"];if(!e.statusCode||e.statusCode>=400){var i=e.data&&e.data.error&&e.data.error.code||"SYS_ERR",o=e.data&&e.data.error&&e.data.error.message||e.errMsg||"request:fail";return r(new ge({code:i,message:o,requestId:t}))}var a=e.data;if(a.error)return r(new ge({code:a.error.code,message:a.error.message,requestId:t}));a.result=a.data,a.requestId=t,delete a.data,n(a)}}))}))},toBase64:function(e){return T.stringify(E.parse(e))}},Ie=function(){function e(t){var n=this;(0,g.default)(this,e),["spaceId","clientSecret"].forEach((function(e){if(!Object.prototype.hasOwnProperty.call(t,e))throw new Error("".concat(e," required"))})),this.config=Object.assign({},{endpoint:0===t.spaceId.indexOf("mp-")?"https://api.next.bspapp.com":"https://api.bspapp.com"},t),this.config.provider="aliyun",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.config.accessTokenKey="access_token_"+this.config.spaceId,this.adapter=be,this._getAccessTokenPromiseHub=new V({createPromise:function(){return n.requestAuth(n.setupRequest({method:"serverless.auth.user.anonymousAuthorize",params:"{}"},"auth")).then((function(e){if(!e.result||!e.result.accessToken)throw new ge({code:"AUTH_FAILED",message:"获取accessToken失败"});n.setAccessToken(e.result.accessToken)}))},retryRule:B})}return(0,y.default)(e,[{key:"hasAccessToken",get:function(){return!!this.accessToken}},{key:"setAccessToken",value:function(e){this.accessToken=e}},{key:"requestWrapped",value:function(e){return Pe.wrappedRequest(e,this.adapter.request)}},{key:"requestAuth",value:function(e){return this.requestWrapped(e)}},{key:"request",value:function(e,t){var n=this;return Promise.resolve().then((function(){return n.hasAccessToken?t?n.requestWrapped(e):n.requestWrapped(e).catch((function(t){return new Promise((function(e,n){!t||"GATEWAY_INVALID_TOKEN"!==t.code&&"InvalidParameter.InvalidToken"!==t.code?n(t):e()})).then((function(){return n.getAccessToken()})).then((function(){var t=n.rebuildRequest(e);return n.request(t,!0)}))})):n.getAccessToken().then((function(){var t=n.rebuildRequest(e);return n.request(t,!0)}))}))}},{key:"rebuildRequest",value:function(e){var t=Object.assign({},e);return t.data.token=this.accessToken,t.header["x-basement-token"]=this.accessToken,t.header["x-serverless-sign"]=Pe.sign(t.data,this.config.clientSecret),t}},{key:"setupRequest",value:function(e,t){var n=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),r={"Content-Type":"application/json"};return"auth"!==t&&(n.token=this.accessToken,r["x-basement-token"]=this.accessToken),r["x-serverless-sign"]=Pe.sign(n,this.config.clientSecret),{url:this.config.requestUrl,method:"POST",data:n,dataType:"json",header:r}}},{key:"getAccessToken",value:function(){return this._getAccessTokenPromiseHub.exec()}},{key:"authorize",value:function(){var e=(0,f.default)(a.default.mark((function e(){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.getAccessToken();case 2:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"callFunction",value:function(e){var t={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:e.name,functionArgs:e.data||{}})};return this.request(x(x({},this.setupRequest(t)),{},{timeout:e.timeout}))}},{key:"getOSSUploadOptionsFromPath",value:function(e){var t={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}},{key:"uploadFileToOSS",value:function(e){var t=this,n=e.url,r=e.formData,i=e.name,o=e.filePath,a=e.fileType,s=e.onUploadProgress;return new Promise((function(e,c){var u=t.adapter.uploadFile({url:n,formData:r,name:i,filePath:o,fileType:a,header:{"X-OSS-server-side-encrpytion":"AES256"},success:function(t){t&&t.statusCode<400?e(t):c(new ge({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail:function(e){c(new ge({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof s&&u&&"function"==typeof u.onProgressUpdate&&u.onProgressUpdate((function(e){s({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}},{key:"reportOSSUpload",value:function(e){var t={method:"serverless.file.resource.report",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}},{key:"uploadFile",value:function(){var e=(0,f.default)(a.default.mark((function e(t){var n,r,i,o,s,c,u,l,f,d,p,h,v,m,g,y,_,b,w,k,x,O;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=t.filePath,r=t.cloudPath,i=t.fileType,o=void 0===i?"image":i,s=t.cloudPathAsRealPath,c=void 0!==s&&s,u=t.onUploadProgress,l=t.config,"string"===R(r)){e.next=3;break}throw new ge({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});case 3:if(r=r.trim()){e.next=5;break}throw new ge({code:"INVALID_PARAM",message:"cloudPath不可为空"});case 5:if(!/:\/\//.test(r)){e.next=7;break}throw new ge({code:"INVALID_PARAM",message:"cloudPath不合法"});case 7:if(f=l&&l.envType||this.config.envType,!(c&&("/"!==r[0]&&(r="/"+r),r.indexOf("\\")>-1))){e.next=10;break}throw new ge({code:"INVALID_PARAM",message:"使用cloudPath作为路径时，cloudPath不可包含“\\”"});case 10:return e.next=12,this.getOSSUploadOptionsFromPath({env:f,filename:c?r.split("/").pop():r,fileId:c?r:void 0});case 12:return d=e.sent.result,p="https://"+d.cdnDomain+"/"+d.ossPath,h=d.securityToken,v=d.accessKeyId,m=d.signature,g=d.host,y=d.ossPath,_=d.id,b=d.policy,w=d.ossCallbackUrl,k={"Cache-Control":"max-age=2592000","Content-Disposition":"attachment",OSSAccessKeyId:v,Signature:m,host:g,id:_,key:y,policy:b,success_action_status:200},h&&(k["x-oss-security-token"]=h),w&&(x=JSON.stringify({callbackUrl:w,callbackBody:JSON.stringify({fileId:_,spaceId:this.config.spaceId}),callbackBodyType:"application/json"}),k.callback=Pe.toBase64(x)),O={url:"https://"+d.host,formData:k,fileName:"file",name:"file",filePath:n,fileType:o},e.next=27,this.uploadFileToOSS(Object.assign({},O,{onUploadProgress:u}));case 27:if(!w){e.next=29;break}return e.abrupt("return",{success:!0,filePath:n,fileID:p});case 29:return e.next=31,this.reportOSSUpload({id:_});case 31:if(!e.sent.success){e.next=33;break}return e.abrupt("return",{success:!0,filePath:n,fileID:p});case 33:throw new ge({code:"UPLOAD_FAILED",message:"文件上传失败"});case 34:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"getTempFileURL",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.fileList;return new Promise((function(t,r){Array.isArray(n)&&0!==n.length||r(new ge({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"})),e.getFileInfo({fileList:n}).then((function(e){t({fileList:n.map((function(t,n){var r=e.fileList[n];return{fileID:t,tempFileURL:r&&r.url||t}}))})}))}))}},{key:"getFileInfo",value:function(){var e=(0,f.default)(a.default.mark((function e(){var t,n,r,i=arguments;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=i.length>0&&void 0!==i[0]?i[0]:{},n=t.fileList,Array.isArray(n)&&0!==n.length){e.next=3;break}throw new ge({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});case 3:return r={method:"serverless.file.resource.info",params:JSON.stringify({id:n.map((function(e){return e.split("?")[0]})).join(",")})},e.next=6,this.request(this.setupRequest(r));case 6:return e.t0=e.sent.result,e.abrupt("return",{fileList:e.t0});case 8:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()}]),e}(),Ee={init:function(e){var t=new Ie(e),n={signInAnonymously:function(){return t.authorize()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return n},t.customAuth=t.auth,t}},Te="undefined"!=typeof location&&"http:"===location.protocol?"http:":"https:";!function(e){e.local="local",e.none="none",e.session="session"}(Se||(Se={}));var je,Ce=function(){},Le=A((function(e,t){var n;e.exports=(n=P,function(e){var t=n,r=t.lib,i=r.WordArray,o=r.Hasher,a=t.algo,s=[],c=[];!function(){function t(t){for(var n=e.sqrt(t),r=2;r<=n;r++)if(!(t%r))return!1;return!0}function n(e){return 4294967296*(e-(0|e))|0}for(var r=2,i=0;i<64;)t(r)&&(i<8&&(s[i]=n(e.pow(r,.5))),c[i]=n(e.pow(r,1/3)),i++),r++}();var u=[],l=a.SHA256=o.extend({_doReset:function(){this._hash=new i.init(s.slice(0))},_doProcessBlock:function(e,t){for(var n=this._hash.words,r=n[0],i=n[1],o=n[2],a=n[3],s=n[4],l=n[5],f=n[6],d=n[7],p=0;p<64;p++){if(p<16)u[p]=0|e[t+p];else{var h=u[p-15],v=(h<<25|h>>>7)^(h<<14|h>>>18)^h>>>3,m=u[p-2],g=(m<<15|m>>>17)^(m<<13|m>>>19)^m>>>10;u[p]=v+u[p-7]+g+u[p-16]}var y=r&i^r&o^i&o,_=(r<<30|r>>>2)^(r<<19|r>>>13)^(r<<10|r>>>22),b=d+((s<<26|s>>>6)^(s<<21|s>>>11)^(s<<7|s>>>25))+(s&l^~s&f)+c[p]+u[p];d=f,f=l,l=s,s=a+b|0,a=o,o=i,i=r,r=b+(_+y)|0}n[0]=n[0]+r|0,n[1]=n[1]+i|0,n[2]=n[2]+o|0,n[3]=n[3]+a|0,n[4]=n[4]+s|0,n[5]=n[5]+l|0,n[6]=n[6]+f|0,n[7]=n[7]+d|0},_doFinalize:function(){var t=this._data,n=t.words,r=8*this._nDataBytes,i=8*t.sigBytes;return n[i>>>5]|=128<<24-i%32,n[14+(i+64>>>9<<4)]=e.floor(r/4294967296),n[15+(i+64>>>9<<4)]=r,t.sigBytes=4*n.length,this._process(),this._hash},clone:function(){var e=o.clone.call(this);return e._hash=this._hash.clone(),e}});t.SHA256=o._createHelper(l),t.HmacSHA256=o._createHmacHelper(l)}(Math),n.SHA256)})),$e=Le,De=A((function(e,t){e.exports=P.HmacSHA256})),Ne=function(){var e;if(!Promise){e=function(){},e.promise={};var t=function(){throw new ge({message:'Your Node runtime does support ES6 Promises. Set "global.Promise" to your preferred implementation of promises.'})};return Object.defineProperty(e.promise,"then",{get:t}),Object.defineProperty(e.promise,"catch",{get:t}),e}var n=new Promise((function(t,n){e=function(e,r){return e?n(e):t(r)}}));return e.promise=n,e};function Re(e){return void 0===e}function Me(e){return"[object Null]"===Object.prototype.toString.call(e)}function Ue(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e.replace(/([\s\S]+)\s+(请前往云开发AI小助手查看问题：.*)/,"$1")}function Fe(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:32,t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",n="",r=0;r<e;r++)n+=t.charAt(Math.floor(62*Math.random()));return n}!function(e){e.WEB="web",e.WX_MP="wx_mp"}(je||(je={}));var qe={adapter:null,runtime:void 0},Be=["anonymousUuidKey"],Ve=function(e){(0,p.default)(n,e);var t=O(n);function n(){var e;return(0,g.default)(this,n),e=t.call(this),qe.adapter.root.tcbObject||(qe.adapter.root.tcbObject={}),e}return(0,y.default)(n,[{key:"setItem",value:function(e,t){qe.adapter.root.tcbObject[e]=t}},{key:"getItem",value:function(e){return qe.adapter.root.tcbObject[e]}},{key:"removeItem",value:function(e){delete qe.adapter.root.tcbObject[e]}},{key:"clear",value:function(){delete qe.adapter.root.tcbObject}}]),n}(Ce);function He(e,t){switch(e){case"local":return t.localStorage||new Ve;case"none":return new Ve;default:return t.sessionStorage||new Ve}}var Ke=function(){function e(t){if((0,g.default)(this,e),!this._storage){this._persistence=qe.adapter.primaryStorage||t.persistence,this._storage=He(this._persistence,qe.adapter);var n="access_token_".concat(t.env),r="access_token_expire_".concat(t.env),i="refresh_token_".concat(t.env),o="anonymous_uuid_".concat(t.env),a="login_type_".concat(t.env),s="token_type_".concat(t.env),c="user_info_".concat(t.env);this.keys={accessTokenKey:n,accessTokenExpireKey:r,refreshTokenKey:i,anonymousUuidKey:o,loginTypeKey:a,userInfoKey:c,deviceIdKey:"device_id",tokenTypeKey:s}}}return(0,y.default)(e,[{key:"updatePersistence",value:function(e){if(e!==this._persistence){var t="local"===this._persistence;this._persistence=e;var n=He(e,qe.adapter);for(var r in this.keys){var i=this.keys[r];if(!t||!Be.includes(r)){var o=this._storage.getItem(i);Re(o)||Me(o)||(n.setItem(i,o),this._storage.removeItem(i))}}this._storage=n}}},{key:"setStore",value:function(e,t,n){if(this._storage){var r={version:n||"localCachev1",content:t},i=JSON.stringify(r);try{this._storage.setItem(e,i)}catch(e){throw e}}}},{key:"getStore",value:function(e,t){try{if(!this._storage)return}catch(e){return""}t=t||"localCachev1";var n=this._storage.getItem(e);return n&&n.indexOf(t)>=0?JSON.parse(n).content:""}},{key:"removeStore",value:function(e){this._storage.removeItem(e)}}]),e}(),ze={},Je={};function We(e){return ze[e]}var Ge=(0,y.default)((function e(t,n){(0,g.default)(this,e),this.data=n||null,this.name=t})),Ye=function(e){(0,p.default)(n,e);var t=O(n);function n(e,r){var i;return(0,g.default)(this,n),i=t.call(this,"error",{error:e,data:r}),i.error=e,i}return(0,y.default)(n)}(Ge),Qe=new(function(){function e(){(0,g.default)(this,e),this._listeners={}}return(0,y.default)(e,[{key:"on",value:function(e,t){return function(e,t,n){n[e]=n[e]||[],n[e].push(t)}(e,t,this._listeners),this}},{key:"off",value:function(e,t){return function(e,t,n){if(n&&n[e]){var r=n[e].indexOf(t);-1!==r&&n[e].splice(r,1)}}(e,t,this._listeners),this}},{key:"fire",value:function(e,t){if(e instanceof Ye)return console.error(e.error),this;var n="string"==typeof e?new Ge(e,t||{}):e,r=n.name;if(this._listens(r)){n.target=this;var i,o=this._listeners[r]?(0,l.default)(this._listeners[r]):[],a=b(o);try{for(a.s();!(i=a.n()).done;){var s=i.value;s.call(this,n)}}catch(c){a.e(c)}finally{a.f()}}return this}},{key:"_listens",value:function(e){return this._listeners[e]&&this._listeners[e].length>0}}]),e}());function Xe(e,t){Qe.on(e,t)}function Ze(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};Qe.fire(e,t)}function et(e,t){Qe.off(e,t)}var tt,nt="loginStateChanged",rt="loginStateExpire",it="loginTypeChanged",ot="anonymousConverted",at="refreshAccessToken";!function(e){e.ANONYMOUS="ANONYMOUS",e.WECHAT="WECHAT",e.WECHAT_PUBLIC="WECHAT-PUBLIC",e.WECHAT_OPEN="WECHAT-OPEN",e.CUSTOM="CUSTOM",e.EMAIL="EMAIL",e.USERNAME="USERNAME",e.NULL="NULL"}(tt||(tt={}));var st=function(){function e(){(0,g.default)(this,e),this._fnPromiseMap=new Map}return(0,y.default)(e,[{key:"run",value:function(){var e=(0,f.default)(a.default.mark((function e(t,n){var r,i=this;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r=this._fnPromiseMap.get(t),e.abrupt("return",(r||(r=new Promise(function(){var e=(0,f.default)(a.default.mark((function e(r,o){var s;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,i._runIdlePromise();case 3:return s=n(),e.t0=r,e.next=7,s;case 7:e.t1=e.sent,(0,e.t0)(e.t1),e.next=14;break;case 11:e.prev=11,e.t2=e["catch"](0),o(e.t2);case 14:return e.prev=14,i._fnPromiseMap.delete(t),e.finish(14);case 17:case"end":return e.stop()}}),e,null,[[0,11,14,17]])})));return function(t,n){return e.apply(this,arguments)}}()),this._fnPromiseMap.set(t,r)),r));case 2:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"_runIdlePromise",value:function(){return Promise.resolve()}}]),e}(),ct=function(){function e(t){(0,g.default)(this,e),this._singlePromise=new st,this._cache=We(t.env),this._baseURL="https://".concat(t.env,".ap-shanghai.tcb-api.tencentcloudapi.com"),this._reqClass=new qe.adapter.reqClass({timeout:t.timeout,timeoutMsg:"请求在".concat(t.timeout/1e3,"s内未完成，已中断"),restrictedMethods:["post"]})}return(0,y.default)(e,[{key:"_getDeviceId",value:function(){if(this._deviceID)return this._deviceID;var e=this._cache.keys.deviceIdKey,t=this._cache.getStore(e);return"string"==typeof t&&t.length>=16&&t.length<=48||(t=Fe(),this._cache.setStore(e,t)),this._deviceID=t,t}},{key:"_request",value:function(){var e=(0,f.default)(a.default.mark((function e(t,n){var r,i,o,s,c,u=arguments;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r=u.length>2&&void 0!==u[2]?u[2]:{},i={"x-request-id":Fe(),"x-device-id":this._getDeviceId()},!r.withAccessToken){e.next=9;break}return o=this._cache.keys.tokenTypeKey,e.next=6,this.getAccessToken();case 6:s=e.sent,c=this._cache.getStore(o),i.authorization="".concat(c," ").concat(s);case 9:return e.abrupt("return",this._reqClass["get"===r.method?"get":"post"]({url:"".concat(this._baseURL).concat(t),data:n,headers:i}));case 10:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"_fetchAccessToken",value:function(){var e=(0,f.default)(a.default.mark((function e(){var t,n,r,i,o,s,c,u,l,d,p=this;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=this._cache.keys,n=t.loginTypeKey,r=t.accessTokenKey,i=t.accessTokenExpireKey,o=t.tokenTypeKey,s=this._cache.getStore(n),!s||s===tt.ANONYMOUS){e.next=3;break}throw new ge({code:"INVALID_OPERATION",message:"非匿名登录不支持刷新 access token"});case 3:return e.next=5,this._singlePromise.run("fetchAccessToken",(0,f.default)(a.default.mark((function e(){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,p._request("/auth/v1/signin/anonymously",{},{method:"post"});case 2:return e.abrupt("return",e.sent.data);case 3:case"end":return e.stop()}}),e)}))));case 5:return c=e.sent,u=c.access_token,l=c.expires_in,d=c.token_type,e.abrupt("return",(this._cache.setStore(o,d),this._cache.setStore(r,u),this._cache.setStore(i,Date.now()+1e3*l),u));case 10:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"isAccessTokenExpired",value:function(e,t){var n=!0;return e&&t&&(n=t<Date.now()),n}},{key:"getAccessToken",value:function(){var e=(0,f.default)(a.default.mark((function e(){var t,n,r,i,o;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=this._cache.keys,n=t.accessTokenKey,r=t.accessTokenExpireKey,i=this._cache.getStore(n),o=this._cache.getStore(r),e.abrupt("return",this.isAccessTokenExpired(i,o)?this._fetchAccessToken():i);case 2:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"refreshAccessToken",value:function(){var e=(0,f.default)(a.default.mark((function e(){var t,n,r,i;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=this._cache.keys,n=t.accessTokenKey,r=t.accessTokenExpireKey,i=t.loginTypeKey,e.abrupt("return",(this._cache.removeStore(n),this._cache.removeStore(r),this._cache.setStore(i,tt.ANONYMOUS),this.getAccessToken()));case 2:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"getUserInfo",value:function(){var e=(0,f.default)(a.default.mark((function e(){var t=this;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._singlePromise.run("getUserInfo",(0,f.default)(a.default.mark((function e(){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t._request("/auth/v1/user/me",{},{withAccessToken:!0,method:"get"});case 2:return e.abrupt("return",e.sent.data);case 3:case"end":return e.stop()}}),e)})))));case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()}]),e}(),ut=["auth.getJwt","auth.logout","auth.signInWithTicket","auth.signInAnonymously","auth.signIn","auth.fetchAccessTokenWithRefreshToken","auth.signUpWithEmailAndPassword","auth.activateEndUserMail","auth.sendPasswordResetEmail","auth.resetPasswordWithToken","auth.isUsernameRegistered"],lt={"X-SDK-Version":"1.3.5"};function ft(e,t,n){var r=e[t];e[t]=function(t){var i={},o={};n.forEach((function(n){var r=n.call(e,t),a=r.data,s=r.headers;Object.assign(i,a),Object.assign(o,s)}));var a=t.data;return a&&function(){var e;if(e=a,"[object FormData]"!==Object.prototype.toString.call(e))t.data=x(x({},a),i);else for(var n in i)a.append(n,i[n])}(),t.headers=x(x({},t.headers||{}),o),r.call(e,t)}}function dt(){var e=Math.random().toString(16).slice(2);return{data:{seqId:e},headers:x(x({},lt),{},{"x-seqid":e})}}var pt=function(){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(0,g.default)(this,e),this.config=n,this._reqClass=new qe.adapter.reqClass({timeout:this.config.timeout,timeoutMsg:"请求在".concat(this.config.timeout/1e3,"s内未完成，已中断"),restrictedMethods:["post"]}),this._cache=We(this.config.env),this._localCache=(t=this.config.env,Je[t]),this.oauth=new ct(this.config),ft(this._reqClass,"post",[dt]),ft(this._reqClass,"upload",[dt]),ft(this._reqClass,"download",[dt])}return(0,y.default)(e,[{key:"post",value:function(){var e=(0,f.default)(a.default.mark((function e(t){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._reqClass.post(t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"upload",value:function(){var e=(0,f.default)(a.default.mark((function e(t){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._reqClass.upload(t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"download",value:function(){var e=(0,f.default)(a.default.mark((function e(t){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._reqClass.download(t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"refreshAccessToken",value:function(){var e=(0,f.default)(a.default.mark((function e(){var t,n;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this._refreshAccessTokenPromise||(this._refreshAccessTokenPromise=this._refreshAccessToken()),e.prev=1,e.next=4,this._refreshAccessTokenPromise;case 4:t=e.sent,e.next=10;break;case 7:e.prev=7,e.t0=e["catch"](1),n=e.t0;case 10:if(this._refreshAccessTokenPromise=null,this._shouldRefreshAccessTokenHook=null,!n){e.next=12;break}throw n;case 12:return e.abrupt("return",t);case 13:case"end":return e.stop()}}),e,this,[[1,7]])})));return function(){return e.apply(this,arguments)}}()},{key:"_refreshAccessToken",value:function(){var e=(0,f.default)(a.default.mark((function e(){var t,n,r,i,o,s,c,u,l,f,d,p,h;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=this._cache.keys,n=t.accessTokenKey,r=t.accessTokenExpireKey,i=t.refreshTokenKey,o=t.loginTypeKey,s=t.anonymousUuidKey,this._cache.removeStore(n),this._cache.removeStore(r),c=this._cache.getStore(i),c){e.next=5;break}throw new ge({message:"未登录CloudBase"});case 5:return u={refresh_token:c},e.next=8,this.request("auth.fetchAccessTokenWithRefreshToken",u);case 8:if(l=e.sent,!l.data.code){e.next=21;break}if(f=l.data.code,"SIGN_PARAM_INVALID"!==f&&"REFRESH_TOKEN_EXPIRED"!==f&&"INVALID_REFRESH_TOKEN"!==f){e.next=20;break}if(this._cache.getStore(o)!==tt.ANONYMOUS||"INVALID_REFRESH_TOKEN"!==f){e.next=19;break}return d=this._cache.getStore(s),p=this._cache.getStore(i),e.next=17,this.send("auth.signInAnonymously",{anonymous_uuid:d,refresh_token:p});case 17:return h=e.sent,e.abrupt("return",(this.setRefreshToken(h.refresh_token),this._refreshAccessToken()));case 19:Ze(rt),this._cache.removeStore(i);case 20:throw new ge({code:l.data.code,message:"刷新access token失败：".concat(l.data.code)});case 21:if(!l.data.access_token){e.next=23;break}return e.abrupt("return",(Ze(at),this._cache.setStore(n,l.data.access_token),this._cache.setStore(r,l.data.access_token_expire+Date.now()),{accessToken:l.data.access_token,accessTokenExpire:l.data.access_token_expire}));case 23:l.data.refresh_token&&(this._cache.removeStore(i),this._cache.setStore(i,l.data.refresh_token),this._refreshAccessToken());case 24:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"getAccessToken",value:function(){var e=(0,f.default)(a.default.mark((function e(){var t,n,r,i,o,s,c;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=this._cache.keys,n=t.accessTokenKey,r=t.accessTokenExpireKey,i=t.refreshTokenKey,this._cache.getStore(i)){e.next=3;break}throw new ge({message:"refresh token不存在，登录状态异常"});case 3:if(o=this._cache.getStore(n),s=this._cache.getStore(r),c=!0,e.t0=this._shouldRefreshAccessTokenHook,!e.t0){e.next=9;break}return e.next=8,this._shouldRefreshAccessTokenHook(o,s);case 8:e.t0=!e.sent;case 9:if(e.t1=e.t0,!e.t1){e.next=12;break}c=!1;case 12:return e.abrupt("return",(!o||!s||s<Date.now())&&c?this.refreshAccessToken():{accessToken:o,accessTokenExpire:s});case 13:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"request",value:function(){var e=(0,f.default)(a.default.mark((function e(t,n,r){var i,o,s,c,u,l,f,d,p,h,v,m,g,y,_;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(i="x-tcb-trace_".concat(this.config.env),o="application/x-www-form-urlencoded",s=x({action:t,env:this.config.env,dataVersion:"2019-08-16"},n),e.t0=-1===ut.indexOf(t),!e.t0){e.next=9;break}return this._cache.keys,e.next=8,this.oauth.getAccessToken();case 8:s.access_token=e.sent;case 9:if("storage.uploadFile"!==t){e.next=15;break}for(u in c=new FormData,c)c.hasOwnProperty(u)&&void 0!==c[u]&&c.append(u,s[u]);o="multipart/form-data",e.next=17;break;case 15:for(l in o="application/json",c={},s)void 0!==s[l]&&(c[l]=s[l]);case 17:return f={headers:{"content-type":o}},r&&r.timeout&&(f.timeout=r.timeout),r&&r.onUploadProgress&&(f.onUploadProgress=r.onUploadProgress),d=this._localCache.getStore(i),d&&(f.headers["X-TCB-Trace"]=d),p=n.parse,h=n.inQuery,v=n.search,m={env:this.config.env},p&&(m.parse=!0),h&&(m=x(x({},h),m)),g=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=/\?/.test(t),i="";for(var o in n)""===i?!r&&(t+="?"):i+="&",i+="".concat(o,"=").concat(encodeURIComponent(n[o]));return/^http(s)?\:\/\//.test(t+=i)?t:"".concat(e).concat(t)}(Te,"//tcb-api.tencentcloudapi.com/web",m),v&&(g+=v),e.next=28,this.post(x({url:g,data:c},f));case 28:if(y=e.sent,_=y.header&&y.header["x-tcb-trace"],_&&this._localCache.setStore(i,_),(200===Number(y.status)||200===Number(y.statusCode))&&y.data){e.next=32;break}throw new ge({code:"NETWORK_ERROR",message:"network request error"});case 32:return e.abrupt("return",y);case 33:case"end":return e.stop()}}),e,this)})));return function(t,n,r){return e.apply(this,arguments)}}()},{key:"send",value:function(){var e=(0,f.default)(a.default.mark((function e(t){var n,r,i,o,s=arguments;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=s.length>1&&void 0!==s[1]?s[1]:{},r=s.length>2&&void 0!==s[2]?s[2]:{},e.next=4,this.request(t,n,x(x({},r),{},{onUploadProgress:n.onUploadProgress}));case 4:if(i=e.sent,"ACCESS_TOKEN_DISABLED"!==i.data.code&&"ACCESS_TOKEN_EXPIRED"!==i.data.code||-1!==ut.indexOf(t)){e.next=14;break}return e.next=8,this.oauth.refreshAccessToken();case 8:return e.next=10,this.request(t,n,x(x({},r),{},{onUploadProgress:n.onUploadProgress}));case 10:if(o=e.sent,!o.data.code){e.next=13;break}throw new ge({code:o.data.code,message:Ue(o.data.message)});case 13:return e.abrupt("return",o.data);case 14:if(!i.data.code){e.next=16;break}throw new ge({code:i.data.code,message:Ue(i.data.message)});case 16:return e.abrupt("return",i.data);case 17:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"setRefreshToken",value:function(e){var t=this._cache.keys,n=t.accessTokenKey,r=t.accessTokenExpireKey,i=t.refreshTokenKey;this._cache.removeStore(n),this._cache.removeStore(r),this._cache.setStore(i,e)}}]),e}(),ht={};function vt(e){return ht[e]}var mt=function(){function e(t){(0,g.default)(this,e),this.config=t,this._cache=We(t.env),this._request=vt(t.env)}return(0,y.default)(e,[{key:"setRefreshToken",value:function(e){var t=this._cache.keys,n=t.accessTokenKey,r=t.accessTokenExpireKey,i=t.refreshTokenKey;this._cache.removeStore(n),this._cache.removeStore(r),this._cache.setStore(i,e)}},{key:"setAccessToken",value:function(e,t){var n=this._cache.keys,r=n.accessTokenKey,i=n.accessTokenExpireKey;this._cache.setStore(r,e),this._cache.setStore(i,t)}},{key:"refreshUserInfo",value:function(){var e=(0,f.default)(a.default.mark((function e(){var t,n;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._request.send("auth.getUserInfo",{});case 2:return t=e.sent,n=t.data,e.abrupt("return",(this.setLocalUserInfo(n),n));case 5:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"setLocalUserInfo",value:function(e){var t=this._cache.keys.userInfoKey;this._cache.setStore(t,e)}}]),e}(),gt=function(){function e(t){if((0,g.default)(this,e),!t)throw new ge({code:"PARAM_ERROR",message:"envId is not defined"});this._envId=t,this._cache=We(this._envId),this._request=vt(this._envId),this.setUserInfo()}return(0,y.default)(e,[{key:"linkWithTicket",value:function(e){if("string"!=typeof e)throw new ge({code:"PARAM_ERROR",message:"ticket must be string"});return this._request.send("auth.linkWithTicket",{ticket:e})}},{key:"linkWithRedirect",value:function(e){e.signInWithRedirect()}},{key:"updatePassword",value:function(e,t){return this._request.send("auth.updatePassword",{oldPassword:t,newPassword:e})}},{key:"updateEmail",value:function(e){return this._request.send("auth.updateEmail",{newEmail:e})}},{key:"updateUsername",value:function(e){if("string"!=typeof e)throw new ge({code:"PARAM_ERROR",message:"username must be a string"});return this._request.send("auth.updateUsername",{username:e})}},{key:"getLinkedUidList",value:function(){var e=(0,f.default)(a.default.mark((function e(){var t,n,r,i;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._request.send("auth.getLinkedUidList",{});case 2:return t=e.sent,n=t.data,r=!1,i=n.users,e.abrupt("return",(i.forEach((function(e){e.wxOpenId&&e.wxPublicId&&(r=!0)})),{users:i,hasPrimaryUid:r}));case 7:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"setPrimaryUid",value:function(e){return this._request.send("auth.setPrimaryUid",{uid:e})}},{key:"unlink",value:function(e){return this._request.send("auth.unlink",{platform:e})}},{key:"update",value:function(){var e=(0,f.default)(a.default.mark((function e(t){var n,r,i,o,s,c,u,l;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=t.nickName,r=t.gender,i=t.avatarUrl,o=t.province,s=t.country,c=t.city,e.next=8,this._request.send("auth.updateUserInfo",{nickName:n,gender:r,avatarUrl:i,province:o,country:s,city:c});case 8:u=e.sent,l=u.data,this.setLocalUserInfo(l);case 11:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"refresh",value:function(){var e=(0,f.default)(a.default.mark((function e(){var t;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._request.oauth.getUserInfo();case 2:return t=e.sent,e.abrupt("return",(this.setLocalUserInfo(t),t));case 4:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"setUserInfo",value:function(){var e=this,t=this._cache.keys.userInfoKey,n=this._cache.getStore(t);["uid","loginType","openid","wxOpenId","wxPublicId","unionId","qqMiniOpenId","email","hasPassword","customUserId","nickName","gender","avatarUrl"].forEach((function(t){e[t]=n[t]})),this.location={country:n.country,province:n.province,city:n.city}}},{key:"setLocalUserInfo",value:function(e){var t=this._cache.keys.userInfoKey;this._cache.setStore(t,e),this.setUserInfo()}}]),e}(),yt=function(){function e(t){if((0,g.default)(this,e),!t)throw new ge({code:"PARAM_ERROR",message:"envId is not defined"});this._cache=We(t);var n=this._cache.keys,r=n.refreshTokenKey,i=n.accessTokenKey,o=n.accessTokenExpireKey,a=this._cache.getStore(r),s=this._cache.getStore(i),c=this._cache.getStore(o);this.credential={refreshToken:a,accessToken:s,accessTokenExpire:c},this.user=new gt(t)}return(0,y.default)(e,[{key:"isAnonymousAuth",get:function(){return this.loginType===tt.ANONYMOUS}},{key:"isCustomAuth",get:function(){return this.loginType===tt.CUSTOM}},{key:"isWeixinAuth",get:function(){return this.loginType===tt.WECHAT||this.loginType===tt.WECHAT_OPEN||this.loginType===tt.WECHAT_PUBLIC}},{key:"loginType",get:function(){return this._cache.getStore(this._cache.keys.loginTypeKey)}}]),e}(),_t=function(e){(0,p.default)(n,e);var t=O(n);function n(){return(0,g.default)(this,n),t.apply(this,arguments)}return(0,y.default)(n,[{key:"signIn",value:function(){var e=(0,f.default)(a.default.mark((function e(){var t;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this._cache.updatePersistence("local"),e.next=3,this._request.oauth.getAccessToken();case 3:return Ze(nt),Ze(it,{env:this.config.env,loginType:tt.ANONYMOUS,persistence:"local"}),t=new yt(this.config.env),e.next=8,t.user.refresh();case 8:return e.abrupt("return",t);case 9:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"linkAndRetrieveDataWithTicket",value:function(){var e=(0,f.default)(a.default.mark((function e(t){var n,r,i,o,s,c;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=this._cache.keys,r=n.anonymousUuidKey,i=n.refreshTokenKey,o=this._cache.getStore(r),s=this._cache.getStore(i),e.next=7,this._request.send("auth.linkAndRetrieveDataWithTicket",{anonymous_uuid:o,refresh_token:s,ticket:t});case 7:if(c=e.sent,!c.refresh_token){e.next=16;break}return this._clearAnonymousUUID(),this.setRefreshToken(c.refresh_token),e.next=13,this._request.refreshAccessToken();case 13:return Ze(ot,{env:this.config.env}),Ze(it,{loginType:tt.CUSTOM,persistence:"local"}),e.abrupt("return",{credential:{refreshToken:c.refresh_token}});case 16:throw new ge({message:"匿名转化失败"});case 17:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"_setAnonymousUUID",value:function(e){var t=this._cache.keys,n=t.anonymousUuidKey,r=t.loginTypeKey;this._cache.removeStore(n),this._cache.setStore(n,e),this._cache.setStore(r,tt.ANONYMOUS)}},{key:"_clearAnonymousUUID",value:function(){this._cache.removeStore(this._cache.keys.anonymousUuidKey)}}]),n}(mt),bt=function(e){(0,p.default)(n,e);var t=O(n);function n(){return(0,g.default)(this,n),t.apply(this,arguments)}return(0,y.default)(n,[{key:"signIn",value:function(){var e=(0,f.default)(a.default.mark((function e(t){var n,r;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("string"==typeof t){e.next=2;break}throw new ge({code:"PARAM_ERROR",message:"ticket must be a string"});case 2:return n=this._cache.keys.refreshTokenKey,e.next=5,this._request.send("auth.signInWithTicket",{ticket:t,refresh_token:this._cache.getStore(n)||""});case 5:if(r=e.sent,!r.refresh_token){e.next=15;break}return this.setRefreshToken(r.refresh_token),e.next=10,this._request.refreshAccessToken();case 10:return Ze(nt),Ze(it,{env:this.config.env,loginType:tt.CUSTOM,persistence:this.config.persistence}),e.next=14,this.refreshUserInfo();case 14:return e.abrupt("return",new yt(this.config.env));case 15:throw new ge({message:"自定义登录失败"});case 16:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()}]),n}(mt),wt=function(e){(0,p.default)(n,e);var t=O(n);function n(){return(0,g.default)(this,n),t.apply(this,arguments)}return(0,y.default)(n,[{key:"signIn",value:function(){var e=(0,f.default)(a.default.mark((function e(t,n){var r,i,o,s,c;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("string"==typeof t){e.next=2;break}throw new ge({code:"PARAM_ERROR",message:"email must be a string"});case 2:return r=this._cache.keys.refreshTokenKey,e.next=5,this._request.send("auth.signIn",{loginType:"EMAIL",email:t,password:n,refresh_token:this._cache.getStore(r)||""});case 5:if(i=e.sent,o=i.refresh_token,s=i.access_token,c=i.access_token_expire,!o){e.next=22;break}if(this.setRefreshToken(o),!s||!c){e.next=15;break}this.setAccessToken(s,c),e.next=17;break;case 15:return e.next=17,this._request.refreshAccessToken();case 17:return e.next=19,this.refreshUserInfo();case 19:return Ze(nt),Ze(it,{env:this.config.env,loginType:tt.EMAIL,persistence:this.config.persistence}),e.abrupt("return",new yt(this.config.env));case 22:throw i.code?new ge({code:i.code,message:"邮箱登录失败: ".concat(i.message)}):new ge({message:"邮箱登录失败"});case 23:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"activate",value:function(){var e=(0,f.default)(a.default.mark((function e(t){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._request.send("auth.activateEndUserMail",{token:t}));case 1:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"resetPasswordWithToken",value:function(){var e=(0,f.default)(a.default.mark((function e(t,n){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._request.send("auth.resetPasswordWithToken",{token:t,newPassword:n}));case 1:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()}]),n}(mt),kt=function(e){(0,p.default)(n,e);var t=O(n);function n(){return(0,g.default)(this,n),t.apply(this,arguments)}return(0,y.default)(n,[{key:"signIn",value:function(){var e=(0,f.default)(a.default.mark((function e(t,n){var r,i,o,s,c;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("string"==typeof t){e.next=2;break}throw new ge({code:"PARAM_ERROR",message:"username must be a string"});case 2:return"string"!=typeof n&&(n="",console.warn("password is empty")),r=this._cache.keys.refreshTokenKey,e.next=6,this._request.send("auth.signIn",{loginType:tt.USERNAME,username:t,password:n,refresh_token:this._cache.getStore(r)||""});case 6:if(i=e.sent,o=i.refresh_token,s=i.access_token_expire,c=i.access_token,!o){e.next=23;break}if(this.setRefreshToken(o),!c||!s){e.next=16;break}this.setAccessToken(c,s),e.next=18;break;case 16:return e.next=18,this._request.refreshAccessToken();case 18:return e.next=20,this.refreshUserInfo();case 20:return Ze(nt),Ze(it,{env:this.config.env,loginType:tt.USERNAME,persistence:this.config.persistence}),e.abrupt("return",new yt(this.config.env));case 23:throw i.code?new ge({code:i.code,message:"用户名密码登录失败: ".concat(i.message)}):new ge({message:"用户名密码登录失败"});case 24:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()}]),n}(mt),xt=function(){function e(t){(0,g.default)(this,e),this.config=t,this._cache=We(t.env),this._request=vt(t.env),this._onAnonymousConverted=this._onAnonymousConverted.bind(this),this._onLoginTypeChanged=this._onLoginTypeChanged.bind(this),Xe(it,this._onLoginTypeChanged)}return(0,y.default)(e,[{key:"currentUser",get:function(){var e=this.hasLoginState();return e&&e.user||null}},{key:"loginType",get:function(){return this._cache.getStore(this._cache.keys.loginTypeKey)}},{key:"anonymousAuthProvider",value:function(){return new _t(this.config)}},{key:"customAuthProvider",value:function(){return new bt(this.config)}},{key:"emailAuthProvider",value:function(){return new wt(this.config)}},{key:"usernameAuthProvider",value:function(){return new kt(this.config)}},{key:"signInAnonymously",value:function(){var e=(0,f.default)(a.default.mark((function e(){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",new _t(this.config).signIn());case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"signInWithEmailAndPassword",value:function(){var e=(0,f.default)(a.default.mark((function e(t,n){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",new wt(this.config).signIn(t,n));case 1:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"signInWithUsernameAndPassword",value:function(e,t){return new kt(this.config).signIn(e,t)}},{key:"linkAndRetrieveDataWithTicket",value:function(){var e=(0,f.default)(a.default.mark((function e(t){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this._anonymousAuthProvider||(this._anonymousAuthProvider=new _t(this.config)),Xe(ot,this._onAnonymousConverted),e.next=3,this._anonymousAuthProvider.linkAndRetrieveDataWithTicket(t);case 3:return e.abrupt("return",e.sent);case 4:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"signOut",value:function(){var e=(0,f.default)(a.default.mark((function e(){var t,n,r,i,o,s;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.loginType!==tt.ANONYMOUS){e.next=2;break}throw new ge({message:"匿名用户不支持登出操作"});case 2:if(t=this._cache.keys,n=t.refreshTokenKey,r=t.accessTokenKey,i=t.accessTokenExpireKey,o=this._cache.getStore(n),o){e.next=5;break}return e.abrupt("return");case 5:return e.next=7,this._request.send("auth.logout",{refresh_token:o});case 7:return s=e.sent,e.abrupt("return",(this._cache.removeStore(n),this._cache.removeStore(r),this._cache.removeStore(i),Ze(nt),Ze(it,{env:this.config.env,loginType:tt.NULL,persistence:this.config.persistence}),s));case 9:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"signUpWithEmailAndPassword",value:function(){var e=(0,f.default)(a.default.mark((function e(t,n){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._request.send("auth.signUpWithEmailAndPassword",{email:t,password:n}));case 1:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"sendPasswordResetEmail",value:function(){var e=(0,f.default)(a.default.mark((function e(t){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._request.send("auth.sendPasswordResetEmail",{email:t}));case 1:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"onLoginStateChanged",value:function(e){var t=this;Xe(nt,(function(){var n=t.hasLoginState();e.call(t,n)}));var n=this.hasLoginState();e.call(this,n)}},{key:"onLoginStateExpired",value:function(e){Xe(rt,e.bind(this))}},{key:"onAccessTokenRefreshed",value:function(e){Xe(at,e.bind(this))}},{key:"onAnonymousConverted",value:function(e){Xe(ot,e.bind(this))}},{key:"onLoginTypeChanged",value:function(e){var t=this;Xe(it,(function(){var n=t.hasLoginState();e.call(t,n)}))}},{key:"getAccessToken",value:function(){var e=(0,f.default)(a.default.mark((function e(){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._request.getAccessToken();case 2:return e.t0=e.sent.accessToken,e.t1=this.config.env,e.abrupt("return",{accessToken:e.t0,env:e.t1});case 5:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"hasLoginState",value:function(){var e=this._cache.keys,t=e.accessTokenKey,n=e.accessTokenExpireKey,r=this._cache.getStore(t),i=this._cache.getStore(n);return this._request.oauth.isAccessTokenExpired(r,i)?null:new yt(this.config.env)}},{key:"isUsernameRegistered",value:function(){var e=(0,f.default)(a.default.mark((function e(t){var n,r;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("string"==typeof t){e.next=2;break}throw new ge({code:"PARAM_ERROR",message:"username must be a string"});case 2:return e.next=4,this._request.send("auth.isUsernameRegistered",{username:t});case 4:return n=e.sent,r=n.data,e.abrupt("return",r&&r.isRegistered);case 7:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"getLoginState",value:function(){return Promise.resolve(this.hasLoginState())}},{key:"signInWithTicket",value:function(){var e=(0,f.default)(a.default.mark((function e(t){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",new bt(this.config).signIn(t));case 1:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"shouldRefreshAccessToken",value:function(e){this._request._shouldRefreshAccessTokenHook=e.bind(this)}},{key:"getUserInfo",value:function(){return this._request.send("auth.getUserInfo",{}).then((function(e){return e.code?e:x(x({},e.data),{},{requestId:e.seqId})}))}},{key:"getAuthHeader",value:function(){var e=this._cache.keys,t=e.refreshTokenKey,n=e.accessTokenKey,r=this._cache.getStore(t);return{"x-cloudbase-credentials":this._cache.getStore(n)+"/@@/"+r}}},{key:"_onAnonymousConverted",value:function(e){var t=e.data.env;t===this.config.env&&this._cache.updatePersistence(this.config.persistence)}},{key:"_onLoginTypeChanged",value:function(e){var t=e.data,n=t.loginType,r=t.persistence,i=t.env;i===this.config.env&&(this._cache.updatePersistence(r),this._cache.setStore(this._cache.keys.loginTypeKey,n))}}]),e}(),Ot=function(e,t){t=t||Ne();var n=vt(this.config.env),r=e.cloudPath,i=e.filePath,o=e.onUploadProgress,a=e.fileType,s=void 0===a?"image":a;return n.send("storage.getUploadMetadata",{path:r}).then((function(e){var a=e.data,c=a.url,u=a.authorization,l=a.token,f=a.fileId,d=a.cosFileId,p=e.requestId,h={key:r,signature:u,"x-cos-meta-fileid":d,success_action_status:"201","x-cos-security-token":l};n.upload({url:c,data:h,file:i,name:r,fileType:s,onUploadProgress:o}).then((function(e){201===e.statusCode?t(null,{fileID:f,requestId:p}):t(new ge({code:"STORAGE_REQUEST_FAIL",message:"STORAGE_REQUEST_FAIL: ".concat(e.data)}))})).catch((function(e){t(e)}))})).catch((function(e){t(e)})),t.promise},At=function(e,t){t=t||Ne();var n=vt(this.config.env),r=e.cloudPath;return n.send("storage.getUploadMetadata",{path:r}).then((function(e){t(null,e)})).catch((function(e){t(e)})),t.promise},St=function(e,t){var n=e.fileList;if(t=t||Ne(),!n||!Array.isArray(n))return{code:"INVALID_PARAM",message:"fileList必须是非空的数组"};var r,i=b(n);try{for(i.s();!(r=i.n()).done;){var o=r.value;if(!o||"string"!=typeof o)return{code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"}}}catch(s){i.e(s)}finally{i.f()}var a={fileid_list:n};return vt(this.config.env).send("storage.batchDeleteFile",a).then((function(e){e.code?t(null,e):t(null,{fileList:e.data.delete_list,requestId:e.requestId})})).catch((function(e){t(e)})),t.promise},Pt=function(e,t){var n=e.fileList;t=t||Ne(),n&&Array.isArray(n)||t(null,{code:"INVALID_PARAM",message:"fileList必须是非空的数组"});var r,i=[],o=b(n);try{for(o.s();!(r=o.n()).done;){var a=r.value;"object"==(0,u.default)(a)?(a.hasOwnProperty("fileID")&&a.hasOwnProperty("maxAge")||t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是包含fileID和maxAge的对象"}),i.push({fileid:a.fileID,max_age:a.maxAge})):"string"==typeof a?i.push({fileid:a}):t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是字符串"})}}catch(c){o.e(c)}finally{o.f()}var s={file_list:i};return vt(this.config.env).send("storage.batchGetDownloadUrl",s).then((function(e){e.code?t(null,e):t(null,{fileList:e.data.download_list,requestId:e.requestId})})).catch((function(e){t(e)})),t.promise},It=function(){var e=(0,f.default)(a.default.mark((function e(t,n){var r,i,o,s;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r=t.fileID,e.next=3,Pt.call(this,{fileList:[{fileID:r,maxAge:600}]});case 3:if(i=e.sent.fileList[0],"SUCCESS"===i.code){e.next=6;break}return e.abrupt("return",n?n(i):new Promise((function(e){e(i)})));case 6:if(o=vt(this.config.env),s=i.download_url,s=encodeURI(s),n){e.next=10;break}return e.abrupt("return",o.download({url:s}));case 10:return e.t0=n,e.next=13,o.download({url:s});case 13:e.t1=e.sent,(0,e.t0)(e.t1);case 15:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}(),Et=function(e,t){var n,r=e.name,i=e.data,o=e.query,a=e.parse,s=e.search,c=e.timeout,u=t||Ne();try{n=i?JSON.stringify(i):""}catch(r){return Promise.reject(r)}if(!r)return Promise.reject(new ge({code:"PARAM_ERROR",message:"函数名不能为空"}));var l={inQuery:o,parse:a,search:s,function_name:r,request_data:n};return vt(this.config.env).send("functions.invokeFunction",l,{timeout:c}).then((function(e){if(e.code)u(null,e);else{var t=e.data.response_data;if(a)u(null,{result:t,requestId:e.requestId});else try{t=JSON.parse(e.data.response_data),u(null,{result:t,requestId:e.requestId})}catch(e){u(new ge({message:"response data must be json"}))}}return u.promise})).catch((function(e){u(e)})),u.promise},Tt={timeout:15e3,persistence:"session"},jt={},Ct=function(){function e(t){(0,g.default)(this,e),this.config=t||this.config,this.authObj=void 0}return(0,y.default)(e,[{key:"init",value:function(t){switch(qe.adapter||(this.requestClient=new qe.adapter.reqClass({timeout:t.timeout||5e3,timeoutMsg:"请求在".concat((t.timeout||5e3)/1e3,"s内未完成，已中断")})),this.config=x(x({},Tt),t),!0){case this.config.timeout>6e5:console.warn("timeout大于可配置上限[10分钟]，已重置为上限数值"),this.config.timeout=6e5;break;case this.config.timeout<100:console.warn("timeout小于可配置下限[100ms]，已重置为下限数值"),this.config.timeout=100}return new e(this.config)}},{key:"auth",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.persistence;if(this.authObj)return this.authObj;var n,r=t||qe.adapter.primaryStorage||Tt.persistence;return r!==this.config.persistence&&(this.config.persistence=r),function(e){var t=e.env;ze[t]=new Ke(e),Je[t]=new Ke(x(x({},e),{},{persistence:"local"}))}(this.config),n=this.config,ht[n.env]=new pt(n),this.authObj=new xt(this.config),this.authObj}},{key:"on",value:function(e,t){return Xe.apply(this,[e,t])}},{key:"off",value:function(e,t){return et.apply(this,[e,t])}},{key:"callFunction",value:function(e,t){return Et.apply(this,[e,t])}},{key:"deleteFile",value:function(e,t){return St.apply(this,[e,t])}},{key:"getTempFileURL",value:function(e,t){return Pt.apply(this,[e,t])}},{key:"downloadFile",value:function(e,t){return It.apply(this,[e,t])}},{key:"uploadFile",value:function(e,t){return Ot.apply(this,[e,t])}},{key:"getUploadMetadata",value:function(e,t){return At.apply(this,[e,t])}},{key:"registerExtension",value:function(e){jt[e.name]=e}},{key:"invokeExtension",value:function(){var e=(0,f.default)(a.default.mark((function e(t,n){var r;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r=jt[t],r){e.next=3;break}throw new ge({message:"扩展".concat(t," 必须先注册")});case 3:return e.next=5,r.invoke(n,this);case 5:return e.abrupt("return",e.sent);case 6:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"useAdapters",value:function(e){var t=function(e){var t,n,r=(t=e,"[object Array]"===Object.prototype.toString.call(t)?e:[e]),i=b(r);try{for(i.s();!(n=i.n()).done;){var o=n.value,a=o.isMatch,s=o.genAdapter,c=o.runtime;if(a())return{adapter:s(),runtime:c}}}catch(u){i.e(u)}finally{i.f()}}(e)||{},n=t.adapter,r=t.runtime;n&&(qe.adapter=n),r&&(qe.runtime=r)}}]),e}(),Lt=new Ct;function $t(e,t,n){void 0===n&&(n={});var r=/\?/.test(t),i="";for(var o in n)""===i?!r&&(t+="?"):i+="&",i+=o+"="+encodeURIComponent(n[o]);return/^http(s)?:\/\//.test(t+=i)?t:""+e+t}var Dt=function(){function e(){(0,g.default)(this,e)}return(0,y.default)(e,[{key:"get",value:function(e){var t=e.url,n=e.data,r=e.headers,i=e.timeout;return new Promise((function(e,o){be.request({url:$t("https:",t),data:n,method:"GET",header:r,timeout:i,success:function(t){e(t)},fail:function(e){o(e)}})}))}},{key:"post",value:function(e){var t=e.url,n=e.data,r=e.headers,i=e.timeout;return new Promise((function(e,o){be.request({url:$t("https:",t),data:n,method:"POST",header:r,timeout:i,success:function(t){e(t)},fail:function(e){o(e)}})}))}},{key:"upload",value:function(e){return new Promise((function(t,n){var r=e.url,i=e.file,o=e.data,a=e.headers,s=e.fileType,c=be.uploadFile({url:$t("https:",r),name:"file",formData:Object.assign({},o),filePath:i,fileType:s,header:a,success:function(e){var n={statusCode:e.statusCode,data:e.data||{}};200===e.statusCode&&o.success_action_status&&(n.statusCode=parseInt(o.success_action_status,10)),t(n)},fail:function(e){n(new Error(e.errMsg||"uploadFile:fail"))}});"function"==typeof e.onUploadProgress&&c&&"function"==typeof c.onProgressUpdate&&c.onProgressUpdate((function(t){e.onUploadProgress({loaded:t.totalBytesSent,total:t.totalBytesExpectedToSend})}))}))}}]),e}(),Nt={setItem:function(e,t){be.setStorageSync(e,t)},getItem:function(e){return be.getStorageSync(e)},removeItem:function(e){be.removeStorageSync(e)},clear:function(){be.clearStorageSync()}},Rt={genAdapter:function(){return{root:{},reqClass:Dt,localStorage:Nt,primaryStorage:"local"}},isMatch:function(){return!0},runtime:"uni_app"};Lt.useAdapters(Rt);var Mt=Lt,Ut=Mt.init;Mt.init=function(e){e.env=e.spaceId;var t=Ut.call(this,e);t.config.provider="tencent",t.config.spaceId=e.spaceId;var n=t.auth;return t.auth=function(e){var t=n.call(this,e);return["linkAndRetrieveDataWithTicket","signInAnonymously","signOut","getAccessToken","getLoginState","signInWithTicket","getUserInfo"].forEach((function(e){var n;t[e]=(n=t[e],function(e){e=e||{};var t=me(e),r=t.success,i=t.fail,o=t.complete;if(!(r||i||o))return n.call(this,e);n.call(this,e).then((function(e){r&&r(e),o&&o(e)}),(function(e){i&&i(e),o&&o(e)}))}).bind(t)})),t},t.customAuth=t.auth,t};var Ft=Mt;function qt(e,t){return Bt.apply(this,arguments)}function Bt(){return Bt=(0,f.default)(a.default.mark((function e(t,n){var r,i,o;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r="http://".concat(t,":").concat(n,"/system/ping"),e.prev=1,e.next=4,o={url:r,timeout:500},new Promise((function(e,t){be.request(x(x({},o),{},{success:function(t){e(t)},fail:function(e){t(e)}}))}));case 4:return i=e.sent,e.abrupt("return",!(!i.data||0!==i.data.code));case 8:return e.prev=8,e.t0=e["catch"](1),e.abrupt("return",!1);case 11:case"end":return e.stop()}}),e,null,[[1,8]])}))),Bt.apply(this,arguments)}function Vt(e,t){return Ht.apply(this,arguments)}function Ht(){return Ht=(0,f.default)(a.default.mark((function e(t,n){var r,i,o;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:i=0;case 1:if(!(i<t.length)){e.next=11;break}return o=t[i],e.next=5,qt(o,n);case 5:if(!e.sent){e.next=8;break}return r=o,e.abrupt("break",11);case 8:i++,e.next=1;break;case 11:return e.abrupt("return",{address:r,port:n});case 12:case"end":return e.stop()}}),e)}))),Ht.apply(this,arguments)}var Kt={"serverless.file.resource.generateProximalSign":"storage/generate-proximal-sign","serverless.file.resource.report":"storage/report","serverless.file.resource.delete":"storage/delete","serverless.file.resource.getTempFileURL":"storage/get-temp-file-url"},zt=function(){function e(t){if((0,g.default)(this,e),["spaceId","clientSecret"].forEach((function(e){if(!Object.prototype.hasOwnProperty.call(t,e))throw new Error("".concat(e," required"))})),!t.endpoint)throw new Error("集群空间未配置ApiEndpoint，配置后需要重新关联服务空间后生效");this.config=Object.assign({},t),this.config.provider="dcloud",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.adapter=be}return(0,y.default)(e,[{key:"request",value:function(){var e=(0,f.default)(a.default.mark((function e(t){var n,r=this,i=arguments;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!(i.length>1&&void 0!==i[1])||i[1],n=!1,!n){e.next=8;break}return e.next=5,this.setupLocalRequest(t);case 5:e.t0=e.sent,e.next=9;break;case 8:e.t0=this.setupRequest(t);case 9:return t=e.t0,e.abrupt("return",Promise.resolve().then((function(){return n?r.requestLocal(t):Pe.wrappedRequest(t,r.adapter.request)})));case 11:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"requestLocal",value:function(e){var t=this;return new Promise((function(n,r){t.adapter.request(Object.assign(e,{complete:function(e){if(e||(e={}),!e.statusCode||e.statusCode>=400){var t=e.data&&e.data.code||"SYS_ERR",i=e.data&&e.data.message||"request:fail";return r(new ge({code:t,message:i}))}n({success:!0,result:e.data})}}))}))}},{key:"setupRequest",value:function(e){var t=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),n={"Content-Type":"application/json"};n["x-serverless-sign"]=Pe.sign(t,this.config.clientSecret);var r=Ae();n["x-client-info"]=encodeURIComponent(JSON.stringify(r));var i=we(),o=i.token;return n["x-client-token"]=o,{url:this.config.requestUrl,method:"POST",data:t,dataType:"json",header:JSON.parse(JSON.stringify(n))}}},{key:"setupLocalRequest",value:function(){var e=(0,f.default)(a.default.mark((function e(t){var n,r,i,o,s,c,u,l,f;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=Ae(),r=we(),i=r.token,o=Object.assign({},t,{spaceId:this.config.spaceId,timestamp:Date.now(),clientInfo:n,token:i}),s=this.__dev__&&this.__dev__.debugInfo||{},c=s.address,u=s.servePort,e.next=9,Vt(c,u);case 9:return l=e.sent,f=l.address,e.abrupt("return",{url:"http://".concat(f,":").concat(u,"/").concat(Kt[t.method]),method:"POST",data:o,dataType:"json",header:JSON.parse(JSON.stringify({"Content-Type":"application/json"}))});case 12:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"callFunction",value:function(e){var t={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:e.name,functionArgs:e.data||{}})};return this.request(t,!1)}},{key:"getUploadFileOptions",value:function(e){var t={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(e)};return this.request(t)}},{key:"reportUploadFile",value:function(e){var t={method:"serverless.file.resource.report",params:JSON.stringify(e)};return this.request(t)}},{key:"uploadFile",value:function(e){var t,n=this,r=e.filePath,i=e.cloudPath,o=e.fileType,a=void 0===o?"image":o,s=e.onUploadProgress;if(!i)throw new ge({code:"CLOUDPATH_REQUIRED",message:"cloudPath不可为空"});return this.getUploadFileOptions({cloudPath:i}).then((function(e){var i=e.result,o=i.url,c=i.formData,u=i.name;return t=e.result.fileUrl,new Promise((function(e,t){var i=n.adapter.uploadFile({url:o,formData:c,name:u,filePath:r,fileType:a,success:function(n){n&&n.statusCode<400?e(n):t(new ge({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail:function(e){t(new ge({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof s&&i&&"function"==typeof i.onProgressUpdate&&i.onProgressUpdate((function(e){s({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))})).then((function(){return n.reportUploadFile({cloudPath:i})})).then((function(e){return new Promise((function(n,i){e.success?n({success:!0,filePath:r,fileID:t}):i(new ge({code:"UPLOAD_FAILED",message:"文件上传失败"}))}))}))}},{key:"deleteFile",value:function(e){var t=e.fileList,n={method:"serverless.file.resource.delete",params:JSON.stringify({fileList:t})};return this.request(n).then((function(e){if(e.success)return e.result;throw new ge({code:"DELETE_FILE_FAILED",message:"删除文件失败"})}))}},{key:"getTempFileURL",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.fileList,n=e.maxAge;if(!Array.isArray(t)||0===t.length)throw new ge({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});var r={method:"serverless.file.resource.getTempFileURL",params:JSON.stringify({fileList:t,maxAge:n})};return this.request(r).then((function(e){if(e.success)return{fileList:e.result.fileList.map((function(e){return{fileID:e.fileID,tempFileURL:e.tempFileURL}}))};throw new ge({code:"GET_TEMP_FILE_URL_FAILED",message:"获取临时文件链接失败"})}))}}]),e}(),Jt={init:function(e){var t=new zt(e),n={signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return n},t.customAuth=t.auth,t}},Wt=A((function(e,t){e.exports=P.enc.Hex}));function Gt(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))}function Yt(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.data,r=t.functionName,i=t.method,o=t.headers,a=t.signHeaderKeys,s=void 0===a?[]:a,u=t.config,l=String(Date.now()),f=Gt(),d=Object.assign({},o,{"x-from-app-id":u.spaceAppId,"x-from-env-id":u.spaceId,"x-to-env-id":u.spaceId,"x-from-instance-id":l,"x-from-function-name":r,"x-client-timestamp":l,"x-alipay-source":"client","x-request-id":f,"x-alipay-callid":f,"x-trace-id":f}),p=["x-from-app-id","x-from-env-id","x-to-env-id","x-from-instance-id","x-from-function-name","x-client-timestamp"].concat(s),h=e.split("?")||[],v=(0,c.default)(h,2),m=v[0],g=void 0===m?"":m,y=v[1],_=void 0===y?"":y,b=function(e){var t="HMAC-SHA256",n=e.signedHeaders.join(";"),r=e.signedHeaders.map((function(t){return"".concat(t.toLowerCase(),":").concat(e.headers[t],"\n")})).join(""),i=$e(e.body).toString(Wt),o="".concat(e.method.toUpperCase(),"\n").concat(e.path,"\n").concat(e.query,"\n").concat(r,"\n").concat(n,"\n").concat(i,"\n"),a=$e(o).toString(Wt),s="".concat(t,"\n").concat(e.timestamp,"\n").concat(a,"\n"),c=De(s,e.secretKey).toString(Wt);return"".concat(t," Credential=").concat(e.secretId,", SignedHeaders=").concat(n,", Signature=").concat(c)}({path:g,query:_,method:i,headers:d,timestamp:l,body:JSON.stringify(n),secretId:u.accessKey,secretKey:u.secretKey,signedHeaders:p.sort()});return{url:"".concat(u.endpoint).concat(e),headers:Object.assign({},d,{Authorization:b})}}function Qt(e){var t=e.url,n=e.data,r=e.method,i=void 0===r?"POST":r,o=e.headers,a=void 0===o?{}:o,s=e.timeout;return new Promise((function(e,r){be.request({url:t,method:i,data:"object"==(0,u.default)(n)?JSON.stringify(n):n,header:a,dataType:"json",timeout:s,complete:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=a["x-trace-id"]||"";if(!t.statusCode||t.statusCode>=400){var i=t.data||{},o=i.message,s=i.errMsg,c=i.trace_id;return r(new ge({code:"SYS_ERR",message:o||s||"request:fail",requestId:c||n}))}e({status:t.statusCode,data:t.data,headers:t.header,requestId:n})}})}))}function Xt(e,t){var n=e.path,r=e.data,i=e.method,o=void 0===i?"GET":i,a=Yt(n,{functionName:"",data:r,method:o,headers:{"x-alipay-cloud-mode":"oss","x-data-api-type":"oss","x-expire-timestamp":Date.now()+6e4},signHeaderKeys:["x-data-api-type","x-expire-timestamp"],config:t}),s=a.url,c=a.headers;return Qt({url:s,data:r,method:o,headers:c}).then((function(e){var t=e.data||{};if(!t.success)throw new ge({code:e.errCode,message:e.errMsg,requestId:e.requestId});return t.data||{}})).catch((function(e){throw new ge({code:e.errCode,message:e.errMsg,requestId:e.requestId})}))}function Zt(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=e.trim().replace(/^cloud:\/\//,""),n=t.indexOf("/");if(n<=0)throw new ge({code:"INVALID_PARAM",message:"fileID不合法"});var r=t.substring(0,n),i=t.substring(n+1);return r!==this.config.spaceId&&console.warn("file ".concat(e," does not belong to env ").concat(this.config.spaceId)),i}function en(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return"cloud://".concat(this.config.spaceId,"/").concat(e.replace(/^\/+/,""))}var tn=function(){function e(t){(0,g.default)(this,e),this.config=t}return(0,y.default)(e,[{key:"signedURL",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n="/ws/function/".concat(e),r=this.config.wsEndpoint.replace(/^ws(s)?:\/\//,""),i=Object.assign({},t,{accessKeyId:this.config.accessKey,signatureNonce:Gt(),timestamp:""+Date.now()}),o=[n,["accessKeyId","authorization","signatureNonce","timestamp"].sort().map((function(e){return i[e]?"".concat(e,"=").concat(i[e]):null})).filter(Boolean).join("&"),"host:".concat(r)].join("\n"),a=["HMAC-SHA256",$e(o).toString(Wt)].join("\n"),s=De(a,this.config.secretKey).toString(Wt),c=Object.keys(i).map((function(e){return"".concat(e,"=").concat(encodeURIComponent(i[e]))})).join("&");return"".concat(this.config.wsEndpoint).concat(n,"?").concat(c,"&signature=").concat(s)}}]),e}(),nn=function(){function e(t){if((0,g.default)(this,e),["spaceId","spaceAppId","accessKey","secretKey"].forEach((function(e){if(!Object.prototype.hasOwnProperty.call(t,e))throw new Error("".concat(e," required"))})),t.endpoint){if("string"!=typeof t.endpoint)throw new Error("endpoint must be string");if(!/^https:\/\//.test(t.endpoint))throw new Error("endpoint must start with https://");t.endpoint=t.endpoint.replace(/\/$/,"")}this.config=Object.assign({},t,{endpoint:t.endpoint||"https://".concat(t.spaceId,".api-hz.cloudbasefunction.cn"),wsEndpoint:t.wsEndpoint||"wss://".concat(t.spaceId,".api-hz.cloudbasefunction.cn")}),this._websocket=new tn(this.config)}return(0,y.default)(e,[{key:"callFunction",value:function(e){return function(e,t){var n=e.name,r=e.data,i=e.async,o=void 0!==i&&i,a=e.timeout,s="POST",c={"x-to-function-name":n};o&&(c["x-function-invoke-type"]="async");var u=Yt("/functions/invokeFunction",{functionName:n,data:r,method:s,headers:c,signHeaderKeys:["x-to-function-name"],config:t}),l=u.url,f=u.headers;return Qt({url:l,data:r,method:s,headers:f,timeout:a}).then((function(e){var t=0;if(o){var n=e.data||{};t="200"===n.errCode?0:n.errCode,e.data=n.data||{},e.errMsg=n.errMsg}if(0!==t)throw new ge({code:t,message:e.errMsg,requestId:e.requestId});return{errCode:t,success:0===t,requestId:e.requestId,result:e.data}})).catch((function(e){throw new ge({code:e.errCode,message:e.errMsg,requestId:e.requestId})}))}(e,this.config)}},{key:"uploadFileToOSS",value:function(e){var t=e.url,n=e.filePath,r=e.fileType,i=e.formData,o=e.onUploadProgress;return new Promise((function(e,a){var s=be.uploadFile({url:t,filePath:n,fileType:r,formData:i,name:"file",success:function(t){t&&t.statusCode<400?e(t):a(new ge({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail:function(e){a(new ge({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof o&&s&&"function"==typeof s.onProgressUpdate&&s.onProgressUpdate((function(e){o({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}},{key:"uploadFile",value:function(){var e=(0,f.default)(a.default.mark((function e(t){var n,r,i,o,s,c,u,l,f,d,p;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=t.filePath,r=t.cloudPath,i=void 0===r?"":r,o=t.fileType,s=void 0===o?"image":o,c=t.onUploadProgress,"string"===R(i)){e.next=3;break}throw new ge({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});case 3:if(i=i.trim()){e.next=5;break}throw new ge({code:"INVALID_PARAM",message:"cloudPath不可为空"});case 5:if(!/:\/\//.test(i)){e.next=7;break}throw new ge({code:"INVALID_PARAM",message:"cloudPath不合法"});case 7:return e.next=9,Xt({path:"/".concat(i.replace(/^\//,""),"?post_url")},this.config);case 9:return u=e.sent,l=u.file_id,f=u.upload_url,d=u.form_data,p=d&&d.reduce((function(e,t){return e[t.key]=t.value,e}),{}),e.abrupt("return",this.uploadFileToOSS({url:f,filePath:n,fileType:s,formData:p,onUploadProgress:c}).then((function(){return{fileID:l}})));case 15:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"getTempFileURL",value:function(){var e=(0,f.default)(a.default.mark((function e(t){var n,r=this;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=t.fileList,e.abrupt("return",new Promise((function(e,t){(!n||n.length<0)&&e({code:"INVALID_PARAM",message:"fileList不能为空数组"}),n.length>50&&e({code:"INVALID_PARAM",message:"fileList数组长度不能超过50"});var i,o=[],a=b(n);try{for(a.s();!(i=a.n()).done;){var s=i.value,c=void 0;"string"!==R(s)&&e({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});try{c=Zt.call(r,s)}catch(e){console.warn(e.errCode,e.errMsg),c=s}o.push({file_id:c,expire:600})}}catch(u){a.e(u)}finally{a.f()}Xt({path:"/?download_url",data:{file_list:o},method:"POST"},r.config).then((function(t){var n=t.file_list,i=void 0===n?[]:n;e({fileList:i.map((function(e){return{fileID:en.call(r,e.file_id),tempFileURL:e.download_url}}))})})).catch((function(e){return t(e)}))})));case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()},{key:"connectWebSocket",value:function(){var e=(0,f.default)(a.default.mark((function e(t){var n,r;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=t.name,r=t.query,e.abrupt("return",be.connectSocket({url:this._websocket.signedURL(n,r),complete:function(){}}));case 2:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()}]),e}(),rn={init:function(e){e.provider="alipay";var t=new nn(e);return t.auth=function(){return{signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!0)}}},t}};function on(e){var t,n=e.data;t=Ae();var r=JSON.parse(JSON.stringify(n||{}));if(Object.assign(r,{clientInfo:t}),!r.uniIdToken){var i=we(),o=i.token;o&&(r.uniIdToken=o)}return r}var an=[{rule:/fc_function_not_found|FUNCTION_NOT_FOUND/,content:"，云函数[{functionName}]在云端不存在，请检查此云函数名称是否正确以及该云函数是否已上传到服务空间",mode:"append"}],sn=/[\\^$.*+?()[\]{}|]/g,cn=RegExp(sn.source);function un(e,t,n){return e.replace(new RegExp((r=t)&&cn.test(r)?r.replace(sn,"\\$&"):r,"g"),n);var r}var ln={NONE:"none",REQUEST:"request",RESPONSE:"response",BOTH:"both"},fn="_globalUniCloudStatus",dn="_globalUniCloudSecureNetworkCache__{spaceId}",pn="uni-secure-network",hn={SYSTEM_ERROR:{code:2e4,message:"System error"},APP_INFO_INVALID:{code:20101,message:"Invalid client"},GET_ENCRYPT_KEY_FAILED:{code:20102,message:"Get encrypt key failed"}};function vn(e){var t=e||{},n=t.errSubject,r=t.subject,i=t.errCode,o=t.errMsg,a=t.code,s=t.message,c=t.cause;return new ge({subject:n||r||pn,code:i||a||hn.SYSTEM_ERROR.code,message:o||s,cause:c})}var mn;mn="0123456789abcdef";var gn;function yn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.secretType;return t===ln.REQUEST||t===ln.RESPONSE||t===ln.BOTH}function _n(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.name,n=e.data,r=void 0===n?{}:n;return"app"===W&&"DCloud-clientDB"===t&&"encryption"===r.redirectTo&&"getAppClientKey"===r.action}function bn(e){e.functionName,e.result,e.logPvd}function wn(e){var t=e.callFunction,n=function(n){var r=this,i=n.name;n.data=on.call(e,{data:n.data});var o={aliyun:"aliyun",tencent:"tcb",tcb:"tcb",alipay:"alipay",dcloud:"dcloud"}[this.config.provider],a=yn(n),s=_n(n),c=a||s;return t.call(this,n).then((function(e){return e.errCode=0,!c&&bn.call(r,{functionName:i,result:e,logPvd:o}),Promise.resolve(e)}),(function(e){return!c&&bn.call(r,{functionName:i,result:e,logPvd:o}),e&&e.message&&(e.message=function(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.message,n=void 0===t?"":t,r=e.extraInfo,i=void 0===r?{}:r,o=e.formatter,a=void 0===o?[]:o,s=0;s<a.length;s++){var c=a[s],u=c.rule,l=c.content,f=c.mode,d=n.match(u);if(d){for(var p=l,h=1;h<d.length;h++)p=un(p,"{$".concat(h,"}"),d[h]);for(var v in i)p=un(p,"{".concat(v,"}"),i[v]);return"replace"===f?p:n+p}}return n}({message:"[".concat(n.name,"]: ").concat(e.message),formatter:an,extraInfo:{functionName:i}})),Promise.reject(e)}))};e.callFunction=function(t){var r,i,o=e.config,a=o.provider,s=o.spaceId,c=t.name;return t.data=t.data||{},r=n,r=r.bind(e),i=_n(t)?n.call(e,t):function(e){var t=e.name,n=e.data,r=void 0===n?{}:n;return"mp-weixin"===W&&"uni-id-co"===t&&"secureNetworkHandshakeByWeixin"===r.method}(t)?r.call(e,t):yn(t)?new gn({secretType:t.secretType,uniCloudIns:e}).wrapEncryptDataCallFunction(n.bind(e))(t):function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.provider,n=e.spaceId,r=e.functionName,i=xe(),o=i.appId,a=i.uniPlatform,s=i.osName,c=a;"app"===a&&(c=s);var u=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.provider,n=e.spaceId,r=J;if(!r)return{};t=function(e){return"tencent"===e?"tcb":e}(t);var i=r.find((function(e){return e.provider===t&&e.spaceId===n}));return i&&i.config}({provider:t,spaceId:n});if(!u||!u.accessControl||!u.accessControl.enable)return!1;var l=u.accessControl.function||{},f=Object.keys(l);if(0===f.length)return!0;var d=function(e,t){for(var n,r,i,o=0;o<e.length;o++){var a=e[o];a!==t?"*"!==a?a.split(",").map((function(e){return e.trim()})).indexOf(t)>-1&&(r=a):i=a:n=a}return n||r||i}(f,r);if(!d)return!1;if((l[d]||[]).find((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return e.appId===o&&(e.platform||"").toLowerCase()===c.toLowerCase()})))return!0;throw console.error("此应用[appId: ".concat(o,", platform: ").concat(c,"]不在云端配置的允许访问的应用列表内，参考：https://uniapp.dcloud.net.cn/uniCloud/secure-network.html#verify-client")),vn(hn.APP_INFO_INVALID)}({provider:a,spaceId:s,functionName:c})?new gn({secretType:t.secretType,uniCloudIns:e}).wrapVerifyClientCallFunction(n.bind(e))(t):r(t),Object.defineProperty(i,"result",{get:function(){return console.warn("当前返回结果为Promise类型，不可直接访问其result属性，详情请参考：https://uniapp.dcloud.net.cn/uniCloud/faq?id=promise"),{}}}),i.then((function(e){return e}))}}gn="mp-weixin"!==W&&"app"!==W?function(){return(0,y.default)((function e(){throw(0,g.default)(this,e),vn({message:"Platform ".concat(W," is not supported by secure network")})}))}():function(){return(0,y.default)((function e(){throw(0,g.default)(this,e),vn({message:"Platform ".concat(W," is not enabled, please check whether secure network module is enabled in your manifest.json")})}))}();var kn=Symbol("CLIENT_DB_INTERNAL");function xn(e,t){return e.then="DoNotReturnProxyWithAFunctionNamedThen",e._internalType=kn,e.inspect=null,e.__ob__=void 0,new Proxy(e,{get:function(e,n,r){if("_uniClient"===n)return null;if("symbol"==(0,u.default)(n))return e[n];if(n in e||"string"!=typeof n){var i=e[n];return"function"==typeof i?i.bind(e):i}return t.get(e,n,r)}})}function On(e){return{on:function(t,n){e[t]=e[t]||[],e[t].indexOf(n)>-1||e[t].push(n)},off:function(t,n){e[t]=e[t]||[];var r=e[t].indexOf(n);-1!==r&&e[t].splice(r,1)}}}var An=["db.Geo","db.command","command.aggregate"];function Sn(e,t){return An.indexOf("".concat(e,".").concat(t))>-1}function Pn(e){switch(R(e)){case"array":return e.map((function(e){return Pn(e)}));case"object":return e._internalType===kn||Object.keys(e).forEach((function(t){e[t]=Pn(e[t])})),e;case"regexp":return{$regexp:{source:e.source,flags:e.flags}};case"date":return{$date:e.toISOString()};default:return e}}function In(e){return e&&e.content&&e.content.$method}var En=function(){function e(t,n,r){(0,g.default)(this,e),this.content=t,this.prevStage=n||null,this.udb=null,this._database=r}return(0,y.default)(e,[{key:"toJSON",value:function(){for(var e=this,t=[e.content];e.prevStage;)e=e.prevStage,t.push(e.content);return{$db:t.reverse().map((function(e){return{$method:e.$method,$param:Pn(e.$param)}}))}}},{key:"toString",value:function(){return JSON.stringify(this.toJSON())}},{key:"getAction",value:function(){var e=this.toJSON().$db.find((function(e){return"action"===e.$method}));return e&&e.$param&&e.$param[0]}},{key:"getCommand",value:function(){return{$db:this.toJSON().$db.filter((function(e){return"action"!==e.$method}))}}},{key:"isAggregate",get:function(){for(var e=this;e;){var t=In(e),n=In(e.prevStage);if("aggregate"===t&&"collection"===n||"pipeline"===t)return!0;e=e.prevStage}return!1}},{key:"isCommand",get:function(){for(var e=this;e;){if("command"===In(e))return!0;e=e.prevStage}return!1}},{key:"isAggregateCommand",get:function(){for(var e=this;e;){var t=In(e),n=In(e.prevStage);if("aggregate"===t&&"command"===n)return!0;e=e.prevStage}return!1}},{key:"getNextStageFn",value:function(e){var t=this;return function(){return Tn({$method:e,$param:Pn(Array.from(arguments))},t,t._database)}}},{key:"count",get:function(){return this.isAggregate?this.getNextStageFn("count"):function(){return this._send("count",Array.from(arguments))}}},{key:"remove",get:function(){return this.isCommand?this.getNextStageFn("remove"):function(){return this._send("remove",Array.from(arguments))}}},{key:"get",value:function(){return this._send("get",Array.from(arguments))}},{key:"add",get:function(){return this.isCommand?this.getNextStageFn("add"):function(){return this._send("add",Array.from(arguments))}}},{key:"update",value:function(){return this._send("update",Array.from(arguments))}},{key:"end",value:function(){return this._send("end",Array.from(arguments))}},{key:"set",get:function(){return this.isCommand?this.getNextStageFn("set"):function(){throw new Error("JQL禁止使用set方法")}}},{key:"_send",value:function(e,t){var n=this.getAction(),r=this.getCommand();return r.$db.push({$method:e,$param:Pn(t)}),this._database._callCloudFunction({action:n,command:r})}}]),e}();function Tn(e,t,n){return xn(new En(e,t,n),{get:function(e,t){var r="db";return e&&e.content&&(r=e.content.$method),Sn(r,t)?Tn({$method:t},e,n):function(){return Tn({$method:t,$param:Pn(Array.from(arguments))},e,n)}}})}function jn(e){var t=e.path,n=e.method;return function(){function e(){(0,g.default)(this,e),this.param=Array.from(arguments)}return(0,y.default)(e,[{key:"toJSON",value:function(){return{$newDb:[].concat((0,l.default)(t.map((function(e){return{$method:e}}))),[{$method:n,$param:this.param}])}}},{key:"toString",value:function(){return JSON.stringify(this.toJSON())}}]),e}()}var Cn=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.uniClient,r=void 0===n?{}:n,i=t.isJQL,o=void 0!==i&&i;(0,g.default)(this,e),this._uniClient=r,this._authCallBacks={},this._dbCallBacks={},r._isDefault&&(this._dbCallBacks=X("_globalUniCloudDatabaseCallback")),o||(this.auth=On(this._authCallBacks)),this._isJQL=o,Object.assign(this,On(this._dbCallBacks)),this.env=xn({},{get:function(e,t){return{$env:t}}}),this.Geo=xn({},{get:function(e,t){return jn({path:["Geo"],method:t})}}),this.serverDate=jn({path:[],method:"serverDate"}),this.RegExp=jn({path:[],method:"RegExp"})}return(0,y.default)(e,[{key:"getCloudEnv",value:function(e){if("string"!=typeof e||!e.trim())throw new Error("getCloudEnv参数错误");return{$env:e.replace("$cloudEnv_","")}}},{key:"_callback",value:function(e,t){var n=this._dbCallBacks;n[e]&&n[e].forEach((function(e){e.apply(void 0,(0,l.default)(t))}))}},{key:"_callbackAuth",value:function(e,t){var n=this._authCallBacks;n[e]&&n[e].forEach((function(e){e.apply(void 0,(0,l.default)(t))}))}},{key:"multiSend",value:function(){var e=Array.from(arguments),t=e.map((function(e){var t=e.getAction(),n=e.getCommand();if("getTemp"!==n.$db[n.$db.length-1].$method)throw new Error("multiSend只支持子命令内使用getTemp");return{action:t,command:n}}));return this._callCloudFunction({multiCommand:t,queryList:e})}}]),e}();function Ln(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return xn(new e(t),{get:function(e,t){return Sn("db",t)?Tn({$method:t},null,e):function(){return Tn({$method:t,$param:Pn(Array.from(arguments))},null,e)}}})}var $n=function(e){(0,p.default)(n,e);var t=O(n);function n(){return(0,g.default)(this,n),t.apply(this,arguments)}return(0,y.default)(n,[{key:"_parseResult",value:function(e){return this._isJQL?e.result:e}},{key:"_callCloudFunction",value:function(e){var t=this,n=e.action,r=e.command,i=e.multiCommand,o=e.queryList;function a(e,t){if(i&&o)for(var n=0;n<o.length;n++){var r=o[n];r.udb&&"function"==typeof r.udb.setResult&&(t?r.udb.setResult(t):r.udb.setResult(e.result.dataList[n]))}}var s=this,c=this._isJQL?"databaseForJQL":"database";function u(e){return s._callback("error",[e]),re(ie(c,"fail"),e).then((function(){return re(ie(c,"complete"),e)})).then((function(){return a(null,e),de(se.RESPONSE,{type:ce.CLIENT_DB,content:e}),Promise.reject(e)}))}var l=re(ie(c,"invoke")),f=this._uniClient;return l.then((function(){return f.callFunction({name:"DCloud-clientDB",type:L.CLIENT_DB,data:{action:n,command:r,multiCommand:i}})})).then((function(e){var n=e.result,r=n.code,i=n.message,o=n.token,l=n.tokenExpired,f=n.systemInfo,d=void 0===f?[]:f;if(d)for(var p=0;p<d.length;p++){var h=d[p],v=h.level,m=h.message,g=h.detail,y="[System Info]"+m;g&&(y="".concat(y,"\n详细信息：").concat(g)),(console["app"===W&&"warn"===v?"error":v]||console.log)(y)}if(r)return u(new ge({code:r,message:i,requestId:e.requestId}));e.result.errCode=e.result.errCode||e.result.code,e.result.errMsg=e.result.errMsg||e.result.message,o&&l&&(ke({token:o,tokenExpired:l}),t._callbackAuth("refreshToken",[{token:o,tokenExpired:l}]),t._callback("refreshToken",[{token:o,tokenExpired:l}]),de(se.REFRESH_TOKEN,{token:o,tokenExpired:l}));for(var _=[{prop:"affectedDocs",tips:"affectedDocs不再推荐使用，请使用inserted/deleted/updated/data.length替代"},{prop:"code",tips:"code不再推荐使用，请使用errCode替代"},{prop:"message",tips:"message不再推荐使用，请使用errMsg替代"}],b=function(t){var n=_[t],r=n.prop,i=n.tips;if(r in e.result){var o=e.result[r];Object.defineProperty(e.result,r,{get:function(){return console.warn(i),o}})}},w=0;w<_.length;w++)b(w);return function(e){return re(ie(c,"success"),e).then((function(){return re(ie(c,"complete"),e)})).then((function(){a(e,null);var t=s._parseResult(e);return de(se.RESPONSE,{type:ce.CLIENT_DB,content:t}),Promise.resolve(t)}))}(e)}),(function(e){return/fc_function_not_found|FUNCTION_NOT_FOUND/g.test(e.message)&&console.warn("clientDB未初始化，请在web控制台保存一次schema以开启clientDB"),u(new ge({code:e.code||"SYSTEM_ERROR",message:e.message,requestId:e.requestId}))}))}}]),n}(Cn),Dn="token无效，跳转登录页面",Nn="token过期，跳转登录页面",Rn={TOKEN_INVALID_TOKEN_EXPIRED:Nn,TOKEN_INVALID_INVALID_CLIENTID:Dn,TOKEN_INVALID:Dn,TOKEN_INVALID_WRONG_TOKEN:Dn,TOKEN_INVALID_ANONYMOUS_USER:Dn},Mn={"uni-id-token-expired":Nn,"uni-id-check-token-failed":Dn,"uni-id-token-not-exist":Dn,"uni-id-check-device-feature-failed":Dn},Un=x(x(x({},Rn),Mn),{},{default:"用户未登录或登录状态过期，自动跳转登录页面"});function Fn(e,t){var n="";return n=e?"".concat(e,"/").concat(t):t,n.replace(/^\//,"")}function qn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=[],r=[];return e.forEach((function(e){!0===e.needLogin?n.push(Fn(t,e.path)):!1===e.needLogin&&r.push(Fn(t,e.path))})),{needLoginPage:n,notNeedLoginPage:r}}function Bn(e){return e.split("?")[0].replace(/^\//,"")}function Vn(){return function(e){var t=e&&e.$page&&e.$page.fullPath;return t?("/"!==t.charAt(0)&&(t="/"+t),t):""}(function(){var e=getCurrentPages();return e[e.length-1]}())}function Hn(){return Bn(Vn())}function Kn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!e)return!1;if(!(t&&t.list&&t.list.length))return!1;var n=t.list,r=Bn(e);return n.some((function(e){return e.pagePath===r}))}var zn,Jn=!!_.default.uniIdRouter,Wn=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:_.default,t=e.pages,n=void 0===t?[]:t,r=e.subPackages,i=void 0===r?[]:r,o=e.uniIdRouter,a=void 0===o?{}:o,s=e.tabBar,c=void 0===s?{}:s,u=a.loginPage,f=a.needLogin,d=void 0===f?[]:f,p=a.resToLogin,h=void 0===p||p,v=qn(n),m=v.needLoginPage,g=v.notNeedLoginPage,y=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=[],n=[];return e.forEach((function(e){var r=e.root,i=e.pages,o=void 0===i?[]:i,a=qn(o,r),s=a.needLoginPage,c=a.notNeedLoginPage;t.push.apply(t,(0,l.default)(s)),n.push.apply(n,(0,l.default)(c))})),{needLoginPage:t,notNeedLoginPage:n}}(i),b=y.needLoginPage,w=y.notNeedLoginPage;return{loginPage:u,routerNeedLogin:d,resToLogin:h,needLoginPage:[].concat((0,l.default)(m),(0,l.default)(b)),notNeedLoginPage:[].concat((0,l.default)(g),(0,l.default)(w)),loginPageInTabBar:Kn(u,c)}}(),Gn=Wn.loginPage,Yn=Wn.routerNeedLogin,Qn=Wn.resToLogin,Xn=Wn.needLoginPage,Zn=Wn.notNeedLoginPage,er=Wn.loginPageInTabBar;if(Xn.indexOf(Gn)>-1)throw new Error("Login page [".concat(Gn,'] should not be "needLogin", please check your pages.json'));function tr(e){var t=Hn();if("/"===e.charAt(0))return e;var n=e.split("?"),r=(0,c.default)(n,2),i=r[0],o=r[1],a=i.replace(/^\//,"").split("/"),s=t.split("/");s.pop();for(var u=0;u<a.length;u++){var l=a[u];".."===l?s.pop():"."!==l&&s.push(l)}return""===s[0]&&s.shift(),"/"+s.join("/")+(o?"?"+o:"")}function nr(e){var t=Bn(tr(e));return!(Zn.indexOf(t)>-1)&&(Xn.indexOf(t)>-1||Yn.some((function(t){return function(e,t){return new RegExp(t).test(e)}(e,t)})))}function rr(e){var t=e.redirect,n=Bn(t),r=Bn(Gn);return Hn()!==r&&n!==r}function ir(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.api,n=e.redirect;if(n&&rr({redirect:n})){var i=function(e,t){return"/"!==e.charAt(0)&&(e="/"+e),t?e.indexOf("?")>-1?e+"&uniIdRedirectUrl=".concat(encodeURIComponent(t)):e+"?uniIdRedirectUrl=".concat(encodeURIComponent(t)):e}(Gn,n);er?"navigateTo"!==t&&"redirectTo"!==t||(t="switchTab"):"switchTab"===t&&(t="navigateTo");var o={navigateTo:r.navigateTo,redirectTo:r.redirectTo,switchTab:r.switchTab,reLaunch:r.reLaunch};setTimeout((function(){o[t]({url:i})}),0)}}function or(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.url,n={abortLoginPageJump:!1,autoToLoginPage:!1},r=function(){var e,t=we(),n=t.token,r=t.tokenExpired;if(n){if(r<Date.now()){var i="uni-id-token-expired";e={errCode:i,errMsg:Un[i]}}}else{var o="uni-id-check-token-failed";e={errCode:o,errMsg:Un[o]}}return e}();if(nr(t)&&r){if(r.uniIdRedirectUrl=t,ue(se.NEED_LOGIN).length>0)return setTimeout((function(){de(se.NEED_LOGIN,r)}),0),n.abortLoginPageJump=!0,n;n.autoToLoginPage=!0}return n}function ar(){!function(){var e=Vn(),t=or({url:e}),n=t.abortLoginPageJump,r=t.autoToLoginPage;n||r&&ir({api:"redirectTo",redirect:e})}();for(var e=["navigateTo","redirectTo","reLaunch","switchTab"],t=function(t){var n=e[t];r.addInterceptor(n,{invoke:function(e){var t=or({url:e.url}),r=t.abortLoginPageJump,i=t.autoToLoginPage;return r?e:i?(ir({api:n,redirect:tr(e.url)}),!1):e}})},n=0;n<e.length;n++)t(n)}function sr(){this.onResponse((function(e){var t=e.type,n=e.content,r=!1;switch(t){case"cloudobject":r=function(e){if("object"!=(0,u.default)(e))return!1;var t=e||{},n=t.errCode;return n in Un}(n);break;case"clientdb":r=function(e){if("object"!=(0,u.default)(e))return!1;var t=e||{},n=t.errCode;return n in Rn}(n)}r&&function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=ue(se.NEED_LOGIN);ve().then((function(){var n=Vn();if(n&&rr({redirect:n}))return t.length>0?de(se.NEED_LOGIN,Object.assign({uniIdRedirectUrl:n},e)):void(Gn&&ir({api:"navigateTo",redirect:n}))}))}(n)}))}function cr(e){!function(e){e.onResponse=function(e){le(se.RESPONSE,e)},e.offResponse=function(e){fe(se.RESPONSE,e)}}(e),function(e){e.onNeedLogin=function(e){le(se.NEED_LOGIN,e)},e.offNeedLogin=function(e){fe(se.NEED_LOGIN,e)},Jn&&(X(fn).needLoginInit||(X(fn).needLoginInit=!0,ve().then((function(){ar.call(e)})),Qn&&sr.call(e)))}(e),function(e){e.onRefreshToken=function(e){le(se.REFRESH_TOKEN,e)},e.offRefreshToken=function(e){fe(se.REFRESH_TOKEN,e)}}(e)}var ur="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",lr=/^(?:[A-Za-z\d+/]{4})*?(?:[A-Za-z\d+/]{2}(?:==)?|[A-Za-z\d+/]{3}=?)?$/;function fr(){var e,t,n=we().token||"",r=n.split(".");if(!n||3!==r.length)return{uid:null,role:[],permission:[],tokenExpired:0};try{e=JSON.parse((t=r[1],decodeURIComponent(zn(t).split("").map((function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)})).join(""))))}catch(n){throw new Error("获取当前用户信息出错，详细错误信息为："+n.message)}return e.tokenExpired=1e3*e.exp,delete e.exp,delete e.iat,e}zn="function"!=typeof atob?function(e){if(e=String(e).replace(/[\t\n\f\r ]+/g,""),!lr.test(e))throw new Error("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.");var t;e+="==".slice(2-(3&e.length));for(var n,r,i="",o=0;o<e.length;)t=ur.indexOf(e.charAt(o++))<<18|ur.indexOf(e.charAt(o++))<<12|(n=ur.indexOf(e.charAt(o++)))<<6|(r=ur.indexOf(e.charAt(o++))),i+=64===n?String.fromCharCode(t>>16&255):64===r?String.fromCharCode(t>>16&255,t>>8&255):String.fromCharCode(t>>16&255,t>>8&255,255&t);return i}:atob;var dr=A((function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var n="chooseAndUploadFile:ok",o="chooseAndUploadFile:fail";function a(e,t){return e.tempFiles.forEach((function(e,n){e.name||(e.name=e.path.substring(e.path.lastIndexOf("/")+1)),t&&(e.fileType=t),e.cloudPath=Date.now()+"_"+n+e.name.substring(e.name.lastIndexOf("."))})),e.tempFilePaths||(e.tempFilePaths=e.tempFiles.map((function(e){return e.path}))),e}function s(e,t,r){var i=r.onChooseFile,o=r.onUploadProgress;return t.then((function(e){if(i){var t=i(e);if(void 0!==t)return Promise.resolve(t).then((function(t){return void 0===t?e:t}))}return e})).then((function(t){return!1===t?{errMsg:n,tempFilePaths:[],tempFiles:[]}:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,i=arguments.length>3?arguments[3]:void 0;(t=Object.assign({},t)).errMsg=n;var o=t.tempFiles,a=o.length,s=0;return new Promise((function(n){for(;s<r;)c();function c(){var r=s++;if(r>=a)!o.find((function(e){return!e.url&&!e.errMsg}))&&n(t);else{var u=o[r];e.uploadFile({provider:u.provider,filePath:u.path,cloudPath:u.cloudPath,fileType:u.fileType,cloudPathAsRealPath:u.cloudPathAsRealPath,onUploadProgress:function(e){e.index=r,e.tempFile=u,e.tempFilePath=u.path,i&&i(e)}}).then((function(e){u.url=e.fileID,r<a&&c()})).catch((function(e){u.errMsg=e.errMsg||e.message,r<a&&c()}))}}}))}(e,t,5,o)}))}t.initChooseAndUploadFile=function(e){return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{type:"all"};return"image"===t.type?s(e,function(e){var t=e.count,n=e.sizeType,i=e.sourceType,s=void 0===i?["album","camera"]:i,c=e.extension;return new Promise((function(e,i){r.chooseImage({count:t,sizeType:n,sourceType:s,extension:c,success:function(t){e(a(t,"image"))},fail:function(e){i({errMsg:e.errMsg.replace("chooseImage:fail",o)})}})}))}(t),t):"video"===t.type?s(e,function(e){var t=e.camera,n=e.compressed,i=e.maxDuration,s=e.sourceType,c=void 0===s?["album","camera"]:s,u=e.extension;return new Promise((function(e,s){r.chooseVideo({camera:t,compressed:n,maxDuration:i,sourceType:c,extension:u,success:function(t){var n=t.tempFilePath,r=t.duration,i=t.size,o=t.height,s=t.width;e(a({errMsg:"chooseVideo:ok",tempFilePaths:[n],tempFiles:[{name:t.tempFile&&t.tempFile.name||"",path:n,size:i,type:t.tempFile&&t.tempFile.type||"",width:s,height:o,duration:r,fileType:"video",cloudPath:""}]},"video"))},fail:function(e){s({errMsg:e.errMsg.replace("chooseVideo:fail",o)})}})}))}(t),t):s(e,function(e){var t=e.count,n=e.extension;return new Promise((function(e,s){var c=r.chooseFile;if("undefined"!=typeof i&&"function"==typeof i.chooseMessageFile&&(c=i.chooseMessageFile),"function"!=typeof c)return s({errMsg:o+" 请指定 type 类型，该平台仅支持选择 image 或 video。"});c({type:"all",count:t,extension:n,success:function(t){e(a(t))},fail:function(e){s({errMsg:e.errMsg.replace("chooseFile:fail",o)})}})}))}(t),t)}}})),pr=function(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}(dr),hr={auto:"auto",onready:"onready",manual:"manual"};function vr(e){return{props:{localdata:{type:Array,default:function(){return[]}},options:{type:[Object,Array],default:function(){return{}}},spaceInfo:{type:Object,default:function(){return{}}},collection:{type:[String,Array],default:""},action:{type:String,default:""},field:{type:String,default:""},orderby:{type:String,default:""},where:{type:[String,Object],default:""},pageData:{type:String,default:"add"},pageCurrent:{type:Number,default:1},pageSize:{type:Number,default:20},getcount:{type:[Boolean,String],default:!1},gettree:{type:[Boolean,String],default:!1},gettreepath:{type:[Boolean,String],default:!1},startwith:{type:String,default:""},limitlevel:{type:Number,default:10},groupby:{type:String,default:""},groupField:{type:String,default:""},distinct:{type:[Boolean,String],default:!1},foreignKey:{type:String,default:""},loadtime:{type:String,default:"auto"},manual:{type:Boolean,default:!1}},data:function(){return{mixinDatacomLoading:!1,mixinDatacomHasMore:!1,mixinDatacomResData:[],mixinDatacomErrorMessage:"",mixinDatacomPage:{},mixinDatacomError:null}},created:function(){var e=this;this.mixinDatacomPage={current:this.pageCurrent,size:this.pageSize,count:0},this.$watch((function(){var t=[];return["pageCurrent","pageSize","localdata","collection","action","field","orderby","where","getont","getcount","gettree","groupby","groupField","distinct"].forEach((function(n){t.push(e[n])})),t}),(function(t,n){if(e.loadtime!==hr.manual){for(var r=!1,i=[],o=2;o<t.length;o++)t[o]!==n[o]&&(i.push(t[o]),r=!0);t[0]!==n[0]&&(e.mixinDatacomPage.current=e.pageCurrent),e.mixinDatacomPage.size=e.pageSize,e.onMixinDatacomPropsChange(r,i)}}))},methods:{onMixinDatacomPropsChange:function(e,t){},mixinDatacomEasyGet:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.getone,r=void 0!==n&&n,i=t.success,o=t.fail;this.mixinDatacomLoading||(this.mixinDatacomLoading=!0,this.mixinDatacomErrorMessage="",this.mixinDatacomError=null,this.mixinDatacomGet().then((function(t){e.mixinDatacomLoading=!1;var n=t.result,o=n.data,a=n.count;e.getcount&&(e.mixinDatacomPage.count=a),e.mixinDatacomHasMore=o.length<e.pageSize;var s=r?o.length?o[0]:void 0:o;e.mixinDatacomResData=s,i&&i(s)})).catch((function(t){e.mixinDatacomLoading=!1,e.mixinDatacomErrorMessage=t,e.mixinDatacomError=t,o&&o(t)})))},mixinDatacomGet:function(){var t,n,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};r=r||{},n="undefined"!=typeof __uniX&&__uniX?e.databaseForJQL(this.spaceInfo):e.database(this.spaceInfo);var i=r.action||this.action;i&&(n=n.action(i));var o=r.collection||this.collection;n=Array.isArray(o)?(t=n).collection.apply(t,(0,l.default)(o)):n.collection(o);var a=r.where||this.where;a&&Object.keys(a).length&&(n=n.where(a));var s=r.field||this.field;s&&(n=n.field(s));var c=r.foreignKey||this.foreignKey;c&&(n=n.foreignKey(c));var u=r.groupby||this.groupby;u&&(n=n.groupBy(u));var f=r.groupField||this.groupField;f&&(n=n.groupField(f)),!0===(void 0!==r.distinct?r.distinct:this.distinct)&&(n=n.distinct());var d=r.orderby||this.orderby;d&&(n=n.orderBy(d));var p=void 0!==r.pageCurrent?r.pageCurrent:this.mixinDatacomPage.current,h=void 0!==r.pageSize?r.pageSize:this.mixinDatacomPage.size,v=void 0!==r.getcount?r.getcount:this.getcount,m=void 0!==r.gettree?r.gettree:this.gettree,g=void 0!==r.gettreepath?r.gettreepath:this.gettreepath,y={getCount:v},_={limitLevel:void 0!==r.limitlevel?r.limitlevel:this.limitlevel,startWith:void 0!==r.startwith?r.startwith:this.startwith};return m&&(y.getTree=_),g&&(y.getTreePath=_),n=n.skip(h*(p-1)).limit(h).get(y),n}}}}function mr(e){return X(dn.replace("{spaceId}",e.config.spaceId))}function gr(){return yr.apply(this,arguments)}function yr(){return yr=(0,f.default)(a.default.mark((function e(){var t,n,i,o,s,c,u,l=arguments;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=l.length>0&&void 0!==l[0]?l[0]:{},n=t.openid,i=t.callLoginByWeixin,o=void 0!==i&&i,s=mr(this),"mp-weixin"===W){e.next=4;break}throw new Error("[SecureNetwork] API `initSecureNetworkByWeixin` is not supported on platform `".concat(W,"`"));case 4:if(!n||!o){e.next=6;break}throw new Error("[SecureNetwork] openid and callLoginByWeixin cannot be passed at the same time");case 6:if(!n){e.next=8;break}return e.abrupt("return",(s.mpWeixinOpenid=n,{}));case 8:return e.next=10,new Promise((function(e,t){r.login({success:function(t){e(t.code)},fail:function(e){t(new Error(e.errMsg))}})}));case 10:return c=e.sent,u=this.importObject("uni-id-co",{customUI:!0}),e.next=14,u.secureNetworkHandshakeByWeixin({code:c,callLoginByWeixin:o});case 14:return s.mpWeixinCode=c,e.abrupt("return",{code:c});case 16:case"end":return e.stop()}}),e,this)}))),yr.apply(this,arguments)}function _r(e){return br.apply(this,arguments)}function br(){return br=(0,f.default)(a.default.mark((function e(t){var n;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=mr(this),e.abrupt("return",(n.initPromise||(n.initPromise=gr.call(this,t).then((function(e){return e})).catch((function(e){throw delete n.initPromise,e}))),n.initPromise));case 2:case"end":return e.stop()}}),e,this)}))),br.apply(this,arguments)}function wr(e){!function(e){Oe=e}(e)}function kr(e){var t="mp-weixin"===W&&i.canIUse("getAppBaseInfo"),n={getAppBaseInfo:t?r.getAppBaseInfo:r.getSystemInfo,getPushClientId:r.getPushClientId};return function(r){return new Promise((function(i,o){t&&"getAppBaseInfo"===e?i(n[e]()):n[e](x(x({},r),{},{success:function(e){i(e)},fail:function(e){o(e)}}))}))}}var xr=function(e){(0,p.default)(n,e);var t=O(n);function n(){var e;return(0,g.default)(this,n),e=t.call(this),e._uniPushMessageCallback=e._receivePushMessage.bind((0,s.default)(e)),e._currentMessageId=-1,e._payloadQueue=[],e}return(0,y.default)(n,[{key:"init",value:function(){var e=this;return Promise.all([kr("getAppBaseInfo")(),kr("getPushClientId")()]).then((function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=(0,c.default)(t,2),r=n[0];r=void 0===r?{}:r;var i=r.appId,o=n[1];o=void 0===o?{}:o;var a=o.cid;if(!i)throw new Error("Invalid appId, please check the manifest.json file");if(!a)throw new Error("Invalid push client id");e._appId=i,e._pushClientId=a,e._seqId=Date.now()+"-"+Math.floor(9e5*Math.random()+1e5),e.emit("open"),e._initMessageListener()}),(function(t){throw e.emit("error",t),e.close(),t}))}},{key:"open",value:function(){var e=(0,f.default)(a.default.mark((function e(){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this.init());case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"_isUniCloudSSE",value:function(e){if("receive"!==e.type)return!1;var t=e&&e.data&&e.data.payload;return!(!t||"UNI_CLOUD_SSE"!==t.channel||t.seqId!==this._seqId)}},{key:"_receivePushMessage",value:function(e){if(this._isUniCloudSSE(e)){var t=e&&e.data&&e.data.payload,n=t.action,r=t.messageId,i=t.message;this._payloadQueue.push({action:n,messageId:r,message:i}),this._consumMessage()}}},{key:"_consumMessage",value:function(){for(var e=this;;){var t=this._payloadQueue.find((function(t){return t.messageId===e._currentMessageId+1}));if(!t)break;this._currentMessageId++,this._parseMessagePayload(t)}}},{key:"_parseMessagePayload",value:function(e){var t=e.action,n=e.messageId,r=e.message;"end"===t?this._end({messageId:n,message:r}):"message"===t&&this._appendMessage({messageId:n,message:r})}},{key:"_appendMessage",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(e.messageId,e.message);this.emit("message",t)}},{key:"_end",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(e.messageId,e.message);this.emit("end",t),this.close()}},{key:"_initMessageListener",value:function(){r.onPushMessage(this._uniPushMessageCallback)}},{key:"_destroy",value:function(){r.offPushMessage(this._uniPushMessageCallback)}},{key:"toJSON",value:function(){return{appId:this._appId,pushClientId:this._pushClientId,seqId:this._seqId}}},{key:"close",value:function(){this._destroy(),this.emit("close")}}]),n}(H);var Or={tcb:Ft,tencent:Ft,aliyun:Ee,private:Jt,dcloud:Jt,alipay:rn},Ar=new(function(){function e(){(0,g.default)(this,e)}return(0,y.default)(e,[{key:"init",value:function(e){var t={},n=Or[e.provider];if(!n)throw new Error("未提供正确的provider参数");return t=n.init(e),function(e){e._initPromiseHub||(e._initPromiseHub=new V({createPromise:function(){var t=Promise.resolve();t=new Promise((function(e){setTimeout((function(){e()}),1)}));var n=e.auth();return t.then((function(){return n.getLoginState()})).then((function(e){return e?Promise.resolve():n.signInAnonymously()}))}}))}(t),wn(t),function(e){var t=e.uploadFile;e.uploadFile=function(e){return t.call(this,e)}}(t),function(e){e.database=function(t){if(t&&Object.keys(t).length>0)return e.init(t).database();if(this._database)return this._database;var n=Ln($n,{uniClient:e});return this._database=n,n},e.databaseForJQL=function(t){if(t&&Object.keys(t).length>0)return e.init(t).databaseForJQL();if(this._databaseForJQL)return this._databaseForJQL;var n=Ln($n,{uniClient:e,isJQL:!0});return this._databaseForJQL=n,n}}(t),function(e){e.getCurrentUserInfo=fr,e.chooseAndUploadFile=pr.initChooseAndUploadFile(e),Object.assign(e,{get mixinDatacom(){return vr(e)}}),e.SSEChannel=xr,e.initSecureNetworkByWeixin=function(e){return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.openid,r=t.callLoginByWeixin,i=void 0!==r&&r;return _r.call(e,{openid:n,callLoginByWeixin:i})}}(e),e.setCustomClientInfo=wr,e.importObject=function(e){return function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};n=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e.customUI=t.customUI||e.customUI,e.parseSystemError=t.parseSystemError||e.parseSystemError,Object.assign(e.loadingOptions,t.loadingOptions),Object.assign(e.errorOptions,t.errorOptions),"object"==(0,u.default)(t.secretMethods)&&(e.secretMethods=t.secretMethods),e}({customUI:!1,loadingOptions:{title:"加载中...",mask:!0},errorOptions:{type:"modal",retry:!1}},n);var i=n,o=i.customUI,s=i.loadingOptions,c=i.errorOptions,l=i.parseSystemError,d=!o;return new Proxy({},{get:function(i,o){switch(o){case"toString":return"[object UniCloudObject]";case"toJSON":return{}}return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.fn,n=e.interceptorName,r=e.getCallbackArgs;return(0,f.default)(a.default.mark((function e(){var i,o,s,c,u,l,f=arguments;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:for(i=f.length,o=new Array(i),s=0;s<i;s++)o[s]=f[s];return c=r?r({params:o}):{},e.prev=2,e.next=5,re(ie(n,"invoke"),x({},c));case 5:return e.next=7,t.apply(void 0,o);case 7:return u=e.sent,e.next=10,re(ie(n,"success"),x(x({},c),{},{result:u}));case 10:return e.abrupt("return",u);case 13:return e.prev=13,e.t0=e["catch"](2),l=e.t0,e.next=18,re(ie(n,"fail"),x(x({},c),{},{error:l}));case 18:throw l;case 19:return e.prev=19,e.next=22,re(ie(n,"complete"),x(x({},c),{},l?{error:l}:{result:u}));case 22:return e.finish(19);case 23:case"end":return e.stop()}}),e,null,[[2,13,19,23]])})))}({fn:function(){var i=(0,f.default)(a.default.mark((function i(){var h,v,m,g,y,_,b,w,k,O,A,S,P,I,E,T=arguments;return a.default.wrap((function(i){while(1)switch(i.prev=i.next){case 0:for(d&&r.showLoading({title:s.title,mask:s.mask}),v=T.length,m=new Array(v),g=0;g<v;g++)m[g]=T[g];return y={name:t,type:L.OBJECT,data:{method:o,params:m}},"object"==(0,u.default)(n.secretMethods)&&function(e,t){var n=t.data.method,r=e.secretMethods||{},i=r[n]||r["*"];i&&(t.secretType=i)}(n,y),_=!1,i.prev=5,i.next=8,e.callFunction(y);case 8:h=i.sent,i.next=14;break;case 11:i.prev=11,i.t0=i["catch"](5),_=!0,h={result:new ge(i.t0)};case 14:if(b=h.result||{},w=b.errSubject,k=b.errCode,O=b.errMsg,A=b.newToken,d&&r.hideLoading(),A&&A.token&&A.tokenExpired&&(ke(A),de(se.REFRESH_TOKEN,x({},A))),!k){i.next=39;break}if(S=O,!_||!l){i.next=24;break}return i.next=20,l({objectName:t,methodName:o,params:m,errSubject:w,errCode:k,errMsg:O});case 20:if(i.t1=i.sent.errMsg,i.t1){i.next=23;break}i.t1=O;case 23:S=i.t1;case 24:if(!d){i.next=37;break}if("toast"!==c.type){i.next=29;break}r.showToast({title:S,icon:"none"}),i.next=37;break;case 29:if("modal"===c.type){i.next=31;break}throw new Error("Invalid errorOptions.type: ".concat(c.type));case 31:return i.next=33,(0,f.default)(a.default.mark((function e(){var t,n,i,o,s,c,u=arguments;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=u.length>0&&void 0!==u[0]?u[0]:{},n=t.title,i=t.content,o=t.showCancel,s=t.cancelText,c=t.confirmText,e.abrupt("return",new Promise((function(e,t){r.showModal({title:n,content:i,showCancel:o,cancelText:s,confirmText:c,success:function(t){e(t)},fail:function(){e({confirm:!1,cancel:!0})}})})));case 2:case"end":return e.stop()}}),e)})))({title:"提示",content:S,showCancel:c.retry,cancelText:"取消",confirmText:c.retry?"重试":"确定"});case 33:if(P=i.sent,I=P.confirm,!c.retry||!I){i.next=37;break}return i.abrupt("return",p.apply(void 0,m));case 37:throw E=new ge({subject:w,code:k,message:O,requestId:h.requestId}),E.detail=h.result,de(se.RESPONSE,{type:ce.CLOUD_OBJECT,content:E}),E;case 39:return i.abrupt("return",(de(se.RESPONSE,{type:ce.CLOUD_OBJECT,content:h.result}),h.result));case 40:case"end":return i.stop()}}),i,null,[[5,11]])})));function p(){return i.apply(this,arguments)}return p}(),interceptorName:"callObject",getCallbackArgs:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=e.params;return{objectName:t,methodName:o,params:n}}})}})}}(e)}(t),["callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile","chooseAndUploadFile"].forEach((function(e){if(t[e]){var n=t[e];t[e]=function(){return n.apply(t,Array.from(arguments))},t[e]=function(e,t){return function(n){var r=this,i=!1;if("callFunction"===t){var o=n&&n.type||L.DEFAULT;i=o!==L.DEFAULT}var a="callFunction"===t&&!i,s=this._initPromiseHub.exec();n=n||{};var c=me(n),u=c.success,l=c.fail,f=c.complete,d=s.then((function(){return i?Promise.resolve():re(ie(t,"invoke"),n)})).then((function(){return e.call(r,n)})).then((function(e){return i?Promise.resolve(e):re(ie(t,"success"),e).then((function(){return re(ie(t,"complete"),e)})).then((function(){return a&&de(se.RESPONSE,{type:ce.CLOUD_FUNCTION,content:e}),Promise.resolve(e)}))}),(function(e){return i?Promise.reject(e):re(ie(t,"fail"),e).then((function(){return re(ie(t,"complete"),e)})).then((function(){return de(se.RESPONSE,{type:ce.CLOUD_FUNCTION,content:e}),Promise.reject(e)}))}));if(!(u||l||f))return d;d.then((function(e){u&&u(e),f&&f(e),a&&de(se.RESPONSE,{type:ce.CLOUD_FUNCTION,content:e})}),(function(e){l&&l(e),f&&f(e),a&&de(se.RESPONSE,{type:ce.CLOUD_FUNCTION,content:e})}))}}(t[e],e).bind(t)}})),t.init=this.init,t}}]),e}());t.uniCloud=Ar,function(){var e=G,n={};if(e&&1===e.length)n=e[0],t.uniCloud=Ar=Ar.init(n),Ar._isDefault=!0;else{var i,o=["database","getCurrentUserInfo","importObject"];i=e&&e.length>0?"应用有多个服务空间，请通过uniCloud.init方法指定要使用的服务空间":"应用未关联服务空间，请在uniCloud目录右键关联服务空间",[].concat(["auth","callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile"],o).forEach((function(e){Ar[e]=function(){if(console.error(i),-1===o.indexOf(e))return Promise.reject(new ge({code:"SYS_ERR",message:i}));console.error(i)}}))}if(Object.assign(Ar,{get mixinDatacom(){return vr(Ar)}}),cr(Ar),Ar.addInterceptor=te,Ar.removeInterceptor=ne,Ar.interceptObject=oe,"app"===W&&(r.__uniCloud=Ar),"app"===W||"web"===W){var a=function(){return Y||(Y=function(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;function e(){return this}return void 0!==e()?e():new Function("return this")()}(),Y)}();a.uniCloud=Ar,a.UniCloudError=ge}}();var Sr=Ar;t.default=Sr}).call(this,n("0ee4"),n("df3c")["default"],n("3223")["default"])},8708:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={debug:!1,isAdmin:!1,loginTypes:["weixin","username","smsCode"],agreements:{serviceUrl:"https://xxx",privacyUrl:"https://xxx",scope:["register","login","realNameVerify"]},appid:{weixin:{h5:"wx0xxxxxxxxxxx10",web:"wx6xxxxxxxxxxxc8"}},passwordStrength:"medium",setPasswordAfterLogin:!1,webWXAuthBackDomain:"m.xxxxxx.love"}},"8a35":function(e,t,n){"use strict";var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=r(n("20c2")),o=r(n("a671")),a=r(n("e9da")),s={en:i.default,"zh-Hans":o.default,"zh-Hant":a.default};t.default=s},"8ffa":function(e,t,n){var r=n("7647");e.exports=function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&r(e,t)},e.exports.__esModule=!0,e.exports["default"]=e.exports},9008:function(e,t){e.exports=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports["default"]=e.exports},"931d":function(e,t,n){var r=n("7647"),i=n("011a");e.exports=function(e,t,n){if(i())return Reflect.construct.apply(null,arguments);var o=[null];o.push.apply(o,t);var a=new(e.bind.apply(e,o));return n&&r(a,n.prototype),a},e.exports.__esModule=!0,e.exports["default"]=e.exports},9789:function(e,t,n){"use strict";(function(e){var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.createAnimation=function(e,t){if(!t)return;return clearTimeout(t.timer),new u(e,t)};var i=r(n("7ca3")),o=r(n("67ad")),a=r(n("0bdb"));function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){(0,i.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var u=function(){function t(n,r){(0,o.default)(this,t),this.options=n,this.animation=e.createAnimation(n),this.currentStepAnimates={},this.next=0,this.$=r}return(0,a.default)(t,[{key:"_nvuePushAnimates",value:function(e,t){var n=this.currentStepAnimates[this.next],r={};if(r=n||{styles:{},config:{}},l.includes(e)){r.styles.transform||(r.styles.transform="");var i="";"rotate"===e&&(i="deg"),r.styles.transform+="".concat(e,"(").concat(t+i,") ")}else r.styles[e]="".concat(t);this.currentStepAnimates[this.next]=r}},{key:"_animateRun",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=this.$.$refs["ani"].ref;if(n)return new Promise((function(r,i){nvueAnimation.transition(n,c({styles:e},t),(function(e){r()}))}))}},{key:"_nvueNextAnimate",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=arguments.length>2?arguments[2]:void 0,i=e[n];if(i){var o=i.styles,a=i.config;this._animateRun(o,a).then((function(){n+=1,t._nvueNextAnimate(e,n,r)}))}else this.currentStepAnimates={},"function"===typeof r&&r(),this.isEnd=!0}},{key:"step",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.animation.step(e),this}},{key:"run",value:function(e){this.$.animationData=this.animation.export(),this.$.timer=setTimeout((function(){"function"===typeof e&&e()}),this.$.durationTime)}}]),t}(),l=["matrix","matrix3d","rotate","rotate3d","rotateX","rotateY","rotateZ","scale","scale3d","scaleX","scaleY","scaleZ","skew","skewX","skewY","translate","translate3d","translateX","translateY","translateZ"];l.concat(["opacity","backgroundColor"],["width","height","left","right","top","bottom"]).forEach((function(e){u.prototype[e]=function(){var t;return(t=this.animation)[e].apply(t,arguments),this}}))}).call(this,n("df3c")["default"])},"9fc1":function(e,t,n){var r=n("3b2d")["default"];function i(){"use strict";
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */e.exports=i=function(){return n},e.exports.__esModule=!0,e.exports["default"]=e.exports;var t,n={},o=Object.prototype,a=o.hasOwnProperty,s=Object.defineProperty||function(e,t,n){e[t]=n.value},c="function"==typeof Symbol?Symbol:{},u=c.iterator||"@@iterator",l=c.asyncIterator||"@@asyncIterator",f=c.toStringTag||"@@toStringTag";function d(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(t){d=function(e,t,n){return e[t]=n}}function p(e,t,n,r){var i=t&&t.prototype instanceof _?t:_,o=Object.create(i.prototype),a=new C(r||[]);return s(o,"_invoke",{value:I(e,n,a)}),o}function h(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}n.wrap=p;var v="suspendedStart",m="executing",g="completed",y={};function _(){}function b(){}function w(){}var k={};d(k,u,(function(){return this}));var x=Object.getPrototypeOf,O=x&&x(x(L([])));O&&O!==o&&a.call(O,u)&&(k=O);var A=w.prototype=_.prototype=Object.create(k);function S(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function P(e,t){function n(i,o,s,c){var u=h(e[i],e,o);if("throw"!==u.type){var l=u.arg,f=l.value;return f&&"object"==r(f)&&a.call(f,"__await")?t.resolve(f.__await).then((function(e){n("next",e,s,c)}),(function(e){n("throw",e,s,c)})):t.resolve(f).then((function(e){l.value=e,s(l)}),(function(e){return n("throw",e,s,c)}))}c(u.arg)}var i;s(this,"_invoke",{value:function(e,r){function o(){return new t((function(t,i){n(e,r,t,i)}))}return i=i?i.then(o,o):o()}})}function I(e,n,r){var i=v;return function(o,a){if(i===m)throw Error("Generator is already running");if(i===g){if("throw"===o)throw a;return{value:t,done:!0}}for(r.method=o,r.arg=a;;){var s=r.delegate;if(s){var c=E(s,r);if(c){if(c===y)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===v)throw i=g,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=m;var u=h(e,n,r);if("normal"===u.type){if(i=r.done?g:"suspendedYield",u.arg===y)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(i=g,r.method="throw",r.arg=u.arg)}}}function E(e,n){var r=n.method,i=e.iterator[r];if(i===t)return n.delegate=null,"throw"===r&&e.iterator["return"]&&(n.method="return",n.arg=t,E(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),y;var o=h(i,e.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,y;var a=o.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,y):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,y)}function T(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function j(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function C(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(T,this),this.reset(!0)}function L(e){if(e||""===e){var n=e[u];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,o=function n(){for(;++i<e.length;)if(a.call(e,i))return n.value=e[i],n.done=!1,n;return n.value=t,n.done=!0,n};return o.next=o}}throw new TypeError(r(e)+" is not iterable")}return b.prototype=w,s(A,"constructor",{value:w,configurable:!0}),s(w,"constructor",{value:b,configurable:!0}),b.displayName=d(w,f,"GeneratorFunction"),n.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===b||"GeneratorFunction"===(t.displayName||t.name))},n.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,d(e,f,"GeneratorFunction")),e.prototype=Object.create(A),e},n.awrap=function(e){return{__await:e}},S(P.prototype),d(P.prototype,l,(function(){return this})),n.AsyncIterator=P,n.async=function(e,t,r,i,o){void 0===o&&(o=Promise);var a=new P(p(e,t,r,i),o);return n.isGeneratorFunction(t)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},S(A),d(A,f,"Generator"),d(A,u,(function(){return this})),d(A,"toString",(function(){return"[object Generator]"})),n.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},n.values=L,C.prototype={constructor:C,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(j),!e)for(var n in this)"t"===n.charAt(0)&&a.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function r(r,i){return s.type="throw",s.arg=e,n.next=r,i&&(n.method="next",n.arg=t),!!i}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var c=a.call(o,"catchLoc"),u=a.call(o,"finallyLoc");if(c&&u){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&a.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),j(n),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var i=r.arg;j(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:L(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),y}},n}e.exports=i,e.exports.__esModule=!0,e.exports["default"]=e.exports},a019:function(e,t){},a671:function(e){e.exports=JSON.parse('{"uni-load-more.contentdown":"上拉显示更多","uni-load-more.contentrefresh":"正在加载...","uni-load-more.contentnomore":"没有更多数据了"}')},a708:function(e,t,n){var r=n("6454");e.exports=function(e){if(Array.isArray(e))return r(e)},e.exports.__esModule=!0,e.exports["default"]=e.exports},af34:function(e,t,n){var r=n("a708"),i=n("b893"),o=n("6382"),a=n("9008");e.exports=function(e){return r(e)||i(e)||o(e)||a()},e.exports.__esModule=!0,e.exports["default"]=e.exports},b282:function(e){e.exports=JSON.parse('{"uni-popup.cancel":"cancel","uni-popup.ok":"ok","uni-popup.placeholder":"pleace enter","uni-popup.title":"Hint","uni-popup.shareTitle":"Share to"}')},b4d2:function(e,t){function n(t){return e.exports=n=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},e.exports.__esModule=!0,e.exports["default"]=e.exports,n(t)}e.exports=n,e.exports.__esModule=!0,e.exports["default"]=e.exports},b7d4:function(e,t,n){var r=n("67cf");e.exports=function(e,t){if(null==e)return{};var n,i,o=r(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o},e.exports.__esModule=!0,e.exports["default"]=e.exports},b893:function(e,t){e.exports=function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)},e.exports.__esModule=!0,e.exports["default"]=e.exports},cb85:function(e,t,n){"use strict";var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=r(n("7eb4")),o=r(n("8ffa")),a=r(n("4ffb")),s=r(n("b4d2")),c=r(n("ee10")),u=r(n("67ad")),l=r(n("0bdb")),f=r(n("3b2d"));function d(e){var t=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=(0,s.default)(e);if(t){var i=(0,s.default)(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return(0,a.default)(this,n)}}var p={email:/^\S+?@\S+?\.\S+?$/,idcard:/^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,url:new RegExp("^(?!mailto:)(?:(?:http|https|ftp)://|//)(?:\\S+(?::\\S*)?@)?(?:(?:(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[0-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u00a1-\\uffff0-9]+-*)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]+-*)*[a-z\\u00a1-\\uffff0-9]+)*(?:\\.(?:[a-z\\u00a1-\\uffff]{2,})))|localhost)(?::\\d{2,5})?(?:(/|\\?|#)[^\\s]*)?$","i")},h={int:"integer",bool:"boolean",double:"number",long:"number",password:"string"};function v(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=["label"];n.forEach((function(t){void 0===e[t]&&(e[t]="")}));var r=t;for(var i in e){var o=new RegExp("{"+i+"}");r=r.replace(o,e[i])}return r}var m={integer:function(e){return m.number(e)&&parseInt(e,10)===e},string:function(e){return"string"===typeof e},number:function(e){return!isNaN(e)&&"number"===typeof e},boolean:function(e){return"boolean"===typeof e},float:function(e){return m.number(e)&&!m.integer(e)},array:function(e){return Array.isArray(e)},object:function(e){return"object"===(0,f.default)(e)&&!m.array(e)},date:function(e){return e instanceof Date},timestamp:function(e){return!(!this.integer(e)||Math.abs(e).toString().length>16)},file:function(e){return"string"===typeof e.url},email:function(e){return"string"===typeof e&&!!e.match(p.email)&&e.length<255},url:function(e){return"string"===typeof e&&!!e.match(p.url)},pattern:function(e,t){try{return new RegExp(e).test(t)}catch(n){return!1}},method:function(e){return"function"===typeof e},idcard:function(e){return"string"===typeof e&&!!e.match(p.idcard)},"url-https":function(e){return this.url(e)&&e.startsWith("https://")},"url-scheme":function(e){return e.startsWith("://")},"url-web":function(e){return!1}},g=function(){function e(t){(0,u.default)(this,e),this._message=t}return(0,l.default)(e,[{key:"validateRule",value:function(){var e=(0,c.default)(i.default.mark((function e(t,n,r,o,a){var s,c,u,l,f,d,p,h,v;return i.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(s=null,c=n.rules,u=c.findIndex((function(e){return e.required})),!(u<0)){e.next=8;break}if(null!==r&&void 0!==r){e.next=6;break}return e.abrupt("return",s);case 6:if("string"!==typeof r||r.length){e.next=8;break}return e.abrupt("return",s);case 8:if(l=this._message,void 0!==c){e.next=11;break}return e.abrupt("return",l["default"]);case 11:f=0;case 12:if(!(f<c.length)){e.next=35;break}if(d=c[f],p=this._getValidateType(d),Object.assign(d,{label:n.label||'["'.concat(t,'"]')}),!y[p]){e.next=20;break}if(s=y[p](d,r,l),null==s){e.next=20;break}return e.abrupt("break",35);case 20:if(!d.validateExpr){e.next=26;break}if(h=Date.now(),v=d.validateExpr(r,a,h),!1!==v){e.next=26;break}return s=this._getMessage(d,d.errorMessage||this._message["default"]),e.abrupt("break",35);case 26:if(!d.validateFunction){e.next=32;break}return e.next=29,this.validateFunction(d,r,o,a,p);case 29:if(s=e.sent,null===s){e.next=32;break}return e.abrupt("break",35);case 32:f++,e.next=12;break;case 35:return null!==s&&(s=l.TAG+s),e.abrupt("return",s);case 37:case"end":return e.stop()}}),e,this)})));return function(t,n,r,i,o){return e.apply(this,arguments)}}()},{key:"validateFunction",value:function(){var e=(0,c.default)(i.default.mark((function e(t,n,r,o,a){var s,c,u;return i.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return s=null,e.prev=1,c=null,e.next=5,t.validateFunction(t,n,o||r,(function(e){c=e}));case 5:u=e.sent,(c||"string"===typeof u&&u||!1===u)&&(s=this._getMessage(t,c||u,a)),e.next=12;break;case 9:e.prev=9,e.t0=e["catch"](1),s=this._getMessage(t,e.t0.message,a);case 12:return e.abrupt("return",s);case 13:case"end":return e.stop()}}),e,this,[[1,9]])})));return function(t,n,r,i,o){return e.apply(this,arguments)}}()},{key:"_getMessage",value:function(e,t,n){return v(e,t||e.errorMessage||this._message[n]||t["default"])}},{key:"_getValidateType",value:function(e){var t="";return e.required?t="required":e.format?t="format":e.arrayType?t="arrayTypeFormat":e.range?t="range":void 0!==e.maximum||void 0!==e.minimum?t="rangeNumber":void 0!==e.maxLength||void 0!==e.minLength?t="rangeLength":e.pattern?t="pattern":e.validateFunction&&(t="validateFunction"),t}}]),e}(),y={required:function(e,t,n){return e.required&&function(e,t){return void 0===e||null===e||("string"===typeof e&&!e||(!(!Array.isArray(e)||e.length)||"object"===t&&!Object.keys(e).length))}(t,e.format||(0,f.default)(t))?v(e,e.errorMessage||n.required):null},range:function(e,t,n){for(var r=e.range,i=e.errorMessage,o=new Array(r.length),a=0;a<r.length;a++){var s=r[a];m.object(s)&&void 0!==s.value?o[a]=s.value:o[a]=s}var c=!1;return Array.isArray(t)?c=new Set(t.concat(o)).size===o.length:o.indexOf(t)>-1&&(c=!0),c?null:v(e,i||n["enum"])},rangeNumber:function(e,t,n){if(!m.number(t))return v(e,e.errorMessage||n.pattern.mismatch);var r=e.minimum,i=e.maximum,o=e.exclusiveMinimum,a=e.exclusiveMaximum,s=o?t<=r:t<r,c=a?t>=i:t>i;return void 0!==r&&s?v(e,e.errorMessage||n["number"][o?"exclusiveMinimum":"minimum"]):void 0!==i&&c?v(e,e.errorMessage||n["number"][a?"exclusiveMaximum":"maximum"]):void 0!==r&&void 0!==i&&(s||c)?v(e,e.errorMessage||n["number"].range):null},rangeLength:function(e,t,n){if(!m.string(t)&&!m.array(t))return v(e,e.errorMessage||n.pattern.mismatch);var r=e.minLength,i=e.maxLength,o=t.length;return void 0!==r&&o<r?v(e,e.errorMessage||n["length"].minLength):void 0!==i&&o>i?v(e,e.errorMessage||n["length"].maxLength):void 0!==r&&void 0!==i&&(o<r||o>i)?v(e,e.errorMessage||n["length"].range):null},pattern:function(e,t,n){return m["pattern"](e.pattern,t)?null:v(e,e.errorMessage||n.pattern.mismatch)},format:function(e,t,n){var r=Object.keys(m),i=h[e.format]?h[e.format]:e.format||e.arrayType;return r.indexOf(i)>-1&&!m[i](t)?v(e,e.errorMessage||n.typeError):null},arrayTypeFormat:function(e,t,n){if(!Array.isArray(t))return v(e,e.errorMessage||n.typeError);for(var r=0;r<t.length;r++){var i=t[r],o=this.format(e,i,n);if(null!==o)return o}return null}},_=function(e){(0,o.default)(n,e);var t=d(n);function n(e,r){var i;return(0,u.default)(this,n),i=t.call(this,n.message),i._schema=e,i._options=r||null,i}return(0,l.default)(n,[{key:"updateSchema",value:function(e){this._schema=e}},{key:"validate",value:function(){var e=(0,c.default)(i.default.mark((function e(t,n){var r;return i.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r=this._checkFieldInSchema(t),r){e.next=5;break}return e.next=4,this.invokeValidate(t,!1,n);case 4:r=e.sent;case 5:return e.abrupt("return",r.length?r[0]:null);case 6:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"validateAll",value:function(){var e=(0,c.default)(i.default.mark((function e(t,n){var r;return i.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r=this._checkFieldInSchema(t),r){e.next=5;break}return e.next=4,this.invokeValidate(t,!0,n);case 4:r=e.sent;case 5:return e.abrupt("return",r);case 6:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"validateUpdate",value:function(){var e=(0,c.default)(i.default.mark((function e(t,n){var r;return i.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r=this._checkFieldInSchema(t),r){e.next=5;break}return e.next=4,this.invokeValidateUpdate(t,!1,n);case 4:r=e.sent;case 5:return e.abrupt("return",r.length?r[0]:null);case 6:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"invokeValidate",value:function(){var e=(0,c.default)(i.default.mark((function e(t,n,r){var o,a,s,c,u;return i.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:o=[],a=this._schema,e.t0=i.default.keys(a);case 3:if((e.t1=e.t0()).done){e.next=15;break}return s=e.t1.value,c=a[s],e.next=8,this.validateRule(s,c,t[s],t,r);case 8:if(u=e.sent,null==u){e.next=13;break}if(o.push({key:s,errorMessage:u}),n){e.next=13;break}return e.abrupt("break",15);case 13:e.next=3;break;case 15:return e.abrupt("return",o);case 16:case"end":return e.stop()}}),e,this)})));return function(t,n,r){return e.apply(this,arguments)}}()},{key:"invokeValidateUpdate",value:function(){var e=(0,c.default)(i.default.mark((function e(t,n,r){var o,a,s;return i.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:o=[],e.t0=i.default.keys(t);case 2:if((e.t1=e.t0()).done){e.next=13;break}return a=e.t1.value,e.next=6,this.validateRule(a,this._schema[a],t[a],t,r);case 6:if(s=e.sent,null==s){e.next=11;break}if(o.push({key:a,errorMessage:s}),n){e.next=11;break}return e.abrupt("break",13);case 11:e.next=2;break;case 13:return e.abrupt("return",o);case 14:case"end":return e.stop()}}),e,this)})));return function(t,n,r){return e.apply(this,arguments)}}()},{key:"_checkFieldInSchema",value:function(e){var t=Object.keys(e),r=Object.keys(this._schema);if(new Set(t.concat(r)).size===r.length)return"";var i=t.filter((function(e){return r.indexOf(e)<0})),o=v({field:JSON.stringify(i)},n.message.TAG+n.message["defaultInvalid"]);return[{key:"invalid",errorMessage:o}]}}]),n}(g);_.message=new function(){return{TAG:"",default:"验证错误",defaultInvalid:"提交的字段{field}在数据库中并不存在",validateFunction:"验证无效",required:"{label}必填",enum:"{label}超出范围",timestamp:"{label}格式无效",whitespace:"{label}不能为空",typeError:"{label}类型无效",date:{format:"{label}日期{value}格式无效",parse:"{label}日期无法解析,{value}无效",invalid:"{label}日期{value}无效"},length:{minLength:"{label}长度不能少于{minLength}",maxLength:"{label}长度不能超过{maxLength}",range:"{label}必须介于{minLength}和{maxLength}之间"},number:{minimum:"{label}不能小于{minimum}",maximum:"{label}不能大于{maximum}",exclusiveMinimum:"{label}不能小于等于{minimum}",exclusiveMaximum:"{label}不能大于等于{maximum}",range:"{label}必须介于{minimum}and{maximum}之间"},pattern:{mismatch:"{label}格式不匹配"}}};var b=_;t.default=b},d080:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={appid:"__UNI__9F50D13"}},d3b4:function(e,t,n){"use strict";(function(e,r){var i=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.LOCALE_ZH_HANT=t.LOCALE_ZH_HANS=t.LOCALE_FR=t.LOCALE_ES=t.LOCALE_EN=t.I18n=t.Formatter=void 0,t.compileI18nJsonStr=function(e,t){var n=t.locale,r=t.locales,i=t.delimiters;if(!A(e,i))return e;x||(x=new f);var o=[];Object.keys(r).forEach((function(e){e!==n&&o.push({locale:e,values:r[e]})})),o.unshift({locale:n,values:r[n]});try{return JSON.stringify(P(JSON.parse(e),o,i),null,2)}catch(a){}return e},t.hasI18nJson=function e(t,n){x||(x=new f);return I(t,(function(t,r){var i=t[r];return O(i)?!!A(i,n)||void 0:e(i,n)}))},t.initVueI18n=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0;if("string"!==typeof e){var i=[t,e];e=i[0],t=i[1]}"string"!==typeof e&&(e=k());"string"!==typeof n&&(n="undefined"!==typeof __uniConfig&&__uniConfig.fallbackLocale||"en");var o=new b({locale:e,fallbackLocale:n,messages:t,watcher:r}),a=function(e,t){if("function"!==typeof getApp)a=function(e,t){return o.t(e,t)};else{var n=!1;a=function(e,t){var r=getApp().$vm;return r&&(r.$locale,n||(n=!0,w(r,o))),o.t(e,t)}}return a(e,t)};return{i18n:o,f:function(e,t,n){return o.f(e,t,n)},t:function(e,t){return a(e,t)},add:function(e,t){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return o.add(e,t,n)},watch:function(e){return o.watchLocale(e)},getLocale:function(){return o.getLocale()},setLocale:function(e){return o.setLocale(e)}}},t.isI18nStr=A,t.isString=void 0,t.normalizeLocale=_,t.parseI18nJson=function e(t,n,r){x||(x=new f);return I(t,(function(t,i){var o=t[i];O(o)?A(o,r)&&(t[i]=S(o,n,r)):e(o,n,r)})),t},t.resolveLocale=function(e){return function(t){return t?(t=_(t)||t,function(e){var t=[],n=e.split("-");while(n.length)t.push(n.join("-")),n.pop();return t}(t).find((function(t){return e.indexOf(t)>-1}))):t}};var o=i(n("34cf")),a=i(n("67ad")),s=i(n("0bdb")),c=i(n("3b2d")),u=function(e){return null!==e&&"object"===(0,c.default)(e)},l=["{","}"],f=function(){function e(){(0,a.default)(this,e),this._caches=Object.create(null)}return(0,s.default)(e,[{key:"interpolate",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:l;if(!t)return[e];var r=this._caches[e];return r||(r=h(e,n),this._caches[e]=r),v(r,t)}}]),e}();t.Formatter=f;var d=/^(?:\d)+/,p=/^(?:\w)+/;function h(e,t){var n=(0,o.default)(t,2),r=n[0],i=n[1],a=[],s=0,c="";while(s<e.length){var u=e[s++];if(u===r){c&&a.push({type:"text",value:c}),c="";var l="";u=e[s++];while(void 0!==u&&u!==i)l+=u,u=e[s++];var f=u===i,h=d.test(l)?"list":f&&p.test(l)?"named":"unknown";a.push({value:l,type:h})}else c+=u}return c&&a.push({type:"text",value:c}),a}function v(e,t){var n=[],r=0,i=Array.isArray(t)?"list":u(t)?"named":"unknown";if("unknown"===i)return n;while(r<e.length){var o=e[r];switch(o.type){case"text":n.push(o.value);break;case"list":n.push(t[parseInt(o.value,10)]);break;case"named":"named"===i&&n.push(t[o.value]);break;case"unknown":0;break}r++}return n}t.LOCALE_ZH_HANS="zh-Hans";t.LOCALE_ZH_HANT="zh-Hant";t.LOCALE_EN="en";t.LOCALE_FR="fr";t.LOCALE_ES="es";var m=Object.prototype.hasOwnProperty,g=function(e,t){return m.call(e,t)},y=new f;function _(e,t){if(e){if(e=e.trim().replace(/_/g,"-"),t&&t[e])return e;if(e=e.toLowerCase(),"chinese"===e)return"zh-Hans";if(0===e.indexOf("zh"))return e.indexOf("-hans")>-1?"zh-Hans":e.indexOf("-hant")>-1||function(e,t){return!!t.find((function(t){return-1!==e.indexOf(t)}))}(e,["-tw","-hk","-mo","-cht"])?"zh-Hant":"zh-Hans";var n=["en","fr","es"];t&&Object.keys(t).length>0&&(n=Object.keys(t));var r=function(e,t){return t.find((function(t){return 0===e.indexOf(t)}))}(e,n);return r||void 0}}var b=function(){function e(t){var n=t.locale,r=t.fallbackLocale,i=t.messages,o=t.watcher,s=t.formater;(0,a.default)(this,e),this.locale="en",this.fallbackLocale="en",this.message={},this.messages={},this.watchers=[],r&&(this.fallbackLocale=r),this.formater=s||y,this.messages=i||{},this.setLocale(n||"en"),o&&this.watchLocale(o)}return(0,s.default)(e,[{key:"setLocale",value:function(e){var t=this,n=this.locale;this.locale=_(e,this.messages)||this.fallbackLocale,this.messages[this.locale]||(this.messages[this.locale]={}),this.message=this.messages[this.locale],n!==this.locale&&this.watchers.forEach((function(e){e(t.locale,n)}))}},{key:"getLocale",value:function(){return this.locale}},{key:"watchLocale",value:function(e){var t=this,n=this.watchers.push(e)-1;return function(){t.watchers.splice(n,1)}}},{key:"add",value:function(e,t){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],r=this.messages[e];r?n?Object.assign(r,t):Object.keys(t).forEach((function(e){g(r,e)||(r[e]=t[e])})):this.messages[e]=t}},{key:"f",value:function(e,t,n){return this.formater.interpolate(e,t,n).join("")}},{key:"t",value:function(e,t,n){var r=this.message;return"string"===typeof t?(t=_(t,this.messages),t&&(r=this.messages[t])):n=t,g(r,e)?this.formater.interpolate(r[e],n).join(""):(console.warn("Cannot translate the value of keypath ".concat(e,". Use the value of keypath as default.")),e)}}]),e}();function w(e,t){e.$watchLocale?e.$watchLocale((function(e){t.setLocale(e)})):e.$watch((function(){return e.$locale}),(function(e){t.setLocale(e)}))}function k(){return"undefined"!==typeof e&&e.getLocale?e.getLocale():"undefined"!==typeof r&&r.getLocale?r.getLocale():"en"}t.I18n=b;var x,O=function(e){return"string"===typeof e};function A(e,t){return e.indexOf(t[0])>-1}function S(e,t,n){return x.interpolate(e,t,n).join("")}function P(e,t,n){return I(e,(function(e,r){(function(e,t,n,r){var i=e[t];if(O(i)){if(A(i,r)&&(e[t]=S(i,n[0].values,r),n.length>1)){var o=e[t+"Locales"]={};n.forEach((function(e){o[e.locale]=S(i,e.values,r)}))}}else P(i,n,r)})(e,r,t,n)})),e}function I(e,t){if(Array.isArray(e)){for(var n=0;n<e.length;n++)if(t(e,n))return!0}else if(u(e))for(var r in e)if(t(e,r))return!0;return!1}t.isString=O}).call(this,n("df3c")["default"],n("0ee4"))},d551:function(e,t,n){var r=n("3b2d")["default"],i=n("e6db");e.exports=function(e){var t=i(e,"string");return"symbol"==r(t)?t:t+""},e.exports.__esModule=!0,e.exports["default"]=e.exports},dc82:function(e,t,n){"use strict";var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=r(n("7ca3")),o=r(n("4db8"));function a(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var s=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?a(Object(n),!0).forEach((function(t){(0,i.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):a(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({username:{rules:[{required:!0,errorMessage:"请输入用户名"},{minLength:3,maxLength:32,errorMessage:"用户名长度在 {minLength} 到 {maxLength} 个字符"},{validateFunction:function(e,t,n,r){return(/^1\d{10}$/.test(t)||/^(\w-*\.*)+@(\w-?)+(\.\w{2,})+$/.test(t))&&r("用户名不能是：手机号或邮箱"),/^\d+$/.test(t)&&r("用户名不能为纯数字"),/[\u4E00-\u9FA5\uF900-\uFA2D]{1,}/.test(t)&&r("用户名不能包含中文"),!0}}],label:"用户名"},nickname:{rules:[{minLength:3,maxLength:32,errorMessage:"昵称长度在 {minLength} 到 {maxLength} 个字符"},{validateFunction:function(e,t,n,r){return(/^1\d{10}$/.test(t)||/^(\w-*\.*)+@(\w-?)+(\.\w{2,})+$/.test(t))&&r("昵称不能是：手机号或邮箱"),/^\d+$/.test(t)&&r("昵称不能为纯数字"),/[\u4E00-\u9FA5\uF900-\uFA2D]{1,}/.test(t)&&r("昵称不能包含中文"),!0}}],label:"昵称"}},o.default.getPwdRules());t.default=s},dd3e:function(e,t){e.exports=function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports["default"]=e.exports},df3c:function(e,t,n){"use strict";(function(e,r){var i=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.createApp=$t,t.createComponent=Ht,t.createPage=Vt,t.createPlugin=zt,t.createSubpackageApp=Kt,t.default=void 0;var o,a=i(n("34cf")),s=i(n("7ca3")),c=i(n("931d")),u=i(n("af34")),l=i(n("3b2d")),f=n("d3b4"),d=i(n("3240"));function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach((function(t){(0,s.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var v="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",m=/^(?:[A-Za-z\d+/]{4})*?(?:[A-Za-z\d+/]{2}(?:==)?|[A-Za-z\d+/]{3}=?)?$/;function g(){var t,n=e.getStorageSync("uni_id_token")||"",r=n.split(".");if(!n||3!==r.length)return{uid:null,role:[],permission:[],tokenExpired:0};try{t=JSON.parse(function(e){return decodeURIComponent(o(e).split("").map((function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)})).join(""))}(r[1]))}catch(i){throw new Error("获取当前用户信息出错，详细错误信息为："+i.message)}return t.tokenExpired=1e3*t.exp,delete t.exp,delete t.iat,t}o="function"!==typeof atob?function(e){if(e=String(e).replace(/[\t\n\f\r ]+/g,""),!m.test(e))throw new Error("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.");var t;e+="==".slice(2-(3&e.length));for(var n,r,i="",o=0;o<e.length;)t=v.indexOf(e.charAt(o++))<<18|v.indexOf(e.charAt(o++))<<12|(n=v.indexOf(e.charAt(o++)))<<6|(r=v.indexOf(e.charAt(o++))),i+=64===n?String.fromCharCode(t>>16&255):64===r?String.fromCharCode(t>>16&255,t>>8&255):String.fromCharCode(t>>16&255,t>>8&255,255&t);return i}:atob;var y=Object.prototype.toString,_=Object.prototype.hasOwnProperty;function b(e){return"function"===typeof e}function w(e){return"string"===typeof e}function k(e){return"[object Object]"===y.call(e)}function x(e,t){return _.call(e,t)}function O(){}function A(e){var t=Object.create(null);return function(n){var r=t[n];return r||(t[n]=e(n))}}var S=/-(\w)/g,P=A((function(e){return e.replace(S,(function(e,t){return t?t.toUpperCase():""}))}));function I(e){var t={};return k(e)&&Object.keys(e).sort().forEach((function(n){t[n]=e[n]})),Object.keys(t)?t:e}var E=["invoke","success","fail","complete","returnValue"],T={},j={};function C(e,t){Object.keys(t).forEach((function(n){-1!==E.indexOf(n)&&b(t[n])&&(e[n]=function(e,t){var n=t?e?e.concat(t):Array.isArray(t)?t:[t]:e;return n?function(e){for(var t=[],n=0;n<e.length;n++)-1===t.indexOf(e[n])&&t.push(e[n]);return t}(n):n}(e[n],t[n]))}))}function L(e,t){e&&t&&Object.keys(t).forEach((function(n){-1!==E.indexOf(n)&&b(t[n])&&function(e,t){var n=e.indexOf(t);-1!==n&&e.splice(n,1)}(e[n],t[n])}))}function $(e,t){return function(n){return e(n,t)||n}}function D(e){return!!e&&("object"===(0,l.default)(e)||"function"===typeof e)&&"function"===typeof e.then}function N(e,t,n){for(var r=!1,i=0;i<e.length;i++){var o=e[i];if(r)r=Promise.resolve($(o,n));else{var a=o(t,n);if(D(a)&&(r=Promise.resolve(a)),!1===a)return{then:function(){}}}}return r||{then:function(e){return e(t)}}}function R(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return["success","fail","complete"].forEach((function(n){if(Array.isArray(e[n])){var r=t[n];t[n]=function(i){N(e[n],i,t).then((function(e){return b(r)&&r(e)||e}))}}})),t}function M(e,t){var n=[];Array.isArray(T.returnValue)&&n.push.apply(n,(0,u.default)(T.returnValue));var r=j[e];return r&&Array.isArray(r.returnValue)&&n.push.apply(n,(0,u.default)(r.returnValue)),n.forEach((function(e){t=e(t)||t})),t}function U(e){var t=Object.create(null);Object.keys(T).forEach((function(e){"returnValue"!==e&&(t[e]=T[e].slice())}));var n=j[e];return n&&Object.keys(n).forEach((function(e){"returnValue"!==e&&(t[e]=(t[e]||[]).concat(n[e]))})),t}function F(e,t,n){for(var r=arguments.length,i=new Array(r>3?r-3:0),o=3;o<r;o++)i[o-3]=arguments[o];var a=U(e);if(a&&Object.keys(a).length){if(Array.isArray(a.invoke)){var s=N(a.invoke,n);return s.then((function(n){return t.apply(void 0,[R(U(e),n)].concat(i))}))}return t.apply(void 0,[R(a,n)].concat(i))}return t.apply(void 0,[n].concat(i))}var q={returnValue:function(e){return D(e)?new Promise((function(t,n){e.then((function(e){e?e[0]?n(e[0]):t(e[1]):t(e)}))})):e}},B=/^\$|__f__|Window$|WindowStyle$|sendHostEvent|sendNativeEvent|restoreGlobal|requireGlobal|getCurrentSubNVue|getMenuButtonBoundingClientRect|^report|interceptors|Interceptor$|getSubNVueById|requireNativePlugin|rpx2px|upx2px|hideKeyboard|canIUse|^create|Sync$|Manager$|base64ToArrayBuffer|arrayBufferToBase64|getLocale|setLocale|invokePushCallback|getWindowInfo|getDeviceInfo|getAppBaseInfo|getSystemSetting|getAppAuthorizeSetting|initUTS|requireUTS|registerUTS/,V=/^create|Manager$/,H=["createBLEConnection"],K=["createBLEConnection","createPushMessage"],z=/^on|^off/;function J(e){return V.test(e)&&-1===H.indexOf(e)}function W(e){return B.test(e)&&-1===K.indexOf(e)}function G(e){return e.then((function(e){return[null,e]})).catch((function(e){return[e]}))}function Y(e){return!(J(e)||W(e)||function(e){return z.test(e)&&"onPush"!==e}(e))}function Q(e,t){return Y(e)&&b(t)?function(){for(var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length,i=new Array(r>1?r-1:0),o=1;o<r;o++)i[o-1]=arguments[o];return b(n.success)||b(n.fail)||b(n.complete)?M(e,F.apply(void 0,[e,t,Object.assign({},n)].concat(i))):M(e,G(new Promise((function(r,o){F.apply(void 0,[e,t,Object.assign({},n,{success:r,fail:o})].concat(i))}))))}:t}Promise.prototype.finally||(Promise.prototype.finally=function(e){var t=this.constructor;return this.then((function(n){return t.resolve(e()).then((function(){return n}))}),(function(n){return t.resolve(e()).then((function(){throw n}))}))});var X=!1,Z=0,ee=0;function te(t,n){if(0===Z&&function(){var t,n,r,i="function"===typeof e.getWindowInfo&&e.getWindowInfo()?e.getWindowInfo():e.getSystemInfoSync(),o="function"===typeof e.getDeviceInfo&&e.getDeviceInfo()?e.getDeviceInfo():e.getSystemInfoSync();t=i.windowWidth,n=i.pixelRatio,r=o.platform,Z=t,ee=n,X="ios"===r}(),t=Number(t),0===t)return 0;var r=t/750*(n||Z);return r<0&&(r=-r),r=Math.floor(r+1e-4),0===r&&(r=1!==ee&&X?.5:1),t<0?-r:r}var ne,re={};function ie(){var t,n="function"===typeof e.getAppBaseInfo&&e.getAppBaseInfo()?e.getAppBaseInfo():e.getSystemInfoSync(),r=n&&n.language?n.language:"en";return t=se(r)||"en",t}ne=ie(),function(){if(function(){return"undefined"!==typeof __uniConfig&&__uniConfig.locales&&!!Object.keys(__uniConfig.locales).length}()){var e=Object.keys(__uniConfig.locales);e.length&&e.forEach((function(e){var t=re[e],n=__uniConfig.locales[e];t?Object.assign(t,n):re[e]=n}))}}();var oe=(0,f.initVueI18n)(ne,{}),ae=oe.t;oe.mixin={beforeCreate:function(){var e=this,t=oe.i18n.watchLocale((function(){e.$forceUpdate()}));this.$once("hook:beforeDestroy",(function(){t()}))},methods:{$$t:function(e,t){return ae(e,t)}}},oe.setLocale,oe.getLocale;function se(e,t){if(e){if(e=e.trim().replace(/_/g,"-"),t&&t[e])return e;if(e=e.toLowerCase(),"chinese"===e)return"zh-Hans";if(0===e.indexOf("zh"))return e.indexOf("-hans")>-1?"zh-Hans":e.indexOf("-hant")>-1||function(e,t){return!!t.find((function(t){return-1!==e.indexOf(t)}))}(e,["-tw","-hk","-mo","-cht"])?"zh-Hant":"zh-Hans";var n=function(e,t){return t.find((function(t){return 0===e.indexOf(t)}))}(e,["en","fr","es"]);return n||void 0}}function ce(){if(b(getApp)){var e=getApp({allowDefault:!0});if(e&&e.$vm)return e.$vm.$locale}return ie()}var ue=[];"undefined"!==typeof r&&(r.getLocale=ce);var le={promiseInterceptor:q},fe=Object.freeze({__proto__:null,upx2px:te,rpx2px:te,getLocale:ce,setLocale:function(e){var t=!!b(getApp)&&getApp();if(!t)return!1;var n=t.$vm.$locale;return n!==e&&(t.$vm.$locale=e,ue.forEach((function(t){return t({locale:e})})),!0)},onLocaleChange:function(e){-1===ue.indexOf(e)&&ue.push(e)},addInterceptor:function(e,t){"string"===typeof e&&k(t)?C(j[e]||(j[e]={}),t):k(e)&&C(T,e)},removeInterceptor:function(e,t){"string"===typeof e?k(t)?L(j[e],t):delete j[e]:k(e)&&L(T,e)},interceptors:le});var de,pe={name:function(e){return"back"===e.exists&&e.delta?"navigateBack":"redirectTo"},args:function(e){if("back"===e.exists&&e.url){var t=function(e){var t=getCurrentPages(),n=t.length;while(n--){var r=t[n];if(r.$page&&r.$page.fullPath===e)return n}return-1}(e.url);if(-1!==t){var n=getCurrentPages().length-1-t;n>0&&(e.delta=n)}}}},he={args:function(e){var t=parseInt(e.current);if(!isNaN(t)){var n=e.urls;if(Array.isArray(n)){var r=n.length;if(r)return t<0?t=0:t>=r&&(t=r-1),t>0?(e.current=n[t],e.urls=n.filter((function(e,r){return!(r<t)||e!==n[t]}))):e.current=n[0],{indicator:!1,loop:!1}}}}};function ve(t){de=de||e.getStorageSync("__DC_STAT_UUID"),de||(de=Date.now()+""+Math.floor(1e7*Math.random()),e.setStorage({key:"__DC_STAT_UUID",data:de})),t.deviceId=de}function me(e){if(e.safeArea){var t=e.safeArea;e.safeAreaInsets={top:t.top,left:t.left,right:e.windowWidth-t.right,bottom:e.screenHeight-t.bottom}}}function ge(e,t){var n="",r="";switch(n=e.split(" ")[0]||t,r=e.split(" ")[1]||"",n=n.toLocaleLowerCase(),n){case"harmony":case"ohos":case"openharmony":n="harmonyos";break;case"iphone os":n="ios";break;case"mac":case"darwin":n="macos";break;case"windows_nt":n="windows";break}return{osName:n,osVersion:r}}function ye(e,t){for(var n=e.deviceType||"phone",r={ipad:"pad",windows:"pc",mac:"pc"},i=Object.keys(r),o=t.toLocaleLowerCase(),a=0;a<i.length;a++){var s=i[a];if(-1!==o.indexOf(s)){n=r[s];break}}return n}function _e(e){var t=e;return t&&(t=e.toLocaleLowerCase()),t}function be(e){return ce?ce():e}function we(e){var t=e.hostName||"WeChat";return e.environment?t=e.environment:e.host&&e.host.env&&(t=e.host.env),t}var ke={returnValue:function(e){ve(e),me(e),function(e){var t=e.brand,n=void 0===t?"":t,r=e.model,i=void 0===r?"":r,o=e.system,a=void 0===o?"":o,s=e.language,c=void 0===s?"":s,u=e.theme,l=e.version,f=e.platform,d=e.fontSizeSetting,p=e.SDKVersion,h=e.pixelRatio,v=e.deviceOrientation,m=ge(a,f),g=m.osName,y=m.osVersion,_=l,b=ye(e,i),w=_e(n),k=we(e),x=v,O=h,A=p,S=(c||"").replace(/_/g,"-"),P={appId:"__UNI__9F50D13",appName:"租房小程序",appVersion:"1.0.0",appVersionCode:"100",appLanguage:be(S),uniCompileVersion:"4.75",uniCompilerVersion:"4.75",uniRuntimeVersion:"4.75",uniPlatform:"mp-weixin",deviceBrand:w,deviceModel:i,deviceType:b,devicePixelRatio:O,deviceOrientation:x,osName:g.toLocaleLowerCase(),osVersion:y,hostTheme:u,hostVersion:_,hostLanguage:S,hostName:k,hostSDKVersion:A,hostFontSizeSetting:d,windowTop:0,windowBottom:0,osLanguage:void 0,osTheme:void 0,ua:void 0,hostPackageName:void 0,browserName:void 0,browserVersion:void 0,isUniAppX:!1};Object.assign(e,P,{})}(e)}},xe={args:function(e){"object"===(0,l.default)(e)&&(e.alertText=e.title)}},Oe={returnValue:function(e){var t=e,n=t.version,r=t.language,i=t.SDKVersion,o=t.theme,a=we(e),s=(r||"").replace("_","-");e=I(Object.assign(e,{appId:"__UNI__9F50D13",appName:"租房小程序",appVersion:"1.0.0",appVersionCode:"100",appLanguage:be(s),hostVersion:n,hostLanguage:s,hostName:a,hostSDKVersion:i,hostTheme:o,isUniAppX:!1,uniPlatform:"mp-weixin",uniCompileVersion:"4.75",uniCompilerVersion:"4.75",uniRuntimeVersion:"4.75"}))}},Ae={returnValue:function(e){var t=e,n=t.brand,r=t.model,i=t.system,o=void 0===i?"":i,a=t.platform,s=void 0===a?"":a,c=ye(e,r),u=_e(n);ve(e);var l=ge(o,s),f=l.osName,d=l.osVersion;e=I(Object.assign(e,{deviceType:c,deviceBrand:u,deviceModel:r,osName:f,osVersion:d}))}},Se={returnValue:function(e){me(e),e=I(Object.assign(e,{windowTop:0,windowBottom:0}))}},Pe={redirectTo:pe,previewImage:he,getSystemInfo:ke,getSystemInfoSync:ke,showActionSheet:xe,getAppBaseInfo:Oe,getDeviceInfo:Ae,getWindowInfo:Se,getAppAuthorizeSetting:{returnValue:function(e){var t=e.locationReducedAccuracy;e.locationAccuracy="unsupported",!0===t?e.locationAccuracy="reduced":!1===t&&(e.locationAccuracy="full")}},compressImage:{args:function(e){e.compressedHeight&&!e.compressHeight&&(e.compressHeight=e.compressedHeight),e.compressedWidth&&!e.compressWidth&&(e.compressWidth=e.compressedWidth)}}},Ie=["success","fail","cancel","complete"];function Ee(e,t,n){return function(r){return t(je(e,r,n))}}function Te(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(k(t)){var o=!0===i?t:{};for(var a in b(n)&&(n=n(t,o)||{}),t)if(x(n,a)){var s=n[a];b(s)&&(s=s(t[a],t,o)),s?w(s)?o[s]=t[a]:k(s)&&(o[s.name?s.name:a]=s.value):console.warn("The '".concat(e,"' method of platform '微信小程序' does not support option '").concat(a,"'"))}else-1!==Ie.indexOf(a)?b(t[a])&&(o[a]=Ee(e,t[a],r)):i||(o[a]=t[a]);return o}return b(t)&&(t=Ee(e,t,r)),t}function je(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];return b(Pe.returnValue)&&(t=Pe.returnValue(e,t)),Te(e,t,n,{},r)}function Ce(t,n){if(x(Pe,t)){var r=Pe[t];return r?function(n,i){var o=r;b(r)&&(o=r(n)),n=Te(t,n,o.args,o.returnValue);var a=[n];"undefined"!==typeof i&&a.push(i),b(o.name)?t=o.name(n):w(o.name)&&(t=o.name);var s=e[t].apply(e,a);return W(t)?je(t,s,o.returnValue,J(t)):s}:function(){console.error("Platform '微信小程序' does not support '".concat(t,"'."))}}return n}var Le=Object.create(null);["onTabBarMidButtonTap","subscribePush","unsubscribePush","onPush","offPush","share"].forEach((function(e){Le[e]=function(e){return function(t){var n=t.fail,r=t.complete,i={errMsg:"".concat(e,":fail method '").concat(e,"' not supported")};b(n)&&n(i),b(r)&&r(i)}}(e)}));var $e={oauth:["weixin"],share:["weixin"],payment:["wxpay"],push:["weixin"]};var De=Object.freeze({__proto__:null,getProvider:function(e){var t=e.service,n=e.success,r=e.fail,i=e.complete,o=!1;$e[t]?(o={errMsg:"getProvider:ok",service:t,provider:$e[t]},b(n)&&n(o)):(o={errMsg:"getProvider:fail service not found"},b(r)&&r(o)),b(i)&&i(o)}}),Ne=function(){var e;return function(){return e||(e=new d.default),e}}();function Re(e,t,n){return e[t].apply(e,n)}var Me,Ue,Fe,qe=Object.freeze({__proto__:null,$on:function(){return Re(Ne(),"$on",Array.prototype.slice.call(arguments))},$off:function(){return Re(Ne(),"$off",Array.prototype.slice.call(arguments))},$once:function(){return Re(Ne(),"$once",Array.prototype.slice.call(arguments))},$emit:function(){return Re(Ne(),"$emit",Array.prototype.slice.call(arguments))}});function Be(e){return function(){try{return e.apply(e,arguments)}catch(t){console.error(t)}}}function Ve(e){try{return JSON.parse(e)}catch(t){}return e}var He=[];function Ke(e,t){He.forEach((function(n){n(e,t)})),He.length=0}var ze=[];var Je=e.getAppBaseInfo&&e.getAppBaseInfo();Je||(Je=e.getSystemInfoSync());var We=Je?Je.host:null,Ge=We&&"SAAASDK"===We.env?e.miniapp.shareVideoMessage:e.shareVideoMessage,Ye=Object.freeze({__proto__:null,shareVideoMessage:Ge,getPushClientId:function(e){k(e)||(e={});var t=function(e){var t={};for(var n in e){var r=e[n];b(r)&&(t[n]=Be(r),delete e[n])}return t}(e),n=t.success,r=t.fail,i=t.complete,o=b(n),a=b(r),s=b(i);Promise.resolve().then((function(){"undefined"===typeof Fe&&(Fe=!1,Me="",Ue="uniPush is not enabled"),He.push((function(e,t){var c;e?(c={errMsg:"getPushClientId:ok",cid:e},o&&n(c)):(c={errMsg:"getPushClientId:fail"+(t?" "+t:"")},a&&r(c)),s&&i(c)})),"undefined"!==typeof Me&&Ke(Me,Ue)}))},onPushMessage:function(e){-1===ze.indexOf(e)&&ze.push(e)},offPushMessage:function(e){if(e){var t=ze.indexOf(e);t>-1&&ze.splice(t,1)}else ze.length=0},invokePushCallback:function(e){if("enabled"===e.type)Fe=!0;else if("clientId"===e.type)Me=e.cid,Ue=e.errMsg,Ke(Me,e.errMsg);else if("pushMsg"===e.type)for(var t={type:"receive",data:Ve(e.message)},n=0;n<ze.length;n++){var r=ze[n];if(r(t),t.stopped)break}else"click"===e.type&&ze.forEach((function(t){t({type:"click",data:Ve(e.message)})}))},__f__:function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];console[e].apply(console,n)}}),Qe=["__route__","__wxExparserNodeId__","__wxWebviewId__"];function Xe(e){return Behavior(e)}function Ze(){return!!this.route}function et(e){this.triggerEvent("__l",e)}function tt(e){var t=e.$scope,n={};Object.defineProperty(e,"$refs",{get:function(){var e={};(function e(t,n,r){var i=t.selectAllComponents(n)||[];i.forEach((function(t){var i=t.dataset.ref;r[i]=t.$vm||it(t),"scoped"===t.dataset.vueGeneric&&t.selectAllComponents(".scoped-ref").forEach((function(t){e(t,n,r)}))}))})(t,".vue-ref",e);var r=t.selectAllComponents(".vue-ref-in-for")||[];return r.forEach((function(t){var n=t.dataset.ref;e[n]||(e[n]=[]),e[n].push(t.$vm||it(t))})),function(e,t){var n=(0,c.default)(Set,(0,u.default)(Object.keys(e))),r=Object.keys(t);return r.forEach((function(r){var i=e[r],o=t[r];Array.isArray(i)&&Array.isArray(o)&&i.length===o.length&&o.every((function(e){return i.includes(e)}))||(e[r]=o,n.delete(r))})),n.forEach((function(t){delete e[t]})),e}(n,e)}})}function nt(e){var t,n=e.detail||e.value,r=n.vuePid,i=n.vueOptions;r&&(t=function e(t,n){for(var r,i=t.$children,o=i.length-1;o>=0;o--){var a=i[o];if(a.$scope._$vueId===n)return a}for(var s=i.length-1;s>=0;s--)if(r=e(i[s],n),r)return r}(this.$vm,r)),t||(t=this.$vm),i.parent=t}function rt(e){return Object.defineProperty(e,"__v_isMPComponent",{configurable:!0,enumerable:!1,value:!0}),e}function it(e){return function(e){return null!==e&&"object"===(0,l.default)(e)}(e)&&Object.isExtensible(e)&&Object.defineProperty(e,"__ob__",{configurable:!0,enumerable:!1,value:(0,s.default)({},"__v_skip",!0)}),e}var ot=/_(.*)_worklet_factory_/;var at=Page,st=Component,ct=/:/g,ut=A((function(e){return P(e.replace(ct,"-"))}));function lt(e){var t=e.triggerEvent,n=function(e){for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];if(this.$vm||this.dataset&&this.dataset.comType)e=ut(e);else{var o=ut(e);o!==e&&t.apply(this,[o].concat(r))}return t.apply(this,[e].concat(r))};try{e.triggerEvent=n}catch(r){e._triggerEvent=n}}function ft(e,t,n){var r=t[e];t[e]=function(){if(rt(this),lt(this),r){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return r.apply(this,t)}}}at.__$wrappered||(at.__$wrappered=!0,Page=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return ft("onLoad",e),at(e)},Page.after=at.after,Component=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return ft("created",e),st(e)});function dt(e,t,n){t.forEach((function(t){(function e(t,n){if(!n)return!0;if(d.default.options&&Array.isArray(d.default.options[t]))return!0;if(n=n.default||n,b(n))return!!b(n.extendOptions[t])||!!(n.super&&n.super.options&&Array.isArray(n.super.options[t]));if(b(n[t])||Array.isArray(n[t]))return!0;var r=n.mixins;return Array.isArray(r)?!!r.find((function(n){return e(t,n)})):void 0})(t,n)&&(e[t]=function(e){return this.$vm&&this.$vm.__call_hook(t,e)})}))}function pt(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];ht(t).forEach((function(t){return vt(e,t,n)}))}function ht(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return e&&Object.keys(e).forEach((function(n){0===n.indexOf("on")&&b(e[n])&&t.push(n)})),t}function vt(e,t,n){-1!==n.indexOf(t)||x(e,t)||(e[t]=function(e){return this.$vm&&this.$vm.__call_hook(t,e)})}function mt(e,t){var n;return t=t.default||t,n=b(t)?t:e.extend(t),t=n.options,[n,t]}function gt(e,t){if(Array.isArray(t)&&t.length){var n=Object.create(null);t.forEach((function(e){n[e]=!0})),e.$scopedSlots=e.$slots=n}}function yt(e,t){e=(e||"").split(",");var n=e.length;1===n?t._$vueId=e[0]:2===n&&(t._$vueId=e[0],t._$vuePid=e[1])}function _t(e,t){var n=e.data||{},r=e.methods||{};if("function"===typeof n)try{n=n.call(t)}catch(i){Object({NODE_ENV:"production",VUE_APP_DARK_MODE:"false",VUE_APP_NAME:"租房小程序",VUE_APP_PLATFORM:"mp-weixin",BASE_URL:"/"}).VUE_APP_DEBUG&&console.warn("根据 Vue 的 data 函数初始化小程序 data 失败，请尽量确保 data 函数中不访问 vm 对象，否则可能影响首次数据渲染速度。",n)}else try{n=JSON.parse(JSON.stringify(n))}catch(i){}return k(n)||(n={}),Object.keys(r).forEach((function(e){-1!==t.__lifecycle_hooks__.indexOf(e)||x(n,e)||(n[e]=r[e])})),n}var bt=[String,Number,Boolean,Object,Array,null];function wt(e){return function(t,n){this.$vm&&(this.$vm[e]=t)}}function kt(e,t){var n=e.behaviors,r=e.extends,i=e.mixins,o=e.props;o||(e.props=o=[]);var a=[];return Array.isArray(n)&&n.forEach((function(e){a.push(e.replace("uni://","wx".concat("://"))),"uni://form-field"===e&&(Array.isArray(o)?(o.push("name"),o.push("value")):(o.name={type:String,default:""},o.value={type:[String,Number,Boolean,Array,Object,Date],default:""}))})),k(r)&&r.props&&a.push(t({properties:Ot(r.props,!0)})),Array.isArray(i)&&i.forEach((function(e){k(e)&&e.props&&a.push(t({properties:Ot(e.props,!0)}))})),a}function xt(e,t,n,r){return Array.isArray(t)&&1===t.length?t[0]:t}function Ot(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>3?arguments[3]:void 0,r={};return t||(r.vueId={type:String,value:""},n.virtualHost&&(r.virtualHostStyle={type:null,value:""},r.virtualHostClass={type:null,value:""}),r.scopedSlotsCompiler={type:String,value:""},r.vueSlots={type:null,value:[],observer:function(e,t){var n=Object.create(null);e.forEach((function(e){n[e]=!0})),this.setData({$slots:n})}}),Array.isArray(e)?e.forEach((function(e){r[e]={type:null,observer:wt(e)}})):k(e)&&Object.keys(e).forEach((function(t){var n=e[t];if(k(n)){var i=n.default;b(i)&&(i=i()),n.type=xt(0,n.type),r[t]={type:-1!==bt.indexOf(n.type)?n.type:null,value:i,observer:wt(t)}}else{var o=xt(0,n);r[t]={type:-1!==bt.indexOf(o)?o:null,observer:wt(t)}}})),r}function At(e,t,n,r){var i={};return Array.isArray(t)&&t.length&&t.forEach((function(t,o){"string"===typeof t?t?"$event"===t?i["$"+o]=n:"arguments"===t?i["$"+o]=n.detail&&n.detail.__args__||r:0===t.indexOf("$event.")?i["$"+o]=e.__get_value(t.replace("$event.",""),n):i["$"+o]=e.__get_value(t):i["$"+o]=e:i["$"+o]=function(e,t){var n=e;return t.forEach((function(t){var r=t[0],i=t[2];if(r||"undefined"!==typeof i){var o,a=t[1],s=t[3];Number.isInteger(r)?o=r:r?"string"===typeof r&&r&&(o=0===r.indexOf("#s#")?r.substr(3):e.__get_value(r,n)):o=n,Number.isInteger(o)?n=i:a?Array.isArray(o)?n=o.find((function(t){return e.__get_value(a,t)===i})):k(o)?n=Object.keys(o).find((function(t){return e.__get_value(a,o[t])===i})):console.error("v-for 暂不支持循环数据：",o):n=o[i],s&&(n=e.__get_value(s,n))}})),n}(e,t)})),i}function St(e){for(var t={},n=1;n<e.length;n++){var r=e[n];t[r[0]]=r[1]}return t}function Pt(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[],i=arguments.length>4?arguments[4]:void 0,o=arguments.length>5?arguments[5]:void 0,a=!1,s=k(t.detail)&&t.detail.__args__||[t.detail];if(i&&(a=t.currentTarget&&t.currentTarget.dataset&&"wx"===t.currentTarget.dataset.comType,!n.length))return a?[t]:s;var c=At(e,r,t,s),u=[];return n.forEach((function(e){"$event"===e?"__set_model"!==o||i?i&&!a?u.push(s[0]):u.push(t):u.push(t.target.value):Array.isArray(e)&&"o"===e[0]?u.push(St(e)):"string"===typeof e&&x(c,e)?u.push(c[e]):u.push(e)})),u}function It(e){var t=this;e=function(e){try{e.mp=JSON.parse(JSON.stringify(e))}catch(t){}return e.stopPropagation=O,e.preventDefault=O,e.target=e.target||{},x(e,"detail")||(e.detail={}),x(e,"markerId")&&(e.detail="object"===(0,l.default)(e.detail)?e.detail:{},e.detail.markerId=e.markerId),k(e.detail)&&(e.target=Object.assign({},e.target,e.detail)),e}(e);var n=(e.currentTarget||e.target).dataset;if(!n)return console.warn("事件信息不存在");var r=n.eventOpts||n["event-opts"];if(!r)return console.warn("事件信息不存在");var i=e.type,o=[];return r.forEach((function(n){var r=n[0],a=n[1],s="^"===r.charAt(0);r=s?r.slice(1):r;var c="~"===r.charAt(0);r=c?r.slice(1):r,a&&function(e,t){return e===t||"regionchange"===t&&("begin"===e||"end"===e)}(i,r)&&a.forEach((function(n){var r=n[0];if(r){var i=t.$vm;if(i.$options.generic&&(i=function(e){var t=e.$parent;while(t&&t.$parent&&(t.$options.generic||t.$parent.$options.generic||t.$scope._$vuePid))t=t.$parent;return t&&t.$parent}(i)||i),"$emit"===r)return void i.$emit.apply(i,Pt(t.$vm,e,n[1],n[2],s,r));var a=i[r];if(!b(a)){var u="page"===t.$vm.mpType?"Page":"Component",l=t.route||t.is;throw new Error("".concat(u,' "').concat(l,'" does not have a method "').concat(r,'"'))}if(c){if(a.once)return;a.once=!0}var f=Pt(t.$vm,e,n[1],n[2],s,r);f=Array.isArray(f)?f:[],/=\s*\S+\.eventParams\s*\|\|\s*\S+\[['"]event-params['"]\]/.test(a.toString())&&(f=f.concat([,,,,,,,,,,e])),o.push(a.apply(i,f))}}))})),"input"===i&&1===o.length&&"undefined"!==typeof o[0]?o[0]:void 0}var Et={};var Tt=["onShow","onHide","onError","onPageNotFound","onThemeChange","onUnhandledRejection"];function jt(){d.default.prototype.getOpenerEventChannel=function(){return this.$scope.getOpenerEventChannel()};var e=d.default.prototype.__call_hook;d.default.prototype.__call_hook=function(t,n){return"onLoad"===t&&n&&n.__id__&&(this.__eventChannel__=function(e){var t=Et[e];return delete Et[e],t}(n.__id__),delete n.__id__),e.call(this,t,n)}}function Ct(t,n){var r=n.mocks,i=n.initRefs;jt(),function(){var e={},t={};function n(e){var t=this.$options.propsData.vueId;if(t){var n=t.split(",")[0];e(n)}}d.default.prototype.$hasSSP=function(n){var r=e[n];return r||(t[n]=this,this.$on("hook:destroyed",(function(){delete t[n]}))),r},d.default.prototype.$getSSP=function(t,n,r){var i=e[t];if(i){var o=i[n]||[];return r?o:o[0]}},d.default.prototype.$setSSP=function(t,r){var i=0;return n.call(this,(function(n){var o=e[n],a=o[t]=o[t]||[];a.push(r),i=a.length-1})),i},d.default.prototype.$initSSP=function(){n.call(this,(function(t){e[t]={}}))},d.default.prototype.$callSSP=function(){n.call(this,(function(e){t[e]&&t[e].$forceUpdate()}))},d.default.mixin({destroyed:function(){var n=this.$options.propsData,r=n&&n.vueId;r&&(delete e[r],delete t[r])}})}(),t.$options.store&&(d.default.prototype.$store=t.$options.store),function(e){e.prototype.uniIDHasRole=function(e){var t=g(),n=t.role;return n.indexOf(e)>-1},e.prototype.uniIDHasPermission=function(e){var t=g(),n=t.permission;return this.uniIDHasRole("admin")||n.indexOf(e)>-1},e.prototype.uniIDTokenValid=function(){var e=g(),t=e.tokenExpired;return t>Date.now()}}(d.default),d.default.prototype.mpHost="mp-weixin",d.default.mixin({beforeCreate:function(){if(this.$options.mpType){if(this.mpType=this.$options.mpType,this.$mp=(0,s.default)({data:{}},this.mpType,this.$options.mpInstance),this.$scope=this.$options.mpInstance,delete this.$options.mpType,delete this.$options.mpInstance,"page"===this.mpType&&"function"===typeof getApp){var e=getApp();e.$vm&&e.$vm.$i18n&&(this._i18n=e.$vm.$i18n)}"app"!==this.mpType&&(i(this),function(e,t){var n=e.$mp[e.mpType];t.forEach((function(t){x(n,t)&&(e[t]=n[t])}))}(this,r))}}});var o={onLaunch:function(n){this.$vm||(e.canIUse&&!e.canIUse("nextTick")&&console.error("当前微信基础库版本过低，请将 微信开发者工具-详情-项目设置-调试基础库版本 更换为`2.3.0`以上"),this.$vm=t,this.$vm.$mp={app:this},this.$vm.$scope=this,this.$vm.globalData=this.globalData,this.$vm._isMounted=!0,this.$vm.__call_hook("mounted",n),this.$vm.__call_hook("onLaunch",n))}};o.globalData=t.$options.globalData||{};var a=t.$options.methods;return a&&Object.keys(a).forEach((function(e){o[e]=a[e]})),function(e,t,n){var r=e.observable({locale:n||oe.getLocale()}),i=[];t.$watchLocale=function(e){i.push(e)},Object.defineProperty(t,"$locale",{get:function(){return r.locale},set:function(e){r.locale=e,i.forEach((function(t){return t(e)}))}})}(d.default,t,function(){var t,n=e.getAppBaseInfo(),r=n&&n.language?n.language:"en";return t=se(r)||"en",t}()),dt(o,Tt),pt(o,t.$options),o}function Lt(e){return Ct(e,{mocks:Qe,initRefs:tt})}function $t(e){return App(Lt(e)),e}var Dt=/[!'()*]/g,Nt=function(e){return"%"+e.charCodeAt(0).toString(16)},Rt=/%2C/g,Mt=function(e){return encodeURIComponent(e).replace(Dt,Nt).replace(Rt,",")};function Ut(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Mt,n=e?Object.keys(e).map((function(n){var r=e[n];if(void 0===r)return"";if(null===r)return t(n);if(Array.isArray(r)){var i=[];return r.forEach((function(e){void 0!==e&&(null===e?i.push(t(n)):i.push(t(n)+"="+t(e)))})),i.join("&")}return t(n)+"="+t(r)})).filter((function(e){return e.length>0})).join("&"):null;return n?"?".concat(n):""}function Ft(e,t){return function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.isPage,r=t.initRelation,i=arguments.length>2?arguments[2]:void 0,o=mt(d.default,e),s=(0,a.default)(o,2),c=s[0],u=s[1],l=h({multipleSlots:!0,addGlobalClass:!0},u.options||{});u["mp-weixin"]&&u["mp-weixin"].options&&Object.assign(l,u["mp-weixin"].options);var f={options:l,data:_t(u,d.default.prototype),behaviors:kt(u,Xe),properties:Ot(u.props,!1,u.__file,l),lifetimes:{attached:function(){var e=this.properties,t={mpType:n.call(this)?"page":"component",mpInstance:this,propsData:e};yt(e.vueId,this),r.call(this,{vuePid:this._$vuePid,vueOptions:t}),this.$vm=new c(t),gt(this.$vm,e.vueSlots),this.$vm.$mount()},ready:function(){this.$vm&&(this.$vm._isMounted=!0,this.$vm.__call_hook("mounted"),this.$vm.__call_hook("onReady"))},detached:function(){this.$vm&&this.$vm.$destroy()}},pageLifetimes:{show:function(e){this.$vm&&this.$vm.__call_hook("onPageShow",e)},hide:function(){this.$vm&&this.$vm.__call_hook("onPageHide")},resize:function(e){this.$vm&&this.$vm.__call_hook("onPageResize",e)}},methods:{__l:nt,__e:It}};return u.externalClasses&&(f.externalClasses=u.externalClasses),Array.isArray(u.wxsCallMethods)&&u.wxsCallMethods.forEach((function(e){f.methods[e]=function(t){return this.$vm[e](t)}})),i?[f,u,c]:n?f:[f,c]}(e,{isPage:Ze,initRelation:et},t)}var qt=["onShow","onHide","onUnload"];function Bt(e){var t=Ft(e,!0),n=(0,a.default)(t,2),r=n[0],i=n[1];return dt(r.methods,qt,i),r.methods.onLoad=function(e){this.options=e;var t=Object.assign({},e);delete t.__id__,this.$page={fullPath:"/"+(this.route||this.is)+Ut(t)},this.$vm.$mp.query=e,this.$vm.__call_hook("onLoad",e)},pt(r.methods,e,["onReady"]),function(e,t){t&&Object.keys(t).forEach((function(n){var r=n.match(ot);if(r){var i=r[1];e[n]=t[n],e[i]=t[i]}}))}(r.methods,i.methods),r}function Vt(e){return Component(function(e){return Bt(e)}(e))}function Ht(e){return Component(Ft(e))}function Kt(t){var n=Lt(t),r=getApp({allowDefault:!0});t.$scope=r;var i=r.globalData;if(i&&Object.keys(n.globalData).forEach((function(e){x(i,e)||(i[e]=n.globalData[e])})),Object.keys(n).forEach((function(e){x(r,e)||(r[e]=n[e])})),b(n.onShow)&&e.onAppShow&&e.onAppShow((function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];t.__call_hook("onShow",n)})),b(n.onHide)&&e.onAppHide&&e.onAppHide((function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];t.__call_hook("onHide",n)})),b(n.onLaunch)){var o=e.getLaunchOptionsSync&&e.getLaunchOptionsSync();t.__call_hook("onLaunch",o)}return t}function zt(t){var n=Lt(t);if(b(n.onShow)&&e.onAppShow&&e.onAppShow((function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];t.__call_hook("onShow",n)})),b(n.onHide)&&e.onAppHide&&e.onAppHide((function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];t.__call_hook("onHide",n)})),b(n.onLaunch)){var r=e.getLaunchOptionsSync&&e.getLaunchOptionsSync();t.__call_hook("onLaunch",r)}return t}qt.push.apply(qt,["onPullDownRefresh","onReachBottom","onAddToFavorites","onShareTimeline","onShareAppMessage","onPageScroll","onResize","onTabItemTap"]),["vibrate","preloadPage","unPreloadPage","loadSubPackage"].forEach((function(e){Pe[e]=!1})),[].forEach((function(t){var n=Pe[t]&&Pe[t].name?Pe[t].name:t;e.canIUse(n)||(Pe[t]=!1)}));var Jt={};"undefined"!==typeof Proxy?Jt=new Proxy({},{get:function(t,n){return x(t,n)?t[n]:fe[n]?fe[n]:Ye[n]?Q(n,Ye[n]):De[n]?Q(n,De[n]):Le[n]?Q(n,Le[n]):qe[n]?qe[n]:Q(n,Ce(n,e[n]))},set:function(e,t,n){return e[t]=n,!0}}):(Object.keys(fe).forEach((function(e){Jt[e]=fe[e]})),Object.keys(Le).forEach((function(e){Jt[e]=Q(e,Le[e])})),Object.keys(De).forEach((function(e){Jt[e]=Q(e,De[e])})),Object.keys(qe).forEach((function(e){Jt[e]=qe[e]})),Object.keys(Ye).forEach((function(e){Jt[e]=Q(e,Ye[e])})),Object.keys(e).forEach((function(t){(x(e,t)||x(Pe,t))&&(Jt[t]=Q(t,Ce(t,e[t])))}))),e.createApp=$t,e.createPage=Vt,e.createComponent=Ht,e.createSubpackageApp=Kt,e.createPlugin=zt;var Wt=Jt,Gt=Wt;t.default=Gt}).call(this,n("3223")["default"],n("0ee4"))},e011:function(e,t,n){"use strict";var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=r(n("b282")),o=r(n("7b83")),a=r(n("3a0f")),s={en:i.default,"zh-Hans":o.default,"zh-Hant":a.default};t.default=s},e638:function(e,t,n){"use strict";(function(e,r){var i=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){return f.apply(this,arguments)};var o=i(n("7eb4")),a=i(n("ee10")),s=i(n("8708")),c=e.importObject("uni-id-co",{customUI:!0}),u=s.default.loginTypes,l=s.default.debug;function f(){return f=(0,a.default)(o.default.mark((function t(){var n,i,s,f,d,p;return o.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(p=function(e){e.code,e.message},!l){t.next=10;break}return t.next=4,c.getSupportedLoginType();case 4:n=t.sent,i=n.supportedLoginType,console.log("supportedLoginType: "+JSON.stringify(i)),s={smsCode:"mobile-code",univerify:"univerify",username:"username-password",weixin:"weixin",qq:"qq",xiaomi:"xiaomi",sinaweibo:"sinaweibo",taobao:"taobao",facebook:"facebook",google:"google",alipay:"alipay",apple:"apple",weixinMobile:"weixin"},f=u.filter((function(e){return!i.includes(s[e])})),f.length&&console.error("错误：前端启用的登录方式:".concat(f.join("，"),';没有在服务端完成配置。配置文件路径："/uni_modules/uni-config-center/uniCloud/cloudfunctions/common/uni-config-center/uni-id/config.json"'));case 10:d=e.database(),d.on("error",p),e.onRefreshToken&&e.onRefreshToken((function(){r.getPushClientId&&r.getPushClientId({success:function(){var e=(0,a.default)(o.default.mark((function e(t){var n;return o.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=t.cid,e.next=3,c.setPushCid({pushClientId:n});case 3:e.sent;case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),fail:function(e){}})}));case 13:case"end":return t.stop()}}),t)}))),f.apply(this,arguments)}}).call(this,n("861b")["uniCloud"],n("df3c")["default"])},e6db:function(e,t,n){var r=n("3b2d")["default"];e.exports=function(e,t){if("object"!=r(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!=r(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports["default"]=e.exports},e9da:function(e){e.exports=JSON.parse('{"uni-load-more.contentdown":"上拉顯示更多","uni-load-more.contentrefresh":"正在加載...","uni-load-more.contentnomore":"沒有更多數據了"}')},ed45:function(e,t){e.exports=function(e){if(Array.isArray(e))return e},e.exports.__esModule=!0,e.exports["default"]=e.exports},ee10:function(e,t){function n(e,t,n,r,i,o,a){try{var s=e[o](a),c=s.value}catch(u){return void n(u)}s.done?t(c):Promise.resolve(c).then(r,i)}e.exports=function(e){return function(){var t=this,r=arguments;return new Promise((function(i,o){var a=e.apply(t,r);function s(e){n(a,i,o,s,c,"next",e)}function c(e){n(a,i,o,s,c,"throw",e)}s(void 0)}))}},e.exports.__esModule=!0,e.exports["default"]=e.exports},f12a:function(e,t,n){"use strict";(function(e,r){var i=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.store=t.mutations=void 0;var o=i(n("7eb4")),a=i(n("7ca3")),s=i(n("ee10")),c=i(n("23ba")),u=i(n("8708")),l=i(n("3240"));function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?f(Object(n),!0).forEach((function(t){(0,a.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var p=e.importObject("uni-id-co"),h=e.database(),v=h.collection("uni-id-users"),m=r.getStorageSync("uni-id-pages-userInfo")||{},g={userInfo:m,hasLogin:0!=Object.keys(m).length},y={updateUserInfo:function(){var t=arguments,n=this;return(0,s.default)(o.default.mark((function i(){var a,s,c,u;return o.default.wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(a=t.length>0&&void 0!==t[0]&&t[0],!a){i.next=5;break}v.where("_id==$env.uid").update(a).then((function(e){e.result.updated?(r.showToast({title:"更新成功",icon:"none",duration:3e3}),n.setUserInfo(a)):r.showToast({title:"没有改变",icon:"none",duration:3e3})})),i.next=20;break;case 5:return s=e.importObject("uni-id-co",{customUI:!0}),i.prev=6,i.next=9,v.where("'_id' == $cloudEnv_uid").field("mobile,nickname,username,email,avatar_file").get();case 9:return c=i.sent,i.next=12,s.getRealNameInfo();case 12:u=i.sent,n.setUserInfo(d(d({},c.result.data[0]),{},{realNameAuth:u})),i.next=20;break;case 16:i.prev=16,i.t0=i["catch"](6),n.setUserInfo({},{cover:!0}),console.error(i.t0.message,i.t0.errCode);case 20:case"end":return i.stop()}}),i,null,[[6,16]])})))()},setUserInfo:function(e){var t=arguments;return(0,s.default)(o.default.mark((function n(){var i,a,s;return o.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return i=t.length>1&&void 0!==t[1]?t[1]:{cover:!1},a=i.cover,s=a?e:Object.assign(_.userInfo,e),_.userInfo=Object.assign({},s),_.hasLogin=0!=Object.keys(_.userInfo).length,r.setStorageSync("uni-id-pages-userInfo",_.userInfo),n.abrupt("return",e);case 6:case"end":return n.stop()}}),n)})))()},logout:function(){var t=this;return(0,s.default)(o.default.mark((function n(){return o.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!(e.getCurrentUserInfo().tokenExpired>Date.now())){n.next=9;break}return n.prev=1,n.next=4,p.logout();case 4:n.next=9;break;case 6:n.prev=6,n.t0=n["catch"](1),console.error(n.t0);case 9:r.removeStorageSync("uni_id_token"),r.setStorageSync("uni_id_token_expired",0),r.redirectTo({url:"/".concat(c.default.uniIdRouter&&c.default.uniIdRouter.loginPage?c.default.uniIdRouter.loginPage:"uni_modules/uni-id-pages/pages/login/login-withoutpwd")}),r.$emit("uni-id-pages-logout"),t.setUserInfo({},{cover:!0});case 14:case"end":return n.stop()}}),n,null,[[1,6]])})))()},loginBack:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.uniIdRedirectUrl,n=void 0===t?"":t,i=0,o=getCurrentPages();if(o.forEach((function(e,t){"login"==o[o.length-t-1].route.split("/")[3]&&i++})),n)return r.redirectTo({url:n,fail:function(e){r.switchTab({url:n,fail:function(t){console.log(e,t)}})}});if(i){var a=c.default.pages[0];return r.reLaunch({url:"/".concat(a.path)})}r.navigateBack({delta:i})},loginSuccess:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.showToast,n=void 0===t||t,i=e.toastText,o=void 0===i?"登录成功":i,a=e.autoBack,s=void 0===a||a,c=e.uniIdRedirectUrl,l=void 0===c?"":c,f=e.passwordConfirmed;if(n&&r.showToast({title:o,icon:"none",duration:3e3}),this.updateUserInfo(),r.$emit("uni-id-pages-login-success"),u.default.setPasswordAfterLogin&&!f)return r.redirectTo({url:l?"/uni_modules/uni-id-pages/pages/userinfo/set-pwd/set-pwd?uniIdRedirectUrl=".concat(l,"&loginType=").concat(e.loginType):"/uni_modules/uni-id-pages/pages/userinfo/set-pwd/set-pwd?loginType=".concat(e.loginType),fail:function(e){console.log(e)}});s&&this.loginBack({uniIdRedirectUrl:l})}};t.mutations=y;var _=l.default.observable(g);t.store=_}).call(this,n("861b")["uniCloud"],n("df3c")["default"])},f5cc:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={id:"2852637",name:"uniui图标库",font_family:"uniicons",css_prefix_text:"uniui-",description:"",glyphs:[{icon_id:"25027049",name:"yanse",font_class:"color",unicode:"e6cf",unicode_decimal:59087},{icon_id:"25027048",name:"wallet",font_class:"wallet",unicode:"e6b1",unicode_decimal:59057},{icon_id:"25015720",name:"settings-filled",font_class:"settings-filled",unicode:"e6ce",unicode_decimal:59086},{icon_id:"25015434",name:"shimingrenzheng-filled",font_class:"auth-filled",unicode:"e6cc",unicode_decimal:59084},{icon_id:"24934246",name:"shop-filled",font_class:"shop-filled",unicode:"e6cd",unicode_decimal:59085},{icon_id:"24934159",name:"staff-filled-01",font_class:"staff-filled",unicode:"e6cb",unicode_decimal:59083},{icon_id:"24932461",name:"VIP-filled",font_class:"vip-filled",unicode:"e6c6",unicode_decimal:59078},{icon_id:"24932462",name:"plus_circle_fill",font_class:"plus-filled",unicode:"e6c7",unicode_decimal:59079},{icon_id:"24932463",name:"folder_add-filled",font_class:"folder-add-filled",unicode:"e6c8",unicode_decimal:59080},{icon_id:"24932464",name:"yanse-filled",font_class:"color-filled",unicode:"e6c9",unicode_decimal:59081},{icon_id:"24932465",name:"tune-filled",font_class:"tune-filled",unicode:"e6ca",unicode_decimal:59082},{icon_id:"24932455",name:"a-rilidaka-filled",font_class:"calendar-filled",unicode:"e6c0",unicode_decimal:59072},{icon_id:"24932456",name:"notification-filled",font_class:"notification-filled",unicode:"e6c1",unicode_decimal:59073},{icon_id:"24932457",name:"wallet-filled",font_class:"wallet-filled",unicode:"e6c2",unicode_decimal:59074},{icon_id:"24932458",name:"paihangbang-filled",font_class:"medal-filled",unicode:"e6c3",unicode_decimal:59075},{icon_id:"24932459",name:"gift-filled",font_class:"gift-filled",unicode:"e6c4",unicode_decimal:59076},{icon_id:"24932460",name:"fire-filled",font_class:"fire-filled",unicode:"e6c5",unicode_decimal:59077},{icon_id:"24928001",name:"refreshempty",font_class:"refreshempty",unicode:"e6bf",unicode_decimal:59071},{icon_id:"24926853",name:"location-ellipse",font_class:"location-filled",unicode:"e6af",unicode_decimal:59055},{icon_id:"24926735",name:"person-filled",font_class:"person-filled",unicode:"e69d",unicode_decimal:59037},{icon_id:"24926703",name:"personadd-filled",font_class:"personadd-filled",unicode:"e698",unicode_decimal:59032},{icon_id:"24923351",name:"back",font_class:"back",unicode:"e6b9",unicode_decimal:59065},{icon_id:"24923352",name:"forward",font_class:"forward",unicode:"e6ba",unicode_decimal:59066},{icon_id:"24923353",name:"arrowthinright",font_class:"arrow-right",unicode:"e6bb",unicode_decimal:59067},{icon_id:"24923353",name:"arrowthinright",font_class:"arrowthinright",unicode:"e6bb",unicode_decimal:59067},{icon_id:"24923354",name:"arrowthinleft",font_class:"arrow-left",unicode:"e6bc",unicode_decimal:59068},{icon_id:"24923354",name:"arrowthinleft",font_class:"arrowthinleft",unicode:"e6bc",unicode_decimal:59068},{icon_id:"24923355",name:"arrowthinup",font_class:"arrow-up",unicode:"e6bd",unicode_decimal:59069},{icon_id:"24923355",name:"arrowthinup",font_class:"arrowthinup",unicode:"e6bd",unicode_decimal:59069},{icon_id:"24923356",name:"arrowthindown",font_class:"arrow-down",unicode:"e6be",unicode_decimal:59070},{icon_id:"24923356",name:"arrowthindown",font_class:"arrowthindown",unicode:"e6be",unicode_decimal:59070},{icon_id:"24923349",name:"arrowdown",font_class:"bottom",unicode:"e6b8",unicode_decimal:59064},{icon_id:"24923349",name:"arrowdown",font_class:"arrowdown",unicode:"e6b8",unicode_decimal:59064},{icon_id:"24923346",name:"arrowright",font_class:"right",unicode:"e6b5",unicode_decimal:59061},{icon_id:"24923346",name:"arrowright",font_class:"arrowright",unicode:"e6b5",unicode_decimal:59061},{icon_id:"24923347",name:"arrowup",font_class:"top",unicode:"e6b6",unicode_decimal:59062},{icon_id:"24923347",name:"arrowup",font_class:"arrowup",unicode:"e6b6",unicode_decimal:59062},{icon_id:"24923348",name:"arrowleft",font_class:"left",unicode:"e6b7",unicode_decimal:59063},{icon_id:"24923348",name:"arrowleft",font_class:"arrowleft",unicode:"e6b7",unicode_decimal:59063},{icon_id:"24923334",name:"eye",font_class:"eye",unicode:"e651",unicode_decimal:58961},{icon_id:"24923335",name:"eye-filled",font_class:"eye-filled",unicode:"e66a",unicode_decimal:58986},{icon_id:"24923336",name:"eye-slash",font_class:"eye-slash",unicode:"e6b3",unicode_decimal:59059},{icon_id:"24923337",name:"eye-slash-filled",font_class:"eye-slash-filled",unicode:"e6b4",unicode_decimal:59060},{icon_id:"24923305",name:"info-filled",font_class:"info-filled",unicode:"e649",unicode_decimal:58953},{icon_id:"24923299",name:"reload-01",font_class:"reload",unicode:"e6b2",unicode_decimal:59058},{icon_id:"24923195",name:"mic_slash_fill",font_class:"micoff-filled",unicode:"e6b0",unicode_decimal:59056},{icon_id:"24923165",name:"map-pin-ellipse",font_class:"map-pin-ellipse",unicode:"e6ac",unicode_decimal:59052},{icon_id:"24923166",name:"map-pin",font_class:"map-pin",unicode:"e6ad",unicode_decimal:59053},{icon_id:"24923167",name:"location",font_class:"location",unicode:"e6ae",unicode_decimal:59054},{icon_id:"24923064",name:"starhalf",font_class:"starhalf",unicode:"e683",unicode_decimal:59011},{icon_id:"24923065",name:"star",font_class:"star",unicode:"e688",unicode_decimal:59016},{icon_id:"24923066",name:"star-filled",font_class:"star-filled",unicode:"e68f",unicode_decimal:59023},{icon_id:"24899646",name:"a-rilidaka",font_class:"calendar",unicode:"e6a0",unicode_decimal:59040},{icon_id:"24899647",name:"fire",font_class:"fire",unicode:"e6a1",unicode_decimal:59041},{icon_id:"24899648",name:"paihangbang",font_class:"medal",unicode:"e6a2",unicode_decimal:59042},{icon_id:"24899649",name:"font",font_class:"font",unicode:"e6a3",unicode_decimal:59043},{icon_id:"24899650",name:"gift",font_class:"gift",unicode:"e6a4",unicode_decimal:59044},{icon_id:"24899651",name:"link",font_class:"link",unicode:"e6a5",unicode_decimal:59045},{icon_id:"24899652",name:"notification",font_class:"notification",unicode:"e6a6",unicode_decimal:59046},{icon_id:"24899653",name:"staff",font_class:"staff",unicode:"e6a7",unicode_decimal:59047},{icon_id:"24899654",name:"VIP",font_class:"vip",unicode:"e6a8",unicode_decimal:59048},{icon_id:"24899655",name:"folder_add",font_class:"folder-add",unicode:"e6a9",unicode_decimal:59049},{icon_id:"24899656",name:"tune",font_class:"tune",unicode:"e6aa",unicode_decimal:59050},{icon_id:"24899657",name:"shimingrenzheng",font_class:"auth",unicode:"e6ab",unicode_decimal:59051},{icon_id:"24899565",name:"person",font_class:"person",unicode:"e699",unicode_decimal:59033},{icon_id:"24899566",name:"email-filled",font_class:"email-filled",unicode:"e69a",unicode_decimal:59034},{icon_id:"24899567",name:"phone-filled",font_class:"phone-filled",unicode:"e69b",unicode_decimal:59035},{icon_id:"24899568",name:"phone",font_class:"phone",unicode:"e69c",unicode_decimal:59036},{icon_id:"24899570",name:"email",font_class:"email",unicode:"e69e",unicode_decimal:59038},{icon_id:"24899571",name:"personadd",font_class:"personadd",unicode:"e69f",unicode_decimal:59039},{icon_id:"24899558",name:"chatboxes-filled",font_class:"chatboxes-filled",unicode:"e692",unicode_decimal:59026},{icon_id:"24899559",name:"contact",font_class:"contact",unicode:"e693",unicode_decimal:59027},{icon_id:"24899560",name:"chatbubble-filled",font_class:"chatbubble-filled",unicode:"e694",unicode_decimal:59028},{icon_id:"24899561",name:"contact-filled",font_class:"contact-filled",unicode:"e695",unicode_decimal:59029},{icon_id:"24899562",name:"chatboxes",font_class:"chatboxes",unicode:"e696",unicode_decimal:59030},{icon_id:"24899563",name:"chatbubble",font_class:"chatbubble",unicode:"e697",unicode_decimal:59031},{icon_id:"24881290",name:"upload-filled",font_class:"upload-filled",unicode:"e68e",unicode_decimal:59022},{icon_id:"24881292",name:"upload",font_class:"upload",unicode:"e690",unicode_decimal:59024},{icon_id:"24881293",name:"weixin",font_class:"weixin",unicode:"e691",unicode_decimal:59025},{icon_id:"24881274",name:"compose",font_class:"compose",unicode:"e67f",unicode_decimal:59007},{icon_id:"24881275",name:"qq",font_class:"qq",unicode:"e680",unicode_decimal:59008},{icon_id:"24881276",name:"download-filled",font_class:"download-filled",unicode:"e681",unicode_decimal:59009},{icon_id:"24881277",name:"pengyouquan",font_class:"pyq",unicode:"e682",unicode_decimal:59010},{icon_id:"24881279",name:"sound",font_class:"sound",unicode:"e684",unicode_decimal:59012},{icon_id:"24881280",name:"trash-filled",font_class:"trash-filled",unicode:"e685",unicode_decimal:59013},{icon_id:"24881281",name:"sound-filled",font_class:"sound-filled",unicode:"e686",unicode_decimal:59014},{icon_id:"24881282",name:"trash",font_class:"trash",unicode:"e687",unicode_decimal:59015},{icon_id:"24881284",name:"videocam-filled",font_class:"videocam-filled",unicode:"e689",unicode_decimal:59017},{icon_id:"24881285",name:"spinner-cycle",font_class:"spinner-cycle",unicode:"e68a",unicode_decimal:59018},{icon_id:"24881286",name:"weibo",font_class:"weibo",unicode:"e68b",unicode_decimal:59019},{icon_id:"24881288",name:"videocam",font_class:"videocam",unicode:"e68c",unicode_decimal:59020},{icon_id:"24881289",name:"download",font_class:"download",unicode:"e68d",unicode_decimal:59021},{icon_id:"24879601",name:"help",font_class:"help",unicode:"e679",unicode_decimal:59001},{icon_id:"24879602",name:"navigate-filled",font_class:"navigate-filled",unicode:"e67a",unicode_decimal:59002},{icon_id:"24879603",name:"plusempty",font_class:"plusempty",unicode:"e67b",unicode_decimal:59003},{icon_id:"24879604",name:"smallcircle",font_class:"smallcircle",unicode:"e67c",unicode_decimal:59004},{icon_id:"24879605",name:"minus-filled",font_class:"minus-filled",unicode:"e67d",unicode_decimal:59005},{icon_id:"24879606",name:"micoff",font_class:"micoff",unicode:"e67e",unicode_decimal:59006},{icon_id:"24879588",name:"closeempty",font_class:"closeempty",unicode:"e66c",unicode_decimal:58988},{icon_id:"24879589",name:"clear",font_class:"clear",unicode:"e66d",unicode_decimal:58989},{icon_id:"24879590",name:"navigate",font_class:"navigate",unicode:"e66e",unicode_decimal:58990},{icon_id:"24879591",name:"minus",font_class:"minus",unicode:"e66f",unicode_decimal:58991},{icon_id:"24879592",name:"image",font_class:"image",unicode:"e670",unicode_decimal:58992},{icon_id:"24879593",name:"mic",font_class:"mic",unicode:"e671",unicode_decimal:58993},{icon_id:"24879594",name:"paperplane",font_class:"paperplane",unicode:"e672",unicode_decimal:58994},{icon_id:"24879595",name:"close",font_class:"close",unicode:"e673",unicode_decimal:58995},{icon_id:"24879596",name:"help-filled",font_class:"help-filled",unicode:"e674",unicode_decimal:58996},{icon_id:"24879597",name:"plus-filled",font_class:"paperplane-filled",unicode:"e675",unicode_decimal:58997},{icon_id:"24879598",name:"plus",font_class:"plus",unicode:"e676",unicode_decimal:58998},{icon_id:"24879599",name:"mic-filled",font_class:"mic-filled",unicode:"e677",unicode_decimal:58999},{icon_id:"24879600",name:"image-filled",font_class:"image-filled",unicode:"e678",unicode_decimal:59e3},{icon_id:"24855900",name:"locked-filled",font_class:"locked-filled",unicode:"e668",unicode_decimal:58984},{icon_id:"24855901",name:"info",font_class:"info",unicode:"e669",unicode_decimal:58985},{icon_id:"24855903",name:"locked",font_class:"locked",unicode:"e66b",unicode_decimal:58987},{icon_id:"24855884",name:"camera-filled",font_class:"camera-filled",unicode:"e658",unicode_decimal:58968},{icon_id:"24855885",name:"chat-filled",font_class:"chat-filled",unicode:"e659",unicode_decimal:58969},{icon_id:"24855886",name:"camera",font_class:"camera",unicode:"e65a",unicode_decimal:58970},{icon_id:"24855887",name:"circle",font_class:"circle",unicode:"e65b",unicode_decimal:58971},{icon_id:"24855888",name:"checkmarkempty",font_class:"checkmarkempty",unicode:"e65c",unicode_decimal:58972},{icon_id:"24855889",name:"chat",font_class:"chat",unicode:"e65d",unicode_decimal:58973},{icon_id:"24855890",name:"circle-filled",font_class:"circle-filled",unicode:"e65e",unicode_decimal:58974},{icon_id:"24855891",name:"flag",font_class:"flag",unicode:"e65f",unicode_decimal:58975},{icon_id:"24855892",name:"flag-filled",font_class:"flag-filled",unicode:"e660",unicode_decimal:58976},{icon_id:"24855893",name:"gear-filled",font_class:"gear-filled",unicode:"e661",unicode_decimal:58977},{icon_id:"24855894",name:"home",font_class:"home",unicode:"e662",unicode_decimal:58978},{icon_id:"24855895",name:"home-filled",font_class:"home-filled",unicode:"e663",unicode_decimal:58979},{icon_id:"24855896",name:"gear",font_class:"gear",unicode:"e664",unicode_decimal:58980},{icon_id:"24855897",name:"smallcircle-filled",font_class:"smallcircle-filled",unicode:"e665",unicode_decimal:58981},{icon_id:"24855898",name:"map-filled",font_class:"map-filled",unicode:"e666",unicode_decimal:58982},{icon_id:"24855899",name:"map",font_class:"map",unicode:"e667",unicode_decimal:58983},{icon_id:"24855825",name:"refresh-filled",font_class:"refresh-filled",unicode:"e656",unicode_decimal:58966},{icon_id:"24855826",name:"refresh",font_class:"refresh",unicode:"e657",unicode_decimal:58967},{icon_id:"24855808",name:"cloud-upload",font_class:"cloud-upload",unicode:"e645",unicode_decimal:58949},{icon_id:"24855809",name:"cloud-download-filled",font_class:"cloud-download-filled",unicode:"e646",unicode_decimal:58950},{icon_id:"24855810",name:"cloud-download",font_class:"cloud-download",unicode:"e647",unicode_decimal:58951},{icon_id:"24855811",name:"cloud-upload-filled",font_class:"cloud-upload-filled",unicode:"e648",unicode_decimal:58952},{icon_id:"24855813",name:"redo",font_class:"redo",unicode:"e64a",unicode_decimal:58954},{icon_id:"24855814",name:"images-filled",font_class:"images-filled",unicode:"e64b",unicode_decimal:58955},{icon_id:"24855815",name:"undo-filled",font_class:"undo-filled",unicode:"e64c",unicode_decimal:58956},{icon_id:"24855816",name:"more",font_class:"more",unicode:"e64d",unicode_decimal:58957},{icon_id:"24855817",name:"more-filled",font_class:"more-filled",unicode:"e64e",unicode_decimal:58958},{icon_id:"24855818",name:"undo",font_class:"undo",unicode:"e64f",unicode_decimal:58959},{icon_id:"24855819",name:"images",font_class:"images",unicode:"e650",unicode_decimal:58960},{icon_id:"24855821",name:"paperclip",font_class:"paperclip",unicode:"e652",unicode_decimal:58962},{icon_id:"24855822",name:"settings",font_class:"settings",unicode:"e653",unicode_decimal:58963},{icon_id:"24855823",name:"search",font_class:"search",unicode:"e654",unicode_decimal:58964},{icon_id:"24855824",name:"redo-filled",font_class:"redo-filled",unicode:"e655",unicode_decimal:58965},{icon_id:"24841702",name:"list",font_class:"list",unicode:"e644",unicode_decimal:58948},{icon_id:"24841489",name:"mail-open-filled",font_class:"mail-open-filled",unicode:"e63a",unicode_decimal:58938},{icon_id:"24841491",name:"hand-thumbsdown-filled",font_class:"hand-down-filled",unicode:"e63c",unicode_decimal:58940},{icon_id:"24841492",name:"hand-thumbsdown",font_class:"hand-down",unicode:"e63d",unicode_decimal:58941},{icon_id:"24841493",name:"hand-thumbsup-filled",font_class:"hand-up-filled",unicode:"e63e",unicode_decimal:58942},{icon_id:"24841494",name:"hand-thumbsup",font_class:"hand-up",unicode:"e63f",unicode_decimal:58943},{icon_id:"24841496",name:"heart-filled",font_class:"heart-filled",unicode:"e641",unicode_decimal:58945},{icon_id:"24841498",name:"mail-open",font_class:"mail-open",unicode:"e643",unicode_decimal:58947},{icon_id:"24841488",name:"heart",font_class:"heart",unicode:"e639",unicode_decimal:58937},{icon_id:"24839963",name:"loop",font_class:"loop",unicode:"e633",unicode_decimal:58931},{icon_id:"24839866",name:"pulldown",font_class:"pulldown",unicode:"e632",unicode_decimal:58930},{icon_id:"24813798",name:"scan",font_class:"scan",unicode:"e62a",unicode_decimal:58922},{icon_id:"24813786",name:"bars",font_class:"bars",unicode:"e627",unicode_decimal:58919},{icon_id:"24813788",name:"cart-filled",font_class:"cart-filled",unicode:"e629",unicode_decimal:58921},{icon_id:"24813790",name:"checkbox",font_class:"checkbox",unicode:"e62b",unicode_decimal:58923},{icon_id:"24813791",name:"checkbox-filled",font_class:"checkbox-filled",unicode:"e62c",unicode_decimal:58924},{icon_id:"24813794",name:"shop",font_class:"shop",unicode:"e62f",unicode_decimal:58927},{icon_id:"24813795",name:"headphones",font_class:"headphones",unicode:"e630",unicode_decimal:58928},{icon_id:"24813796",name:"cart",font_class:"cart",unicode:"e631",unicode_decimal:58929}]}},f6dd:function(e,t,n){"use strict";(function(e){var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=r(n("7ca3")),o=n("f12a"),a=r(n("8708"));function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){(0,i.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var u={data:function(){return{config:a.default,uniIdRedirectUrl:"",isMounted:!1}},onUnload:function(){},mounted:function(){this.isMounted=!0},onLoad:function(t){var n=this;if(t.is_weixin_redirect){if(e.showLoading({mask:!0}),window.location.href.includes("#")){var r=window.location.href.split("?")[1].split("&");r.forEach((function(e){var n=e.split("=");"code"==n[0]&&(t.code=n[1])}))}this.$nextTick((function(e){n.$refs.uniFabLogin.login({code:t.code},"weixin")}))}t.uniIdRedirectUrl&&(this.uniIdRedirectUrl=decodeURIComponent(t.uniIdRedirectUrl)),1===getCurrentPages().length&&(e.hideHomeButton(),console.log("已隐藏：返回首页按钮"))},computed:{needAgreements:function(){if(this.isMounted)return!!this.$refs.agreements&&this.$refs.agreements.needAgreements},agree:{get:function(){if(this.isMounted)return!this.$refs.agreements||this.$refs.agreements.isAgree},set:function(e){this.$refs.agreements?this.$refs.agreements.isAgree=e:console.log("不存在 隐私政策协议组件")}}},methods:{loginSuccess:function(e){o.mutations.loginSuccess(c(c({},e),{},{uniIdRedirectUrl:this.uniIdRedirectUrl}))}}},l=u;t.default=l}).call(this,n("df3c")["default"])}}]);
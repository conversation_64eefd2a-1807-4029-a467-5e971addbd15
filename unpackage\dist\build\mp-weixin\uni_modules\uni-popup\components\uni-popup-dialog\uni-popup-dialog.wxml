<view class="uni-popup-dialog"><view class="uni-dialog-title"><text class="{{['uni-dialog-title-text','uni-popup__'+dialogType]}}">{{titleText}}</text></view><block wx:if="{{mode==='base'}}"><view class="uni-dialog-content"><block wx:if="{{$slots.default}}"><slot></slot></block><block wx:else><text class="uni-dialog-content-text">{{content}}</text></block></view></block><block wx:else><view class="uni-dialog-content"><block wx:if="{{$slots.default}}"><slot></slot></block><block wx:else><input class="uni-dialog-input" type="{{inputType}}" placeholder="{{placeholderText}}" focus="{{focus}}" data-event-opts="{{[['input',[['__set_model',['','val','$event',[]]]]]]}}" value="{{val}}" bindinput="__e"/></block></view></block><view class="uni-dialog-button-group"><view data-event-opts="{{[['tap',[['closeDialog',['$event']]]]]}}" class="uni-dialog-button" bindtap="__e"><text class="uni-dialog-button-text">{{closeText}}</text></view><view data-event-opts="{{[['tap',[['onOk',['$event']]]]]}}" class="uni-dialog-button uni-border-left" bindtap="__e"><text class="uni-dialog-button-text uni-button-color">{{okText}}</text></view></view></view>
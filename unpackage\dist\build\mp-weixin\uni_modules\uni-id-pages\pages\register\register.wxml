<view class="uni-content"><match-media min-width="{{690}}"><view class="login-logo"><image src="{{logo}}"></image></view><text class="title title-box">用户名密码注册</text></match-media><uni-forms class="vue-ref" vue-id="451aeddd-1" value="{{formData}}" rules="{{rules}}" validate-trigger="submit" err-show-type="toast" data-ref="form" bind:__l="__l" vue-slots="{{['default']}}"><uni-forms-item vue-id="{{('451aeddd-2')+','+('451aeddd-1')}}" name="username" required="{{true}}" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput class="input-box" vue-id="{{('451aeddd-3')+','+('451aeddd-2')}}" inputBorder="{{false}}" focus="{{focusUsername}}" placeholder="请输入用户名" trim="both" value="{{formData.username}}" data-event-opts="{{[['^blur',[['e0']]],['^input',[['__set_model',['$0','username','$event',[]],['formData']]]]]}}" bind:blur="__e" bind:input="__e" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item vue-id="{{('451aeddd-4')+','+('451aeddd-1')}}" name="nickname" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput class="input-box" vue-id="{{('451aeddd-5')+','+('451aeddd-4')}}" inputBorder="{{false}}" focus="{{focusNickname}}" placeholder="请输入用户昵称" trim="both" value="{{formData.nickname}}" data-event-opts="{{[['^blur',[['e1']]],['^input',[['__set_model',['$0','nickname','$event',[]],['formData']]]]]}}" bind:blur="__e" bind:input="__e" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item bind:input="__e" vue-id="{{('451aeddd-6')+','+('451aeddd-1')}}" name="password" required="{{true}}" value="{{formData.password}}" data-event-opts="{{[['^input',[['__set_model',['$0','password','$event',[]],['formData']]]]]}}" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput class="input-box" vue-id="{{('451aeddd-7')+','+('451aeddd-6')}}" inputBorder="{{false}}" focus="{{focusPassword}}" maxlength="20" placeholder="{{'请输入'+(config.passwordStrength=='weak'?'6':'8')+'-16位密码'}}" type="password" trim="both" value="{{formData.password}}" data-event-opts="{{[['^blur',[['e2']]],['^input',[['__set_model',['$0','password','$event',[]],['formData']]]]]}}" bind:blur="__e" bind:input="__e" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item bind:input="__e" vue-id="{{('451aeddd-8')+','+('451aeddd-1')}}" name="password2" required="{{true}}" value="{{formData.password2}}" data-event-opts="{{[['^input',[['__set_model',['$0','password2','$event',[]],['formData']]]]]}}" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput class="input-box" vue-id="{{('451aeddd-9')+','+('451aeddd-8')}}" inputBorder="{{false}}" focus="{{focusPassword2}}" placeholder="再次输入密码" maxlength="20" type="password" trim="both" value="{{formData.password2}}" data-event-opts="{{[['^blur',[['e3']]],['^input',[['__set_model',['$0','password2','$event',[]],['formData']]]]]}}" bind:blur="__e" bind:input="__e" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item vue-id="{{('451aeddd-10')+','+('451aeddd-1')}}" bind:__l="__l" vue-slots="{{['default']}}"><uni-captcha bind:input="__e" class="vue-ref" vue-id="{{('451aeddd-11')+','+('451aeddd-10')}}" scene="register" data-ref="captcha" value="{{formData.captcha}}" data-event-opts="{{[['^input',[['__set_model',['$0','captcha','$event',[]],['formData']]]]]}}" bind:__l="__l"></uni-captcha></uni-forms-item><uni-id-pages-agreements class="vue-ref" vue-id="{{('451aeddd-12')+','+('451aeddd-1')}}" scope="register" data-ref="agreements" bind:__l="__l"></uni-id-pages-agreements><button class="uni-btn" type="primary" data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" bindtap="__e">注册</button><button data-event-opts="{{[['tap',[['navigateBack',['$event']]]]]}}" class="register-back" bindtap="__e">返回</button><match-media min-width="{{690}}"><view class="link-box"><text data-event-opts="{{[['tap',[['registerByEmail',['$event']]]]]}}" class="link" bindtap="__e">邮箱验证码注册</text><text data-event-opts="{{[['tap',[['toLogin',['$event']]]]]}}" class="link" bindtap="__e">已有账号？点此登录</text></view></match-media></uni-forms></view>
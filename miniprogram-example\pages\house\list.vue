<template>
	<view class="container">
		<!-- 搜索栏 -->
		<view class="search-header">
			<view class="search-box">
				<uni-icons type="search" size="16" color="#999"></uni-icons>
				<input class="search-input" v-model="searchKeyword" placeholder="搜索小区、地铁站、学校" 
					@confirm="onSearch" confirm-type="search" />
			</view>
			<view class="search-btn" @click="onSearch">搜索</view>
		</view>

		<!-- 筛选栏 -->
		<view class="filter-bar">
			<view class="filter-item" @click="showFilterPopup('price')">
				<text class="filter-text">{{ priceFilterText }}</text>
				<uni-icons type="arrowdown" size="12" color="#666"></uni-icons>
			</view>
			<view class="filter-item" @click="showFilterPopup('houseType')">
				<text class="filter-text">{{ houseTypeFilterText }}</text>
				<uni-icons type="arrowdown" size="12" color="#666"></uni-icons>
			</view>
			<view class="filter-item" @click="showFilterPopup('more')">
				<text class="filter-text">筛选</text>
				<uni-icons type="arrowdown" size="12" color="#666"></uni-icons>
			</view>
			<view class="filter-item" @click="showSortPopup">
				<text class="filter-text">{{ sortText }}</text>
				<uni-icons type="arrowdown" size="12" color="#666"></uni-icons>
			</view>
		</view>

		<!-- 房源列表 -->
		<view class="house-list">
			<view class="house-item" v-for="(house, index) in houseList" :key="house._id" @click="goToDetail(house)">
				<view class="house-image-container">
					<image class="house-image" :src="house.images[0]?.url || '/static/default-house.jpg'" mode="aspectFill"></image>
					<view class="house-tag" v-if="house.rent_type">{{ house.rent_type }}</view>
					<view class="favorite-btn" @click.stop="toggleFavorite(house, index)">
						<uni-icons :type="house.is_favorite ? 'heart-filled' : 'heart'" 
							:color="house.is_favorite ? '#ff4757' : '#fff'" size="20"></uni-icons>
					</view>
				</view>
				<view class="house-info">
					<view class="house-title">{{ house.title }}</view>
					<view class="house-desc">{{ house.house_type }} · {{ house.area }}㎡ · {{ house.orientation }}</view>
					<view class="house-address">{{ house.address.district }} {{ house.address.street }}</view>
					<view class="house-facilities">
						<text class="facility-tag" v-for="(facility, idx) in house.facilities.slice(0, 3)" :key="idx">
							{{ facility }}
						</text>
						<text class="facility-more" v-if="house.facilities.length > 3">+{{ house.facilities.length - 3 }}</text>
					</view>
					<view class="house-bottom">
						<view class="house-price">
							<text class="price-symbol">¥</text>
							<text class="price-number">{{ house.rent_price }}</text>
							<text class="price-unit">/月</text>
						</view>
						<view class="house-actions">
							<button class="action-btn appointment-btn" @click.stop="makeAppointment(house)">预约看房</button>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 加载状态 -->
		<view class="load-more" v-if="hasMore || loading">
			<uni-load-more :status="loadStatus"></uni-load-more>
		</view>

		<!-- 空状态 -->
		<view class="empty-state" v-if="!loading && houseList.length === 0">
			<image class="empty-image" src="/static/empty-house.png" mode="aspectFit"></image>
			<text class="empty-text">暂无房源信息</text>
		</view>

		<!-- 筛选弹窗 -->
		<uni-popup ref="filterPopup" type="bottom">
			<view class="filter-popup">
				<!-- 价格筛选 -->
				<view class="filter-content" v-if="currentFilterType === 'price'">
					<view class="filter-title">租金范围</view>
					<view class="price-options">
						<view class="price-option" v-for="(option, index) in priceOptions" :key="index"
							:class="{ active: selectedPrice === option.value }" @click="selectPrice(option)">
							{{ option.label }}
						</view>
					</view>
				</view>

				<!-- 房型筛选 -->
				<view class="filter-content" v-if="currentFilterType === 'houseType'">
					<view class="filter-title">房屋类型</view>
					<view class="type-options">
						<view class="type-option" v-for="(option, index) in houseTypeOptions" :key="index"
							:class="{ active: selectedHouseType === option.value }" @click="selectHouseType(option)">
							{{ option.label }}
						</view>
					</view>
				</view>

				<!-- 更多筛选 -->
				<view class="filter-content" v-if="currentFilterType === 'more'">
					<view class="filter-section">
						<view class="filter-title">出租方式</view>
						<view class="option-grid">
							<view class="grid-option" v-for="(option, index) in rentTypeOptions" :key="index"
								:class="{ active: selectedRentType === option.value }" @click="selectRentType(option)">
								{{ option.label }}
							</view>
						</view>
					</view>
					
					<view class="filter-section">
						<view class="filter-title">朝向</view>
						<view class="option-grid">
							<view class="grid-option" v-for="(option, index) in orientationOptions" :key="index"
								:class="{ active: selectedOrientation === option.value }" @click="selectOrientation(option)">
								{{ option.label }}
							</view>
						</view>
					</view>
				</view>

				<view class="filter-actions">
					<button class="reset-btn" @click="resetFilter">重置</button>
					<button class="confirm-btn" @click="confirmFilter">确定</button>
				</view>
			</view>
		</uni-popup>

		<!-- 排序弹窗 -->
		<uni-popup ref="sortPopup" type="bottom">
			<view class="sort-popup">
				<view class="sort-title">排序方式</view>
				<view class="sort-options">
					<view class="sort-option" v-for="(option, index) in sortOptions" :key="index"
						:class="{ active: selectedSort === option.value }" @click="selectSort(option)">
						{{ option.label }}
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				searchKeyword: '',
				houseList: [],
				loading: false,
				hasMore: true,
				loadStatus: 'more',
				pageNum: 1,
				pageSize: 10,
				
				// 筛选相关
				currentFilterType: '',
				selectedPrice: '',
				selectedHouseType: '',
				selectedRentType: '',
				selectedOrientation: '',
				selectedSort: 'create_date desc',
				
				// 筛选选项
				priceOptions: [
					{ label: '不限', value: '' },
					{ label: '1000以下', value: '0-1000' },
					{ label: '1000-2000', value: '1000-2000' },
					{ label: '2000-3000', value: '2000-3000' },
					{ label: '3000-5000', value: '3000-5000' },
					{ label: '5000-8000', value: '5000-8000' },
					{ label: '8000以上', value: '8000-999999' }
				],
				houseTypeOptions: [
					{ label: '不限', value: '' },
					{ label: '1室1厅', value: '1室1厅' },
					{ label: '2室1厅', value: '2室1厅' },
					{ label: '2室2厅', value: '2室2厅' },
					{ label: '3室1厅', value: '3室1厅' },
					{ label: '3室2厅', value: '3室2厅' },
					{ label: '4室2厅', value: '4室2厅' }
				],
				rentTypeOptions: [
					{ label: '不限', value: '' },
					{ label: '整租', value: '整租' },
					{ label: '合租', value: '合租' },
					{ label: '短租', value: '短租' }
				],
				orientationOptions: [
					{ label: '不限', value: '' },
					{ label: '南', value: '南' },
					{ label: '北', value: '北' },
					{ label: '东', value: '东' },
					{ label: '西', value: '西' },
					{ label: '南北', value: '南北' },
					{ label: '东南', value: '东南' },
					{ label: '西南', value: '西南' }
				],
				sortOptions: [
					{ label: '默认排序', value: 'create_date desc' },
					{ label: '价格从低到高', value: 'rent_price asc' },
					{ label: '价格从高到低', value: 'rent_price desc' },
					{ label: '面积从小到大', value: 'area asc' },
					{ label: '面积从大到小', value: 'area desc' }
				]
			}
		},
		computed: {
			priceFilterText() {
				const option = this.priceOptions.find(item => item.value === this.selectedPrice)
				return option ? option.label : '价格'
			},
			houseTypeFilterText() {
				const option = this.houseTypeOptions.find(item => item.value === this.selectedHouseType)
				return option ? option.label : '房型'
			},
			sortText() {
				const option = this.sortOptions.find(item => item.value === this.selectedSort)
				return option ? option.label : '排序'
			}
		},
		onLoad(options) {
			// 接收页面参数
			if (options.keyword) {
				this.searchKeyword = decodeURIComponent(options.keyword)
			}
			if (options.rent_type) {
				this.selectedRentType = options.rent_type
			}
			if (options.house_type) {
				this.selectedHouseType = options.house_type
			}
			
			this.loadHouseList()
		},
		onReachBottom() {
			if (this.hasMore && !this.loading) {
				this.loadMoreHouses()
			}
		},
		onPullDownRefresh() {
			this.refreshData()
		},
		methods: {
			// 搜索
			onSearch() {
				this.refreshData()
			},

			// 显示筛选弹窗
			showFilterPopup(type) {
				this.currentFilterType = type
				this.$refs.filterPopup.open()
			},

			// 显示排序弹窗
			showSortPopup() {
				this.$refs.sortPopup.open()
			},

			// 选择价格
			selectPrice(option) {
				this.selectedPrice = option.value
				this.$refs.filterPopup.close()
				this.refreshData()
			},

			// 选择房型
			selectHouseType(option) {
				this.selectedHouseType = option.value
				this.$refs.filterPopup.close()
				this.refreshData()
			},

			// 选择出租方式
			selectRentType(option) {
				this.selectedRentType = option.value
			},

			// 选择朝向
			selectOrientation(option) {
				this.selectedOrientation = option.value
			},

			// 选择排序
			selectSort(option) {
				this.selectedSort = option.value
				this.$refs.sortPopup.close()
				this.refreshData()
			},

			// 重置筛选
			resetFilter() {
				this.selectedPrice = ''
				this.selectedHouseType = ''
				this.selectedRentType = ''
				this.selectedOrientation = ''
			},

			// 确认筛选
			confirmFilter() {
				this.$refs.filterPopup.close()
				this.refreshData()
			},

			// 跳转详情页
			goToDetail(house) {
				uni.navigateTo({
					url: `/pages/house/detail?id=${house._id}`
				})
			},

			// 切换收藏
			async toggleFavorite(house, index) {
				try {
					const result = await uniCloud.callFunction({
						name: 'rental-service',
						data: {
							action: 'toggleFavorite',
							houseId: house._id,
							isFavorite: !house.is_favorite
						}
					})

					if (result.result.code === 0) {
						this.houseList[index].is_favorite = !house.is_favorite
						uni.showToast({
							title: house.is_favorite ? '取消收藏成功' : '收藏成功',
							icon: 'success'
						})
					} else {
						uni.showToast({
							title: result.result.message,
							icon: 'none'
						})
					}
				} catch (error) {
					uni.showToast({
						title: '操作失败',
						icon: 'none'
					})
				}
			},

			// 预约看房
			makeAppointment(house) {
				uni.navigateTo({
					url: `/pages/appointment/index?houseId=${house._id}`
				})
			},

			// 加载房源列表
			async loadHouseList(refresh = false) {
				if (this.loading) return
				
				this.loading = true
				if (refresh) {
					this.loadStatus = 'loading'
				}

				try {
					// 构建筛选参数
					const params = {
						action: 'getHouseList',
						keyword: this.searchKeyword,
						pageSize: this.pageSize,
						pageNum: this.pageNum,
						orderBy: this.selectedSort
					}

					// 价格筛选
					if (this.selectedPrice) {
						const [minPrice, maxPrice] = this.selectedPrice.split('-').map(Number)
						params.minPrice = minPrice
						params.maxPrice = maxPrice
					}

					// 房型筛选
					if (this.selectedHouseType) {
						params.houseType = this.selectedHouseType
					}

					// 出租方式筛选
					if (this.selectedRentType) {
						params.rentType = this.selectedRentType
					}

					// 朝向筛选
					if (this.selectedOrientation) {
						params.orientation = this.selectedOrientation
					}

					const result = await uniCloud.callFunction({
						name: 'rental-service',
						data: params
					})

					if (result.result.code === 0) {
						const houses = result.result.data.list
						
						if (refresh || this.pageNum === 1) {
							this.houseList = houses
						} else {
							this.houseList = [...this.houseList, ...houses]
						}

						this.hasMore = houses.length === this.pageSize
						this.loadStatus = this.hasMore ? 'more' : 'noMore'
					} else {
						uni.showToast({
							title: result.result.message,
							icon: 'none'
						})
						this.loadStatus = 'more'
					}
				} catch (error) {
					uni.showToast({
						title: '加载失败',
						icon: 'none'
					})
					this.loadStatus = 'more'
				}

				this.loading = false
				if (refresh) {
					uni.stopPullDownRefresh()
				}
			},

			// 加载更多
			loadMoreHouses() {
				this.pageNum++
				this.loadStatus = 'loading'
				this.loadHouseList()
			},

			// 刷新数据
			refreshData() {
				this.pageNum = 1
				this.hasMore = true
				this.loadHouseList(true)
			}
		}
	}
</script>

<style lang="scss" scoped>
	// 样式代码省略，与之前的首页样式类似
	// 包含搜索栏、筛选栏、房源列表、弹窗等样式
</style>

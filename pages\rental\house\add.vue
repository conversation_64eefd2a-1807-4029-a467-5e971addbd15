<template>
	<view class="fix-top-window">
		<view class="uni-header">
			<uni-stat-breadcrumb class="uni-stat-breadcrumb-on-phone" />
			<view class="uni-group">
				<view class="uni-title">
					{{ isEdit ? '编辑房源' : '新增房源' }}
				</view>
			</view>
		</view>
		<view class="uni-container">
			<uni-forms ref="form" :model="formData" validateTrigger="bind" err-show-type="toast">
				<uni-forms-item label="房源标题" required name="title">
					<uni-easyinput v-model="formData.title" placeholder="请输入房源标题" maxlength="100"></uni-easyinput>
				</uni-forms-item>
				
				<uni-forms-item label="房源描述" name="description">
					<uni-easyinput type="textarea" v-model="formData.description" placeholder="请输入房源详细描述" 
						maxlength="2000" :auto-height="true"></uni-easyinput>
				</uni-forms-item>
				
				<uni-forms-item label="租金(元/月)" required name="rent_price">
					<uni-easyinput type="number" v-model="formData.rent_price" placeholder="请输入月租金"></uni-easyinput>
				</uni-forms-item>
				
				<uni-forms-item label="押金(元)" name="deposit">
					<uni-easyinput type="number" v-model="formData.deposit" placeholder="请输入押金金额"></uni-easyinput>
				</uni-forms-item>
				
				<uni-forms-item label="房屋类型" required name="house_type">
					<uni-data-select v-model="formData.house_type" :localdata="houseTypeOptions" 
						placeholder="请选择房屋类型"></uni-data-select>
				</uni-forms-item>
				
				<uni-forms-item label="面积(㎡)" required name="area">
					<uni-easyinput type="number" v-model="formData.area" placeholder="请输入房屋面积"></uni-easyinput>
				</uni-forms-item>
				
				<uni-forms-item label="楼层" name="floor">
					<uni-easyinput v-model="formData.floor" placeholder="如：3/6（3楼，共6楼）"></uni-easyinput>
				</uni-forms-item>
				
				<uni-forms-item label="朝向" name="orientation">
					<uni-data-select v-model="formData.orientation" :localdata="orientationOptions" 
						placeholder="请选择房屋朝向"></uni-data-select>
				</uni-forms-item>
				
				<uni-forms-item label="装修情况" name="decoration">
					<uni-data-select v-model="formData.decoration" :localdata="decorationOptions" 
						placeholder="请选择装修情况"></uni-data-select>
				</uni-forms-item>
				
				<uni-forms-item label="省份" required name="address.province">
					<uni-easyinput v-model="formData.address.province" placeholder="请输入省份"></uni-easyinput>
				</uni-forms-item>
				
				<uni-forms-item label="城市" required name="address.city">
					<uni-easyinput v-model="formData.address.city" placeholder="请输入城市"></uni-easyinput>
				</uni-forms-item>
				
				<uni-forms-item label="区县" required name="address.district">
					<uni-easyinput v-model="formData.address.district" placeholder="请输入区县"></uni-easyinput>
				</uni-forms-item>
				
				<uni-forms-item label="详细地址" required name="address.street">
					<uni-easyinput v-model="formData.address.street" placeholder="请输入详细地址"></uni-easyinput>
				</uni-forms-item>
				
				<uni-forms-item label="配套设施" name="facilities">
					<uni-data-checkbox v-model="formData.facilities" :localdata="facilitiesOptions" 
						multiple wrap></uni-data-checkbox>
				</uni-forms-item>
				
				<uni-forms-item label="房源图片" name="images">
					<uni-file-picker v-model="formData.images" fileMediatype="image" mode="grid" 
						:limit="9" @select="selectFile" @delete="deleteFile"></uni-file-picker>
				</uni-forms-item>
				
				<uni-forms-item label="联系人姓名" required name="contact_info.name">
					<uni-easyinput v-model="formData.contact_info.name" placeholder="请输入联系人姓名"></uni-easyinput>
				</uni-forms-item>
				
				<uni-forms-item label="联系电话" required name="contact_info.phone">
					<uni-easyinput v-model="formData.contact_info.phone" placeholder="请输入联系电话"></uni-easyinput>
				</uni-forms-item>
				
				<uni-forms-item label="微信号" name="contact_info.wechat">
					<uni-easyinput v-model="formData.contact_info.wechat" placeholder="请输入微信号"></uni-easyinput>
				</uni-forms-item>
				
				<uni-forms-item label="出租方式" name="rent_type">
					<uni-data-select v-model="formData.rent_type" :localdata="rentTypeOptions" 
						placeholder="请选择出租方式"></uni-data-select>
				</uni-forms-item>
				
				<uni-forms-item label="可入住时间" name="available_date">
					<uni-datetime-picker v-model="formData.available_date" type="date" 
						placeholder="请选择可入住时间"></uni-datetime-picker>
				</uni-forms-item>
				
				<view class="uni-button-group">
					<button class="uni-button" type="primary" @click="submit">{{ isEdit ? '更新' : '提交' }}</button>
					<button class="uni-button" @click="navigateBack">取消</button>
				</view>
			</uni-forms>
		</view>
	</view>
</template>

<script>
	const db = uniCloud.database()
	const dbCollectionName = 'rental-houses'

	export default {
		data() {
			return {
				formData: {
					title: '',
					description: '',
					rent_price: '',
					deposit: '',
					house_type: '',
					area: '',
					floor: '',
					orientation: '',
					decoration: '',
					address: {
						province: '',
						city: '',
						district: '',
						street: '',
						longitude: '',
						latitude: ''
					},
					facilities: [],
					images: [],
					contact_info: {
						name: '',
						phone: '',
						wechat: ''
					},
					rent_type: '整租',
					available_date: '',
					status: '待审核'
				},
				isEdit: false,
				formDataId: '',
				houseTypeOptions: [
					{value: '1室1厅', text: '1室1厅'},
					{value: '2室1厅', text: '2室1厅'},
					{value: '2室2厅', text: '2室2厅'},
					{value: '3室1厅', text: '3室1厅'},
					{value: '3室2厅', text: '3室2厅'},
					{value: '4室2厅', text: '4室2厅'},
					{value: '单间', text: '单间'},
					{value: '合租', text: '合租'},
					{value: '整租', text: '整租'}
				],
				orientationOptions: [
					{value: '东', text: '东'},
					{value: '南', text: '南'},
					{value: '西', text: '西'},
					{value: '北', text: '北'},
					{value: '东南', text: '东南'},
					{value: '东北', text: '东北'},
					{value: '西南', text: '西南'},
					{value: '西北', text: '西北'},
					{value: '南北', text: '南北'}
				],
				decorationOptions: [
					{value: '毛坯', text: '毛坯'},
					{value: '简装', text: '简装'},
					{value: '精装', text: '精装'},
					{value: '豪装', text: '豪装'}
				],
				facilitiesOptions: [
					{value: '空调', text: '空调'},
					{value: '洗衣机', text: '洗衣机'},
					{value: '冰箱', text: '冰箱'},
					{value: '热水器', text: '热水器'},
					{value: '电视', text: '电视'},
					{value: '宽带', text: '宽带'},
					{value: '衣柜', text: '衣柜'},
					{value: '床', text: '床'},
					{value: '沙发', text: '沙发'},
					{value: '餐桌', text: '餐桌'},
					{value: '微波炉', text: '微波炉'},
					{value: '电梯', text: '电梯'},
					{value: '停车位', text: '停车位'},
					{value: '阳台', text: '阳台'},
					{value: '独立卫生间', text: '独立卫生间'}
				],
				rentTypeOptions: [
					{value: '整租', text: '整租'},
					{value: '合租', text: '合租'},
					{value: '短租', text: '短租'}
				]
			}
		},
		onLoad(option) {
			if (option.id) {
				this.isEdit = true
				this.formDataId = option.id
				this.loadData()
			}
		},
		methods: {
			loadData() {
				db.collection(dbCollectionName).doc(this.formDataId).get().then(res => {
					const data = res.result.data[0]
					if (data) {
						this.formData = {
							...this.formData,
							...data
						}
					}
				}).catch(err => {
					uni.showModal({
						content: err.message || '获取数据失败',
						showCancel: false
					})
				})
			},
			selectFile(e) {
				console.log('选择文件：', e)
			},
			deleteFile(e) {
				console.log('删除文件：', e)
			},
			submit() {
				this.$refs.form.validate().then(res => {
					this.submitForm(res)
				}).catch(err => {
					console.log('表单验证失败：', err)
				})
			},
			submitForm(value) {
				uni.showLoading({
					mask: true
				})
				
				// 设置房东ID为当前用户
				if (!this.isEdit) {
					value.landlord_id = this.$uniIdPagesStore.userInfo._id
				}
				
				const action = this.isEdit ? 
					db.collection(dbCollectionName).doc(this.formDataId).update(value) :
					db.collection(dbCollectionName).add(value)
				
				action.then(res => {
					uni.showToast({
						title: `${this.isEdit ? '更新' : '新增'}成功`
					})
					this.getOpenerEventChannel().emit('refreshData')
					setTimeout(() => uni.navigateBack(), 500)
				}).catch(err => {
					uni.showModal({
						content: err.message || '请求服务失败',
						showCancel: false
					})
				}).finally(() => {
					uni.hideLoading()
				})
			},
			navigateBack() {
				uni.navigateBack()
			}
		}
	}
</script>

<style lang="scss">
	.uni-button-group {
		margin-top: 30px;
		display: flex;
		justify-content: center;
		
		.uni-button {
			margin: 0 10px;
		}
	}
</style>

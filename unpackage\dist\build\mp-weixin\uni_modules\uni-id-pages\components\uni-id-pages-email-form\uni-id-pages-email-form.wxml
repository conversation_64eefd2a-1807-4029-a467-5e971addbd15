<view class="data-v-1f06c581"><uni-captcha bind:input="__e" vue-id="82fd2d86-1" focus="{{focusCaptchaInput}}" scene="send-email-code" data-ref="captcha" value="{{captcha}}" data-event-opts="{{[['^input',[['__set_model',['','captcha','$event',[]]]]]]}}" class="data-v-1f06c581 vue-ref" bind:__l="__l"></uni-captcha><view class="box data-v-1f06c581"><uni-easyinput class="input-box data-v-1f06c581" vue-id="82fd2d86-2" focus="{{focusEmailCodeInput}}" type="number" inputBorder="{{false}}" maxlength="6" placeholder="请输入邮箱验证码" value="{{modelValue}}" data-event-opts="{{[['^blur',[['e0']]],['^input',[['__set_model',['','modelValue','$event',[]]]]]]}}" bind:blur="__e" bind:input="__e" bind:__l="__l"></uni-easyinput><view class="short-code-btn data-v-1f06c581" hover-class="hover" data-event-opts="{{[['tap',[['start',['$event']]]]]}}" bindtap="__e"><text class="{{['inner-text','data-v-1f06c581',reverseNumber==0?'inner-text-active':'']}}">{{innerText}}</text></view></view></view>
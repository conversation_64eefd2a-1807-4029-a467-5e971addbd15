{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/register/register-by-email.vue?1afa", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/register/register-by-email.vue?6c3c", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/register/register-by-email.vue?7922", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/register/register-by-email.vue?a835", "uni-app:///uni_modules/uni-id-pages/pages/register/register-by-email.vue", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/register/register-by-email.vue?e3a8", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/register/register-by-email.vue?a29d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "mixins", "data", "formData", "email", "nickname", "password", "password2", "code", "rules", "required", "errorMessage", "format", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "validateFunction", "callback", "label", "passwordMod", "pattern", "focusEmail", "focusNickname", "focusPassword", "focusPassword2", "logo", "onReady", "onShow", "methods", "submit", "key", "submitForm", "uniIdCo", "uni", "url", "complete", "catch", "console", "navigateBack", "<PERSON><PERSON><PERSON><PERSON>", "registerByUserName"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,wBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACqE;AACL;AACc;;;AAG9E;AAC+K;AAC/K,gBAAgB,gLAAU;AAC1B,EAAE,uFAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,8RAEN;AACP,KAAK;AACL;AACA,aAAa,oSAEN;AACP,KAAK;AACL;AACA,aAAa,6VAEN;AACP,KAAK;AACL;AACA,aAAa,6VAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnEA;AAAA;AAAA;AAAA;AAA0qB,CAAgB,0oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;AC+C9rB;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA,eACA;EACAC;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAL;UACAK;YACAC;YACAC;UACA;YACAC;YACAD;UACA;QAEA;QACAN;UACAI;YACAI;YACAC;YACAH;UACA,GACA;YACAI;cACA;cACA;gBACAC;cACA;cAAA;cACA;gBACAA;cACA;cAAA;cACA;gBACAA;cACA;cACA;YACA;UACA,EACA;UACAC;QACA;MAAA,GACAC;QACAV;UACAC;YACAC;YACAC;UACA,GACA;YACAQ;YACAR;UACA;QAEA;MAAA,EACA;MACAS;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC,2BASA;EACAC;IACA;AACA;AACA;IACAC;MAAA;MACA;QACA;UACA;YACA;UACA;QACA;QACA;MACA;QACA;QACAC;QACA;QACA;MACA;IACA;IACAC;MACAC;QACA;QACAC;UACAC;UACAC;YACA;UAAA;QAEA;MACA,GACAC;QACA;QACAC;MACA;IACA;IACAC;MACAL;IACA;IACAM;MACAN;QACAC;MACA;IACA;IACAM;MACAP;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnLA;AAAA;AAAA;AAAA;AAAqwC,CAAgB,qoCAAG,EAAC,C;;;;;;;;;;;ACAzxC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-id-pages/pages/register/register-by-email.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './uni_modules/uni-id-pages/pages/register/register-by-email.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./register-by-email.vue?vue&type=template&id=6e50df4c&\"\nvar renderjs\nimport script from \"./register-by-email.vue?vue&type=script&lang=js&\"\nexport * from \"./register-by-email.vue?vue&type=script&lang=js&\"\nimport style0 from \"./register-by-email.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-id-pages/pages/register/register-by-email.vue\"\nexport default component.exports", "export * from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register-by-email.vue?vue&type=template&id=6e50df4c&\"", "var components\ntry {\n  components = {\n    uniForms: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-forms/components/uni-forms/uni-forms\" */ \"@/uni_modules/uni-forms/components/uni-forms/uni-forms.vue\"\n      )\n    },\n    uniFormsItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-forms/components/uni-forms-item/uni-forms-item\" */ \"@/uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.vue\"\n      )\n    },\n    uniEasyinput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput\" */ \"@/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue\"\n      )\n    },\n    uniIdPagesEmailForm: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-id-pages/components/uni-id-pages-email-form/uni-id-pages-email-form\" */ \"@/uni_modules/uni-id-pages/components/uni-id-pages-email-form/uni-id-pages-email-form.vue\"\n      )\n    },\n    uniIdPagesAgreements: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-id-pages/components/uni-id-pages-agreements/uni-id-pages-agreements\" */ \"@/uni_modules/uni-id-pages/components/uni-id-pages-agreements/uni-id-pages-agreements.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.focusEmail = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.focusNickname = false\n    }\n    _vm.e2 = function ($event) {\n      _vm.focusPassword = false\n    }\n    _vm.e3 = function ($event) {\n      _vm.focusPassword2 = false\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register-by-email.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register-by-email.vue?vue&type=script&lang=js&\"", "<!-- 邮箱验证码注册 -->\r\n<template>\r\n\t<view class=\"uni-content\">\r\n\t\t<match-media :min-width=\"690\">\r\n\t\t\t<view class=\"login-logo\">\r\n\t\t\t\t<image :src=\"logo\"></image>\r\n\t\t\t</view>\r\n\t\t\t<!-- 顶部文字 -->\r\n\t\t\t<text class=\"title title-box\">邮箱验证码注册</text>\r\n\t\t</match-media>\r\n\t\t<uni-forms ref=\"form\" :value=\"formData\" :rules=\"rules\" validate-trigger=\"submit\" err-show-type=\"toast\">\r\n\t\t\t<uni-forms-item name=\"email\" required>\r\n\t\t\t\t<uni-easyinput :inputBorder=\"false\" :focus=\"focusEmail\" @blur=\"focusEmail = false\"\r\n\t\t\t\t\tclass=\"input-box\" placeholder=\"请输入邮箱\" v-model=\"formData.email\" trim=\"both\" />\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-forms-item name=\"nickname\">\r\n\t\t\t\t<uni-easyinput :inputBorder=\"false\" :focus=\"focusNickname\" @blur=\"focusNickname = false\" class=\"input-box\" placeholder=\"请输入用户昵称\" \r\n\t\t\t\tv-model=\"formData.nickname\" trim=\"both\" />\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-forms-item name=\"password\" v-model=\"formData.password\" required>\r\n\t\t\t\t<uni-easyinput :inputBorder=\"false\" :focus=\"focusPassword\" @blur=\"focusPassword = false\"\r\n\t\t\t\t\tclass=\"input-box\" maxlength=\"20\" :placeholder=\"'请输入' + (config.passwordStrength == 'weak'?'6':'8') + '-16位密码'\" type=\"password\"\r\n\t\t\t\t\tv-model=\"formData.password\" trim=\"both\" />\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-forms-item name=\"password2\" v-model=\"formData.password2\" required>\r\n\t\t\t\t<uni-easyinput :inputBorder=\"false\" :focus=\"focusPassword2\" @blur=\"focusPassword2 =false\"\r\n\t\t\t\t\tclass=\"input-box\" placeholder=\"再次输入密码\" maxlength=\"20\" type=\"password\" v-model=\"formData.password2\"\r\n\t\t\t\t\ttrim=\"both\" />\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-forms-item name=\"code\" >\r\n\t\t\t\t<uni-id-pages-email-form ref=\"shortCode\" :email=\"formData.email\" type=\"register\" v-model=\"formData.code\">\r\n\t\t\t\t</uni-id-pages-email-form>\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-id-pages-agreements scope=\"register\" ref=\"agreements\" ></uni-id-pages-agreements>\r\n\t\t\t<button class=\"uni-btn\" type=\"primary\" @click=\"submit\">注册</button>\r\n\t\t\t<button @click=\"navigateBack\" class=\"register-back\">返回</button>\r\n\t\t\t<match-media :min-width=\"690\">\r\n\t\t\t\t<view class=\"link-box\">\r\n\t\t\t\t\t<text class=\"link\" @click=\"registerByUserName\">用户名密码注册</text>\r\n\t\t\t\t\t<text class=\"link\" @click=\"toLogin\">已有账号？点此登录</text>\r\n\t\t\t\t</view>\r\n\t\t\t</match-media>\r\n\t\t</uni-forms>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport rules from './validator.js';\r\n\timport mixin from '@/uni_modules/uni-id-pages/common/login-page.mixin.js';\r\n\timport config from '@/uni_modules/uni-id-pages/config.js'\r\n\timport passwordMod from '@/uni_modules/uni-id-pages/common/password.js'\r\n\tconst uniIdCo = uniCloud.importObject(\"uni-id-co\")\r\n\texport default {\r\n\t\tmixins: [mixin],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tformData: {\r\n\t\t\t\t\temail: \"\",\r\n\t\t\t\t\tnickname: \"\",\r\n\t\t\t\t\tpassword: \"\",\r\n\t\t\t\t\tpassword2: \"\",\r\n\t\t\t\t\tcode: \"\"\r\n\t\t\t\t},\r\n\t\t\t\trules: {\r\n\t\t\t\t\temail: {\r\n\t\t\t\t\t\trules: [{\r\n\t\t\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\t\t\terrorMessage: '请输入邮箱',\r\n\t\t\t\t\t\t\t},{\r\n\t\t\t\t\t\t\t\tformat:'email',\r\n\t\t\t\t\t\t\t\terrorMessage: '邮箱格式不正确',\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t},\r\n\t\t\t\t\tnickname: {\r\n\t\t\t\t\t\trules: [{\r\n\t\t\t\t\t\t\t\tminLength: 3,\r\n\t\t\t\t\t\t\t\tmaxLength: 32,\r\n\t\t\t\t\t\t\t\terrorMessage: '昵称长度在 {minLength} 到 {maxLength} 个字符',\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tvalidateFunction: function(rule, value, data, callback) {\r\n\t\t\t\t\t\t\t\t\t// console.log(value);\r\n\t\t\t\t\t\t\t\t\tif (/^1\\d{10}$/.test(value) || /^(\\w-*\\.*)+@(\\w-?)+(\\.\\w{2,})+$/.test(value)) {\r\n\t\t\t\t\t\t\t\t\t\tcallback('昵称不能是：手机号或邮箱')\r\n\t\t\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t\t\t\tif (/^\\d+$/.test(value)) {\r\n\t\t\t\t\t\t\t\t\t\tcallback('昵称不能为纯数字')\r\n\t\t\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t\t\t\tif(/[\\u4E00-\\u9FA5\\uF900-\\uFA2D]{1,}/.test(value)){\r\n\t\t\t\t\t\t\t\t\t\tcallback('昵称不能包含中文')\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\treturn true\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t],\r\n\t\t\t\t\t\tlabel: \"昵称\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\t...passwordMod.getPwdRules(),\r\n\t\t\t\t\tcode: {\r\n\t\t\t\t\t\trules: [{\r\n\t\t\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\t\t\terrorMessage: '请输入邮箱验证码',\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tpattern: /^.{6}$/,\r\n\t\t\t\t\t\t\t\terrorMessage: '邮箱验证码不正确',\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tfocusEmail:false,\r\n\t\t\t\tfocusNickname:false,\r\n\t\t\t\tfocusPassword:false,\r\n\t\t\t\tfocusPassword2:false,\r\n\t\t\t\tlogo: \"/static/logo.png\"\r\n\t\t\t}\r\n\t\t},\r\n\t\tonReady() {\r\n\t\t\tthis.$refs.form.setRules(this.rules)\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\t// #ifdef H5\r\n\t\t\tdocument.onkeydown = event => {\r\n\t\t\t\tvar e = event || window.event;\r\n\t\t\t\tif (e && e.keyCode == 13) { //回车键的键值为13\r\n\t\t\t\t\tthis.submit()\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t/**\r\n\t\t\t * 触发表单提交\r\n\t\t\t */\r\n\t\t\tsubmit() {\r\n\t\t\t\tthis.$refs.form.validate().then((res) => {\r\n\t\t\t\t\tif (this.needAgreements && !this.agree) {\r\n\t\t\t\t\t\treturn this.$refs.agreements.popup(()=>{\r\n\t\t\t\t\t\t\tthis.submitForm(res)\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.submitForm(res)\r\n\t\t\t\t}).catch((errors) => {\r\n\t\t\t\t\tlet key = errors[0].key\r\n\t\t\t\t\tkey = key.replace(key[0], key[0].toUpperCase())\r\n\t\t\t\t\t// console.log(key);\r\n\t\t\t\t\tthis['focus'+key] = true\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tsubmitForm(params) {\r\n\t\t\t\tuniIdCo.registerUserByEmail(this.formData).then(e => {\r\n\t\t\t\t\t// console.log(e);\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/uni_modules/uni-id-pages/pages/login/login-withpwd',\r\n\t\t\t\t\t\tcomplete: (e) => {\r\n\t\t\t\t\t\t\t// console.log(e);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t\t.catch(e => {\r\n\t\t\t\t\t// console.log(e);\r\n\t\t\t\t\tconsole.log(e.message);\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tnavigateBack() {\r\n\t\t\t\tuni.navigateBack()\r\n\t\t\t},\r\n\t\t\ttoLogin() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/uni_modules/uni-id-pages/pages/login/login-withpwd'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tregisterByUserName() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/uni_modules/uni-id-pages/pages/register/register'\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t@import \"@/uni_modules/uni-id-pages/common/login-page.scss\";\r\n\t\r\n\t@media screen and (max-width: 690px) {\r\n\t\t.uni-content{\r\n\t\t\tmargin-top: 15px;\r\n\t\t}\r\n\t}\r\n\t@media screen and (min-width: 690px) {\r\n\t\t.uni-content{\r\n\t\t\tpadding: 30px 40px;\r\n\t\t\tmax-height: 650px;\r\n\t\t}\r\n\t\t.link-box {\r\n\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\tdisplay: flex;\r\n\t\t\t/* #endif */\r\n\t\t\tflex-direction: row;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\tmargin-top: 10px;\r\n\t\t}\r\n\t\t.link {\r\n\t\t\tfont-size: 12px;\r\n\t\t}\r\n\t}\r\n\t.uni-content ::v-deep .uni-forms-item__label {\r\n\t\tposition: absolute;\r\n\t\tleft: -15px;\r\n\t}\r\n\r\n\tbutton {\r\n\t\tmargin-top: 15px;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register-by-email.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register-by-email.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753757591940\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
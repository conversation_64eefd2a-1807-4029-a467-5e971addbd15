<view class="uni-content"><match-media min-width="{{690}}"><view class="login-logo"><image src="{{logo}}"></image></view><text class="title title-box">修改密码</text></match-media><uni-forms class="vue-ref" vue-id="5f4f4fd2-1" value="{{formData}}" err-show-type="toast" data-ref="form" bind:__l="__l" vue-slots="{{['default']}}"><uni-forms-item vue-id="{{('5f4f4fd2-2')+','+('5f4f4fd2-1')}}" name="oldPassword" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput class="input-box" vue-id="{{('5f4f4fd2-3')+','+('5f4f4fd2-2')}}" focus="{{focusOldPassword}}" type="password" inputBorder="{{false}}" placeholder="请输入旧密码" value="{{formData.oldPassword}}" data-event-opts="{{[['^blur',[['e0']]],['^input',[['__set_model',['$0','oldPassword','$event',[]],['formData']]]]]}}" bind:blur="__e" bind:input="__e" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item vue-id="{{('5f4f4fd2-4')+','+('5f4f4fd2-1')}}" name="newPassword" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput class="input-box" vue-id="{{('5f4f4fd2-5')+','+('5f4f4fd2-4')}}" focus="{{focusNewPassword}}" type="password" inputBorder="{{false}}" placeholder="请输入新密码" value="{{formData.newPassword}}" data-event-opts="{{[['^blur',[['e1']]],['^input',[['__set_model',['$0','newPassword','$event',[]],['formData']]]]]}}" bind:blur="__e" bind:input="__e" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item vue-id="{{('5f4f4fd2-6')+','+('5f4f4fd2-1')}}" name="newPassword2" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput class="input-box" vue-id="{{('5f4f4fd2-7')+','+('5f4f4fd2-6')}}" focus="{{focusNewPassword2}}" type="password" inputBorder="{{false}}" placeholder="请再次输入新密码" value="{{formData.newPassword2}}" data-event-opts="{{[['^blur',[['e2']]],['^input',[['__set_model',['$0','newPassword2','$event',[]],['formData']]]]]}}" bind:blur="__e" bind:input="__e" bind:__l="__l"></uni-easyinput></uni-forms-item><button class="uni-btn send-btn-box" type="primary" data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" bindtap="__e">提交</button></uni-forms></view>
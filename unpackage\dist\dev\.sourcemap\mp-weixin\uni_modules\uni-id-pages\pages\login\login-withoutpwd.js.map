{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/login/login-withoutpwd.vue?ba1b", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/login/login-withoutpwd.vue?a03d", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/login/login-withoutpwd.vue?83c4", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/login/login-withoutpwd.vue?8f63", "uni-app:///uni_modules/uni-id-pages/pages/login/login-withoutpwd.vue", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/login/login-withoutpwd.vue?fbec", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/login/login-withoutpwd.vue?2af1"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "mixins", "data", "type", "phone", "focusPhone", "logo", "computed", "loginTypes", "config", "isPhone", "imgSrc", "onLoad", "item", "uni", "onShow", "onUnload", "onReady", "methods", "showCurrentWebview", "currentWebview", "quickLogin", "options", "toSmsPage", "title", "icon", "duration", "url", "toPwdLogin", "chooseArea"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,wBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyI;AACzI;AACoE;AACL;AACsC;;;AAGrG;AAC+K;AAC/K,gBAAgB,gLAAU;AAC1B,EAAE,sFAAM;AACR,EAAE,uGAAM;AACR,EAAE,gHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6VAEN;AACP,KAAK;AACL;AACA,aAAa,oSAEN;AACP,KAAK;AACL;AACA,aAAa,uVAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzDA;AAAA;AAAA;AAAA;AAAyqB,CAAgB,yoBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACoC7rB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAFA;AAAA,eAGA;EACAC;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,iCACAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;MACA;IACA;IACAC;MAAA;MACA,qFACA;IACA;EACA;EACAC;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cACA;cACAT;cACA;;cAEA;cACA;gBACA;cACA;cACA;gBACA;gBACA;kBACA;oBAAA,OACAU;kBAAA;gBACA;cACA;cACAC;gBACA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAC,2BASA;EACAC;IACAF;EACA;EACAG;IACA;EAAA,CAaA;EACAC;IACAC;MACA;MACAC;QACA;MACA;IACA;IACAC;MAAA;MACA;MAEA;QACAC;MACA;MAEA;MAEA;IACA;IACAC;MACA;QACA;QACA;UACAC;UACAC;UACAC;QACA;MACA;MACA;QACA;MACA;MACA;MACAZ;QACAa;MACA;IACA;IACA;IACAC;MACAd;QACAa;MACA;IACA;IACAE;MACAf;QACAU;QACAC;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7JA;AAAA;AAAA;AAAA;AAA4xC,CAAgB,4pCAAG,EAAC,C;;;;;;;;;;;ACAhzC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-id-pages/pages/login/login-withoutpwd.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './uni_modules/uni-id-pages/pages/login/login-withoutpwd.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./login-withoutpwd.vue?vue&type=template&id=3d746ff5&scoped=true&\"\nvar renderjs\nimport script from \"./login-withoutpwd.vue?vue&type=script&lang=js&\"\nexport * from \"./login-withoutpwd.vue?vue&type=script&lang=js&\"\nimport style0 from \"./login-withoutpwd.vue?vue&type=style&index=0&id=3d746ff5&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3d746ff5\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-id-pages/pages/login/login-withoutpwd.vue\"\nexport default component.exports", "export * from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login-withoutpwd.vue?vue&type=template&id=3d746ff5&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIdPagesAgreements: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-id-pages/components/uni-id-pages-agreements/uni-id-pages-agreements\" */ \"@/uni_modules/uni-id-pages/components/uni-id-pages-agreements/uni-id-pages-agreements.vue\"\n      )\n    },\n    uniEasyinput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput\" */ \"@/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue\"\n      )\n    },\n    uniIdPagesFabLogin: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-id-pages/components/uni-id-pages-fab-login/uni-id-pages-fab-login\" */ \"@/uni_modules/uni-id-pages/components/uni-id-pages-fab-login/uni-id-pages-fab-login.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = [\"apple\", \"weixin\", \"weixinMobile\"].includes(_vm.type)\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.focusPhone = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login-withoutpwd.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login-withoutpwd.vue?vue&type=script&lang=js&\"", "<!-- 免密登录页 -->\r\n<template>\r\n\t<view class=\"uni-content\">\r\n\t\t<view class=\"login-logo\">\r\n\t\t\t<image :src=\"logo\"></image>\r\n\t\t</view>\r\n\t\t<!-- 顶部文字 -->\r\n\t\t<text class=\"title\">请选择登录方式</text>\r\n\t\t<!-- 快捷登录框 当url带参数时有效 -->\r\n\t\t<template v-if=\"['apple','weixin', 'weixinMobile'].includes(type)\">\r\n\t\t\t<text class=\"tip\">将根据第三方账号服务平台的授权范围获取你的信息</text>\r\n\t\t\t<view class=\"quickLogin\">\r\n\t\t\t\t<image v-if=\"type !== 'weixinMobile'\" @click=\"quickLogin\" :src=\"imgSrc\" mode=\"widthFix\"\r\n\t\t\t\t\tclass=\"quickLoginBtn\"></image>\r\n\t\t\t\t<button v-else type=\"primary\" open-type=\"getPhoneNumber\" @getphonenumber=\"quickLogin\"\r\n\t\t\t\t\tclass=\"uni-btn\">微信授权手机号登录</button>\r\n\t\t\t\t<uni-id-pages-agreements scope=\"register\" ref=\"agreements\"></uni-id-pages-agreements>\r\n\t\t\t</view>\r\n\t\t</template>\r\n\t\t<template v-else>\r\n\t\t\t<text class=\"tip\">未注册的账号验证通过后将自动注册</text>\r\n\t\t\t<view class=\"phone-box\">\r\n\t\t\t\t<view @click=\"chooseArea\" class=\"area\">+86</view>\r\n\t\t\t\t<uni-easyinput :focus=\"focusPhone\" @blur=\"focusPhone = false\" class=\"input-box\" type=\"number\"\r\n\t\t\t\t\t:inputBorder=\"false\" v-model=\"phone\" maxlength=\"11\" placeholder=\"请输入手机号\" />\r\n\t\t\t</view>\r\n\t\t\t<uni-id-pages-agreements scope=\"register\" ref=\"agreements\"></uni-id-pages-agreements>\r\n\t\t\t<button class=\"uni-btn\" type=\"primary\" @click=\"toSmsPage\">获取验证码</button>\r\n\t\t</template>\r\n\t\t<!-- 固定定位的快捷登录按钮 -->\r\n\t\t<uni-id-pages-fab-login ref=\"uniFabLogin\"></uni-id-pages-fab-login>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tlet currentWebview; //当前窗口对象\r\n\timport config from '@/uni_modules/uni-id-pages/config.js'\r\n\timport mixin from '@/uni_modules/uni-id-pages/common/login-page.mixin.js';\r\n\texport default {\r\n\t\tmixins: [mixin],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttype: \"\", //快捷登录方式\r\n\t\t\t\tphone: \"\", //手机号码\r\n\t\t\t\tfocusPhone: false,\r\n\t\t\t\tlogo: \"/static/logo.png\"\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tasync loginTypes() { //读取配置的登录优先级\r\n\t\t\t\treturn config.loginTypes\r\n\t\t\t},\r\n\t\t\tisPhone() { //手机号码校验正则\r\n\t\t\t\treturn /^1\\d{10}$/.test(this.phone);\r\n\t\t\t},\r\n\t\t\timgSrc() { //大快捷登录按钮图\r\n\t\t\t\treturn this.type == 'weixin' ? '/uni_modules/uni-id-pages/static/login/weixin.png' :\r\n\t\t\t\t\t'/uni_modules/uni-id-pages/static/app-plus/apple.png'\r\n\t\t\t}\r\n\t\t},\r\n\t\tasync onLoad(e) {\r\n\t\t\t//获取通过url传递的参数type设置当前登录方式，如果没传递直接默认以配置的登录\r\n\t\t\tlet type = e.type || config.loginTypes[0]\r\n\t\t\tthis.type = type\r\n\r\n\t\t\t// console.log(\"this.type: -----------\",this.type);\r\n\t\t\tif (type != 'univerify') {\r\n\t\t\t\tthis.focusPhone = true\r\n\t\t\t}\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t//关闭重复显示的登录快捷方式\r\n\t\t\t\tif (['weixin', 'apple'].includes(type)) {\r\n\t\t\t\t\tthis.$refs.uniFabLogin.servicesList = this.$refs.uniFabLogin.servicesList.filter(item =>\r\n\t\t\t\t\t\titem.id != type)\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\tuni.$on('uni-id-pages-setLoginType', type => {\r\n\t\t\t\tthis.type = type\r\n\t\t\t})\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\t// #ifdef H5\r\n\t\t\tdocument.onkeydown = event => {\r\n\t\t\t\tvar e = event || window.event;\r\n\t\t\t\tif (e && e.keyCode == 13) { //回车键的键值为13\r\n\t\t\t\t\tthis.toSmsPage()\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tonUnload() {\r\n\t\t\tuni.$off('uni-id-pages-setLoginType')\r\n\t\t},\r\n\t\tonReady() {\r\n\t\t\t// 是否优先启动一键登录。即：页面一加载就启动一键登录\r\n\t\t\t//#ifdef APP-PLUS\r\n\t\t\tif (this.type == \"univerify\") {\r\n\t\t\t\tconst pages = getCurrentPages();\r\n\t\t\t\tcurrentWebview = pages[pages.length - 1].$getAppWebview();\r\n\t\t\t\tcurrentWebview.setStyle({\r\n\t\t\t\t\t\"top\": \"2000px\" // 隐藏当前页面窗体\r\n\t\t\t\t})\r\n\t\t\t\tthis.type == this.loginTypes[1]\r\n\t\t\t\t// console.log('开始一键登录');\r\n\t\t\t\tthis.$refs.uniFabLogin.login_before('univerify')\r\n\t\t\t}\r\n\t\t\t//#endif\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tshowCurrentWebview(){\r\n\t\t\t\t// 恢复当前页面窗体的显示 一键登录，默认不显示当前窗口\r\n\t\t\t\tcurrentWebview.setStyle({\r\n\t\t\t\t\t\"top\": 0\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tquickLogin(e) {\r\n\t\t\t\tlet options = {}\r\n\r\n\t\t\t\tif (e.detail?.code) {\r\n\t\t\t\t\toptions.phoneNumberCode = e.detail.code\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (this.type === 'weixinMobile' && !e.detail?.code) return\r\n\r\n\t\t\t\tthis.$refs.uniFabLogin.login_before(this.type, true, options)\r\n\t\t\t},\r\n\t\t\ttoSmsPage() {\r\n\t\t\t\tif (!this.isPhone) {\r\n\t\t\t\t\tthis.focusPhone = true\r\n\t\t\t\t\treturn uni.showToast({\r\n\t\t\t\t\t\ttitle: \"手机号码格式不正确\",\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 3000\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\tif (this.needAgreements && !this.agree) {\r\n\t\t\t\t\treturn this.$refs.agreements.popup(this.toSmsPage)\r\n\t\t\t\t}\r\n\t\t\t\t// 发送验证吗\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/uni_modules/uni-id-pages/pages/login/login-smscode?phoneNumber=' + this.phone\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t//去密码登录页\r\n\t\t\ttoPwdLogin() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '../login/password'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tchooseArea() {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '暂不支持其他国家',\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\tduration: 3000\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import \"@/uni_modules/uni-id-pages/common/login-page.scss\";\r\n\r\n\t@media screen and (min-width: 690px) {\r\n\t\t.uni-content {\r\n\t\t\theight: 350px;\r\n\t\t}\r\n\t}\r\n\r\n\t.uni-content,\r\n\t.quickLogin {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.phone-box {\r\n\t\tposition: relative;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.area {\r\n\t\tposition: absolute;\r\n\t\tleft: 10px;\r\n\t\tz-index: 9;\r\n\t\ttop: 12px;\r\n\t\tfont-size: 14px;\r\n\t}\r\n\r\n\t.area::after {\r\n\t\tcontent: \"\";\r\n\t\tborder: 3px solid transparent;\r\n\t\tborder-top-color: #000;\r\n\t\ttop: 12px;\r\n\t\tleft: 3px;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t/* #ifdef MP */\r\n\t// 解决小程序端开启虚拟节点virtualHost引起的 class = input-box丢失的问题 [详情参考](https://uniapp.dcloud.net.cn/matter.html#%E5%90%84%E5%AE%B6%E5%B0%8F%E7%A8%8B%E5%BA%8F%E5%AE%9E%E7%8E%B0%E6%9C%BA%E5%88%B6%E4%B8%8D%E5%90%8C-%E5%8F%AF%E8%83%BD%E5%AD%98%E5%9C%A8%E7%9A%84%E5%B9%B3%E5%8F%B0%E5%85%BC%E5%AE%B9%E9%97%AE%E9%A2%98)\r\n\t.phone-box ::v-deep .uni-easyinput__content,\r\n\t/* #endif */\r\n\t.input-box {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tbox-sizing: border-box;\r\n\t\t/* #endif */\r\n\t\tflex: 1;\r\n\t\tpadding-left: 45px;\r\n\t\tmargin-bottom: 10px;\r\n\t\tborder-radius: 0;\r\n\t}\r\n\r\n\t.quickLogin {\r\n\t\theight: 350px;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.quickLoginBtn {\r\n\t\tmargin: 20px 0;\r\n\t\twidth: 450rpx;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tmax-width: 230px;\r\n\t\t/* #endif */\r\n\t\theight: 82rpx;\r\n\t}\r\n\r\n\t.tip {\r\n\t\tmargin-top: -15px;\r\n\t\tmargin-bottom: 20px;\r\n\t}\r\n\r\n\t@media screen and (min-width: 690px) {\r\n\t\t.quickLogin {\r\n\t\t\theight: auto;\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login-withoutpwd.vue?vue&type=style&index=0&id=3d746ff5&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login-withoutpwd.vue?vue&type=style&index=0&id=3d746ff5&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753757592029\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
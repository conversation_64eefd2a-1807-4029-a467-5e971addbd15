{"version": 3, "sources": ["webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/login/login-smscode.vue?649c", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/login/login-smscode.vue?6e0a", "uni-app:///uni_modules/uni-id-pages/pages/login/login-smscode.vue", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/login/login-smscode.vue?aba0", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/login/login-smscode.vue?305b", "uni-app:///main.js", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/login/login-smscode.vue?2223", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/login/login-smscode.vue?7d5f"], "names": ["mixins", "data", "computed", "tipText", "onLoad", "phoneNumber", "onShow", "methods", "submit", "errorOptions", "type", "title", "icon", "duration", "uniIdCo", "console", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,iVAEN;AACP,KAAK;AACL;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAAsqB,CAAgB,soBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACkB1rB;;;;;;;;;;;;;;;;;;eACA;EACAA;EACAC;IACA;MACA;MACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;EACAC,8BAEA;IAAA,IADAC;IAEA;EACA;EACAC,2BASA;EACAC;IACAC;MAAA;MAAA;MACA;QACAC;UACAC;QACA;MACA;MACA;QACA;QACA;UACAC;UACAC;UACAC;QACA;MACA;MACAC;QACA;QACA;QACA;MACA;QACA;MACA;QACA;UACA;QACA;UACAC;QACA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjFA;AAAA;AAAA;AAAA;AAAyxC,CAAgB,ypCAAG,EAAC,C;;;;;;;;;;;ACA7yC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;;;;ACNL;AAE2D;AAC3D;AACA;AAHA;AACAC,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AAC+K;AAC/K,gBAAgB,gLAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA", "file": "uni_modules/uni-id-pages/pages/login/login-smscode.js", "sourcesContent": ["var components\ntry {\n  components = {\n    uniForms: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-forms/components/uni-forms/uni-forms\" */ \"@/uni_modules/uni-forms/components/uni-forms/uni-forms.vue\"\n      )\n    },\n    uniIdPagesSmsForm: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-id-pages/components/uni-id-pages-sms-form/uni-id-pages-sms-form\" */ \"@/uni_modules/uni-id-pages/components/uni-id-pages-sms-form/uni-id-pages-sms-form.vue\"\n      )\n    },\n    uniPopupCaptcha: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-captcha/components/uni-popup-captcha/uni-popup-captcha\" */ \"@/uni_modules/uni-captcha/components/uni-popup-captcha/uni-popup-captcha.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login-smscode.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login-smscode.vue?vue&type=script&lang=js&\"", "<!-- 短信验证码登录页 -->\r\n<template>\r\n\t<view class=\"uni-content\">\r\n\t\t<view class=\"login-logo\">\r\n\t\t\t<image :src=\"logo\"></image>\r\n\t\t</view>\r\n\t\t<!-- 顶部文字 -->\r\n\t\t<text class=\"title\">请输入验证码</text>\r\n\t\t<text class=\"tip\">先输入图形验证码，再获取短信验证码</text>\r\n\t\t<uni-forms>\r\n\t\t\t<uni-id-pages-sms-form focusCaptchaInput v-model=\"code\" type=\"login-by-sms\" ref=\"smsCode\" :phone=\"phone\">\r\n\t\t\t</uni-id-pages-sms-form>\r\n\t\t\t<button class=\"uni-btn send-btn\" type=\"primary\" @click=\"submit\">登录</button>\r\n\t\t</uni-forms>\r\n\t\t<uni-popup-captcha @confirm=\"submit\" v-model=\"captcha\" scene=\"login-by-sms\" ref=\"popup\"></uni-popup-captcha>\r\n\t</view>\r\n</template>\r\n<script>\r\n\timport mixin from '@/uni_modules/uni-id-pages/common/login-page.mixin.js';\r\n\texport default {\r\n\t\tmixins: [mixin],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t\"code\": \"\",\r\n\t\t\t\t\"phone\": \"\",\r\n\t\t\t\t\"captcha\": \"\",\r\n\t\t\t\t\"logo\": \"/static/logo.png\"\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\ttipText() {\r\n\t\t\t\treturn '验证码已通过短信发送至' + this.phone;\r\n\t\t\t},\r\n\t\t},\r\n\t\tonLoad({\r\n\t\t\tphoneNumber\r\n\t\t}) {\r\n\t\t\tthis.phone = phoneNumber;\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\t// #ifdef H5\r\n\t\t\tdocument.onkeydown = event => {\r\n\t\t\t\tvar e = event || window.event;\r\n\t\t\t\tif (e && e.keyCode == 13) { //回车键的键值为13\r\n\t\t\t\t\tthis.submit()\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tsubmit() { //完成并提交\r\n\t\t\t\tconst uniIdCo = uniCloud.importObject(\"uni-id-co\", {\r\n\t\t\t\t\terrorOptions: {\r\n\t\t\t\t\t\ttype: 'toast'\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\tif (this.code.length != 6) {\r\n\t\t\t\t\tthis.$refs.smsCode.focusSmsCodeInput = true\r\n\t\t\t\t\treturn uni.showToast({\r\n\t\t\t\t\t\ttitle: '验证码不能为空',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 3000\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\tuniIdCo.loginBySms({\r\n\t\t\t\t\t\"mobile\": this.phone,\r\n\t\t\t\t\t\"code\": this.code,\r\n\t\t\t\t\t\"captcha\": this.captcha\r\n\t\t\t\t}).then(e => {\r\n\t\t\t\t\tthis.loginSuccess(e)\r\n\t\t\t\t}).catch(e => {\r\n\t\t\t\t\tif (e.errCode == 'uni-id-captcha-required') {\r\n\t\t\t\t\t\tthis.$refs.popup.open()\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.log(e.errMsg);\r\n\t\t\t\t\t}\r\n\t\t\t\t}).finally(e => {\r\n\t\t\t\t\tthis.captcha = ''\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n\t@import \"@/uni_modules/uni-id-pages/common/login-page.scss\";\r\n\r\n\t.tip {\r\n\t\tmargin-top: -15px;\r\n\t\tmargin-bottom: 15px;\r\n\t}\r\n\r\n\t.popup-captcha {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tpadding: 20rpx;\r\n\t\tbackground-color: #FFF;\r\n\t\tborder-radius: 2px;\r\n\t\tflex-direction: column;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.popup-captcha .title {\r\n\t\tfont-weight: normal;\r\n\t\tpadding: 0;\r\n\t\tpadding-bottom: 15px;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.popup-captcha .close {\r\n\t\tposition: absolute;\r\n\t\tbottom: -40px;\r\n\t\tmargin-left: -13px;\r\n\t\tleft: 50%;\r\n\t}\r\n\r\n\t.popup-captcha .uni-btn {\r\n\t\tmargin: 0;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login-smscode.vue?vue&type=style&index=0&id=3dae6cf6&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login-smscode.vue?vue&type=style&index=0&id=3dae6cf6&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753757591974\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './uni_modules/uni-id-pages/pages/login/login-smscode.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./login-smscode.vue?vue&type=template&id=3dae6cf6&scoped=true&\"\nvar renderjs\nimport script from \"./login-smscode.vue?vue&type=script&lang=js&\"\nexport * from \"./login-smscode.vue?vue&type=script&lang=js&\"\nimport style0 from \"./login-smscode.vue?vue&type=style&index=0&id=3dae6cf6&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3dae6cf6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-id-pages/pages/login/login-smscode.vue\"\nexport default component.exports", "export * from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login-smscode.vue?vue&type=template&id=3dae6cf6&scoped=true&\""], "sourceRoot": ""}
<template>
	<view class="container">
		<!-- 房源信息 -->
		<view class="house-info" v-if="houseInfo">
			<view class="house-image-container">
				<image class="house-image" :src="houseInfo.images[0]?.url || '/static/default-house.jpg'" mode="aspectFill"></image>
			</view>
			<view class="house-details">
				<view class="house-title">{{ houseInfo.title }}</view>
				<view class="house-desc">{{ houseInfo.house_type }} · {{ houseInfo.area }}㎡</view>
				<view class="house-price">
					<text class="price-symbol">¥</text>
					<text class="price-number">{{ houseInfo.rent_price }}</text>
					<text class="price-unit">/月</text>
				</view>
				<view class="house-address">{{ houseInfo.address.district }} {{ houseInfo.address.street }}</view>
			</view>
		</view>

		<!-- 预约表单 -->
		<view class="appointment-form">
			<view class="form-section">
				<view class="section-title">预约时间</view>
				<view class="time-selector">
					<view class="date-picker" @click="showDatePicker">
						<text class="picker-text" :class="{ placeholder: !selectedDate }">
							{{ selectedDate || '请选择日期' }}
						</text>
						<uni-icons type="calendar" size="16" color="#999"></uni-icons>
					</view>
					<view class="time-slots">
						<view class="time-slot" v-for="(slot, index) in timeSlots" :key="index"
							:class="{ active: selectedTimeSlot === slot.value, disabled: slot.disabled }"
							@click="selectTimeSlot(slot)">
							{{ slot.label }}
						</view>
					</view>
				</view>
			</view>

			<view class="form-section">
				<view class="section-title">联系信息</view>
				<view class="form-item">
					<text class="item-label">姓名</text>
					<input class="item-input" v-model="formData.contactName" placeholder="请输入您的姓名" />
				</view>
				<view class="form-item">
					<text class="item-label">手机号</text>
					<input class="item-input" v-model="formData.contactPhone" placeholder="请输入手机号" type="number" />
				</view>
			</view>

			<view class="form-section">
				<view class="section-title">备注信息</view>
				<textarea class="message-input" v-model="formData.message" 
					placeholder="请输入您的需求或问题，如：希望了解周边配套设施等" maxlength="200"></textarea>
				<view class="char-count">{{ formData.message.length }}/200</view>
			</view>

			<view class="form-section">
				<view class="tips-box">
					<uni-icons type="info" size="16" color="#ff9500"></uni-icons>
					<text class="tips-text">预约成功后，房东会在24小时内联系您确认看房时间</text>
				</view>
			</view>
		</view>

		<!-- 提交按钮 -->
		<view class="submit-section">
			<button class="submit-btn" :disabled="!canSubmit" @click="submitAppointment">
				{{ submitting ? '提交中...' : '提交预约' }}
			</button>
		</view>

		<!-- 日期选择器 -->
		<uni-popup ref="datePopup" type="bottom">
			<view class="date-popup">
				<view class="popup-header">
					<text class="cancel-btn" @click="cancelDatePicker">取消</text>
					<text class="popup-title">选择日期</text>
					<text class="confirm-btn" @click="confirmDatePicker">确定</text>
				</view>
				<picker-view class="date-picker-view" :value="pickerValue" @change="onDatePickerChange">
					<picker-view-column>
						<view class="picker-item" v-for="(item, index) in dateOptions" :key="index">
							{{ item.label }}
						</view>
					</picker-view-column>
				</picker-view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				houseId: '',
				houseInfo: null,
				selectedDate: '',
				selectedTimeSlot: '',
				formData: {
					contactName: '',
					contactPhone: '',
					message: ''
				},
				submitting: false,
				
				// 时间段选项
				timeSlots: [
					{ label: '09:00-10:00', value: '09:00-10:00', disabled: false },
					{ label: '10:00-11:00', value: '10:00-11:00', disabled: false },
					{ label: '11:00-12:00', value: '11:00-12:00', disabled: false },
					{ label: '14:00-15:00', value: '14:00-15:00', disabled: false },
					{ label: '15:00-16:00', value: '15:00-16:00', disabled: false },
					{ label: '16:00-17:00', value: '16:00-17:00', disabled: false },
					{ label: '17:00-18:00', value: '17:00-18:00', disabled: false },
					{ label: '19:00-20:00', value: '19:00-20:00', disabled: false }
				],
				
				// 日期选择器相关
				pickerValue: [0],
				dateOptions: [],
				tempSelectedDate: ''
			}
		},
		computed: {
			canSubmit() {
				return this.selectedDate && 
					   this.selectedTimeSlot && 
					   this.formData.contactName.trim() && 
					   this.formData.contactPhone.trim() && 
					   /^1[3-9]\d{9}$/.test(this.formData.contactPhone) &&
					   !this.submitting
			}
		},
		onLoad(options) {
			if (options.houseId) {
				this.houseId = options.houseId
				this.loadHouseInfo()
			}
			this.initDateOptions()
			this.loadUserInfo()
		},
		methods: {
			// 加载房源信息
			async loadHouseInfo() {
				try {
					const result = await uniCloud.callFunction({
						name: 'rental-service',
						data: {
							action: 'getHouseDetail',
							houseId: this.houseId
						}
					})

					if (result.result.code === 0) {
						this.houseInfo = result.result.data
					} else {
						uni.showToast({
							title: result.result.message,
							icon: 'none'
						})
						setTimeout(() => {
							uni.navigateBack()
						}, 1500)
					}
				} catch (error) {
					uni.showToast({
						title: '加载失败',
						icon: 'none'
					})
				}
			},

			// 加载用户信息
			loadUserInfo() {
				const userInfo = uni.getStorageSync('userInfo')
				if (userInfo) {
					this.formData.contactName = userInfo.nickname || ''
					this.formData.contactPhone = userInfo.mobile || ''
				}
			},

			// 初始化日期选项（未来7天）
			initDateOptions() {
				const today = new Date()
				const options = []
				
				for (let i = 1; i <= 7; i++) {
					const date = new Date(today.getTime() + i * 24 * 60 * 60 * 1000)
					const month = date.getMonth() + 1
					const day = date.getDate()
					const weekDay = ['日', '一', '二', '三', '四', '五', '六'][date.getDay()]
					
					options.push({
						label: `${month}月${day}日 周${weekDay}`,
						value: `${date.getFullYear()}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`,
						date: date
					})
				}
				
				this.dateOptions = options
			},

			// 显示日期选择器
			showDatePicker() {
				this.$refs.datePopup.open()
			},

			// 日期选择器变化
			onDatePickerChange(e) {
				this.pickerValue = e.detail.value
				this.tempSelectedDate = this.dateOptions[e.detail.value[0]].value
			},

			// 取消日期选择
			cancelDatePicker() {
				this.$refs.datePopup.close()
			},

			// 确认日期选择
			confirmDatePicker() {
				if (this.tempSelectedDate) {
					this.selectedDate = this.tempSelectedDate
					this.selectedTimeSlot = '' // 重置时间段选择
				}
				this.$refs.datePopup.close()
			},

			// 选择时间段
			selectTimeSlot(slot) {
				if (slot.disabled) return
				this.selectedTimeSlot = slot.value
			},

			// 提交预约
			async submitAppointment() {
				if (!this.canSubmit) return

				// 表单验证
				if (!this.validateForm()) return

				this.submitting = true

				try {
					const appointmentDateTime = `${this.selectedDate} ${this.selectedTimeSlot.split('-')[0]}:00`
					
					const result = await uniCloud.callFunction({
						name: 'rental-service',
						data: {
							action: 'createAppointment',
							house_id: this.houseId,
							appointment_date: appointmentDateTime,
							contact_phone: this.formData.contactPhone,
							contact_name: this.formData.contactName,
							message: this.formData.message
						}
					})

					if (result.result.code === 0) {
						uni.showToast({
							title: '预约成功',
							icon: 'success'
						})
						
						// 保存用户信息
						this.saveUserInfo()
						
						setTimeout(() => {
							uni.navigateBack()
						}, 1500)
					} else {
						uni.showToast({
							title: result.result.message,
							icon: 'none'
						})
					}
				} catch (error) {
					uni.showToast({
						title: '预约失败',
						icon: 'none'
					})
				}

				this.submitting = false
			},

			// 表单验证
			validateForm() {
				if (!this.formData.contactName.trim()) {
					uni.showToast({
						title: '请输入姓名',
						icon: 'none'
					})
					return false
				}

				if (!this.formData.contactPhone.trim()) {
					uni.showToast({
						title: '请输入手机号',
						icon: 'none'
					})
					return false
				}

				if (!/^1[3-9]\d{9}$/.test(this.formData.contactPhone)) {
					uni.showToast({
						title: '手机号格式不正确',
						icon: 'none'
					})
					return false
				}

				return true
			},

			// 保存用户信息
			saveUserInfo() {
				const userInfo = uni.getStorageSync('userInfo') || {}
				userInfo.nickname = this.formData.contactName
				userInfo.mobile = this.formData.contactPhone
				uni.setStorageSync('userInfo', userInfo)
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		background-color: #f5f5f5;
		min-height: 100vh;
		padding-bottom: 120rpx;
	}

	.house-info {
		display: flex;
		background-color: #fff;
		padding: 30rpx;
		margin-bottom: 20rpx;

		.house-image-container {
			width: 160rpx;
			height: 120rpx;
			margin-right: 20rpx;

			.house-image {
				width: 100%;
				height: 100%;
				border-radius: 8rpx;
			}
		}

		.house-details {
			flex: 1;

			.house-title {
				font-size: 28rpx;
				color: #333;
				font-weight: bold;
				margin-bottom: 8rpx;
			}

			.house-desc {
				font-size: 24rpx;
				color: #666;
				margin-bottom: 8rpx;
			}

			.house-price {
				display: flex;
				align-items: baseline;
				margin-bottom: 8rpx;

				.price-symbol {
					font-size: 20rpx;
					color: #ff4757;
				}

				.price-number {
					font-size: 28rpx;
					color: #ff4757;
					font-weight: bold;
				}

				.price-unit {
					font-size: 20rpx;
					color: #ff4757;
				}
			}

			.house-address {
				font-size: 24rpx;
				color: #999;
			}
		}
	}

	.appointment-form {
		.form-section {
			background-color: #fff;
			margin-bottom: 20rpx;
			padding: 30rpx;

			.section-title {
				font-size: 28rpx;
				color: #333;
				font-weight: bold;
				margin-bottom: 20rpx;
			}

			.time-selector {
				.date-picker {
					display: flex;
					justify-content: space-between;
					align-items: center;
					height: 80rpx;
					padding: 0 20rpx;
					background-color: #f5f5f5;
					border-radius: 8rpx;
					margin-bottom: 20rpx;

					.picker-text {
						font-size: 26rpx;
						color: #333;

						&.placeholder {
							color: #999;
						}
					}
				}

				.time-slots {
					display: flex;
					flex-wrap: wrap;
					gap: 20rpx;

					.time-slot {
						flex: 0 0 calc(50% - 10rpx);
						height: 60rpx;
						line-height: 60rpx;
						text-align: center;
						background-color: #f5f5f5;
						border-radius: 8rpx;
						font-size: 24rpx;
						color: #666;

						&.active {
							background-color: #007aff;
							color: #fff;
						}

						&.disabled {
							background-color: #f0f0f0;
							color: #ccc;
						}
					}
				}
			}

			.form-item {
				display: flex;
				align-items: center;
				height: 80rpx;
				border-bottom: 1rpx solid #eee;

				&:last-child {
					border-bottom: none;
				}

				.item-label {
					width: 120rpx;
					font-size: 26rpx;
					color: #333;
				}

				.item-input {
					flex: 1;
					font-size: 26rpx;
					color: #333;
				}
			}

			.message-input {
				width: 100%;
				min-height: 120rpx;
				padding: 20rpx;
				background-color: #f5f5f5;
				border-radius: 8rpx;
				font-size: 26rpx;
				color: #333;
				box-sizing: border-box;
			}

			.char-count {
				text-align: right;
				font-size: 22rpx;
				color: #999;
				margin-top: 10rpx;
			}

			.tips-box {
				display: flex;
				align-items: flex-start;
				padding: 20rpx;
				background-color: #fff7e6;
				border-radius: 8rpx;

				.tips-text {
					flex: 1;
					font-size: 24rpx;
					color: #ff9500;
					margin-left: 10rpx;
					line-height: 1.4;
				}
			}
		}
	}

	.submit-section {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		padding: 20rpx 30rpx;
		background-color: #fff;
		border-top: 1rpx solid #eee;

		.submit-btn {
			width: 100%;
			height: 80rpx;
			background-color: #007aff;
			color: #fff;
			border-radius: 8rpx;
			font-size: 28rpx;
			border: none;

			&:disabled {
				background-color: #ccc;
			}
		}
	}

	.date-popup {
		background-color: #fff;

		.popup-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 80rpx;
			padding: 0 30rpx;
			border-bottom: 1rpx solid #eee;

			.cancel-btn,
			.confirm-btn {
				font-size: 26rpx;
				color: #007aff;
			}

			.popup-title {
				font-size: 28rpx;
				color: #333;
				font-weight: bold;
			}
		}

		.date-picker-view {
			height: 400rpx;

			.picker-item {
				display: flex;
				align-items: center;
				justify-content: center;
				height: 80rpx;
				font-size: 26rpx;
				color: #333;
			}
		}
	}
</style>

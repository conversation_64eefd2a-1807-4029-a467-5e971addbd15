<view><block wx:if="{{isCertify}}"><uni-list vue-id="21653b1b-1" bind:__l="__l" vue-slots="{{['default']}}"><uni-list-item class="item" vue-id="{{('21653b1b-2')+','+('21653b1b-1')}}" title="姓名" rightText="{{userInfo.realNameAuth.realName}}" bind:__l="__l"></uni-list-item><uni-list-item class="item" vue-id="{{('21653b1b-3')+','+('21653b1b-1')}}" title="身份证号码" rightText="{{userInfo.realNameAuth.identity}}" bind:__l="__l"></uni-list-item></uni-list></block><block wx:else><view class="uni-content"><block wx:if="{{verifyFail}}"><view class="face-icon"><image class="face-icon-image" src="./face-verify-icon.svg"></image></view><view class="error-title">{{verifyFailTitle}}</view><view class="error-description">{{verifyFailContent}}</view><block wx:if="{{verifyFailCode!==10013}}"><button type="primary" data-event-opts="{{[['tap',[['retry',['$event']]]]]}}" bindtap="__e">重新开始验证</button></block><block wx:else><button type="primary" data-event-opts="{{[['tap',[['retry',['$event']]]]]}}" bindtap="__e">返回</button></block><block wx:if="{{isDev}}"><view class="dev-tip">请在控制台查看详细错误（此提示仅在开发环境展示）</view></block></block><block wx:else><text class="title">实名认证</text><uni-forms vue-id="21653b1b-4" bind:__l="__l" vue-slots="{{['default']}}"><uni-forms-item vue-id="{{('21653b1b-5')+','+('21653b1b-4')}}" name="realName" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" class="input-box" vue-id="{{('21653b1b-6')+','+('21653b1b-5')}}" placeholder="姓名" clearable="{{false}}" value="{{realName}}" data-event-opts="{{[['^input',[['__set_model',['','realName','$event',[]]]]]]}}" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item vue-id="{{('21653b1b-7')+','+('21653b1b-4')}}" name="idCard" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" class="input-box" vue-id="{{('21653b1b-8')+','+('21653b1b-7')}}" placeholder="身份证号码" clearable="{{false}}" value="{{idCard}}" data-event-opts="{{[['^input',[['__set_model',['','idCard','$event',[]]]]]]}}" bind:__l="__l"></uni-easyinput></uni-forms-item></uni-forms><uni-id-pages-agreements class="vue-ref" style="margin-bottom:20px;" vue-id="21653b1b-9" scope="realNameVerify" data-ref="agreements" bind:__l="__l"></uni-id-pages-agreements><button type="primary" disabled="{{!certifyIdNext}}" data-event-opts="{{[['tap',[['getCertifyId',['$event']]]]]}}" bindtap="__e">确定</button></block></view></block></view>
(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uni_modules/uni-id-pages/components/uni-id-pages-bind-mobile/uni-id-pages-bind-mobile"],{5369:function(e,n,t){"use strict";(function(e,u,i){var o=t("47a9");Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var c=o(t("7eb4")),r=o(t("ee10")),a=e.database(),s=(a.collection("uni-id-users"),e.importObject("uni-id-co")),f={emits:["success"],computed:{},data:function(){return{}},methods:{beforeGetphonenumber:function(){return(0,r.default)(c.default.mark((function n(){return c.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,new Promise((function(n,t){u.showLoading({mask:!0}),i.checkSession({success:function(){n(),u.hideLoading()},fail:function(){i.login({success:function(i){var o=i.code;e.importObject("uni-id-co",{customUI:!0}).loginByWeixin({code:o}).then((function(e){n()})).catch((function(e){console.log(e),t()})).finally((function(e){u.hideLoading()}))},fail:function(e){console.error(e),t()}})}})}));case 2:return n.abrupt("return",n.sent);case 3:case"end":return n.stop()}}),n)})))()},bindMobileByMpWeixin:function(e){var n=this;return(0,r.default)(c.default.mark((function t(){return c.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if("getPhoneNumber:ok"!=e.detail.errMsg){t.next=6;break}return t.next=3,n.beforeGetphonenumber();case 3:s.bindMobileByMpWeixin(e.detail).then((function(e){n.$emit("success")})).finally((function(e){n.closeMe()})),t.next=7;break;case 6:n.closeMe();case 7:case"end":return t.stop()}}),t)})))()},open:function(){var e=this;return(0,r.default)(c.default.mark((function n(){return c.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:e.$refs.popup.open();case 1:case"end":return n.stop()}}),n)})))()},closeMe:function(e){this.$refs.popup.close()}}};n.default=f}).call(this,t("861b")["uniCloud"],t("df3c")["default"],t("3223")["default"])},8578:function(e,n,t){},"8c52":function(e,n,t){"use strict";t.d(n,"b",(function(){return i})),t.d(n,"c",(function(){return o})),t.d(n,"a",(function(){return u}));var u={uniPopup:function(){return t.e("uni_modules/uni-popup/components/uni-popup/uni-popup").then(t.bind(null,"2f8a"))}},i=function(){var e=this.$createElement;this._self._c},o=[]},"93f9":function(e,n,t){"use strict";t.r(n);var u=t("8c52"),i=t("b94d");for(var o in i)["default"].indexOf(o)<0&&function(e){t.d(n,e,(function(){return i[e]}))}(o);t("99f1");var c=t("828b"),r=Object(c["a"])(i["default"],u["b"],u["c"],!1,null,"0c5ef59a",null,!1,u["a"],void 0);n["default"]=r.exports},"99f1":function(e,n,t){"use strict";var u=t("8578"),i=t.n(u);i.a},b94d:function(e,n,t){"use strict";t.r(n);var u=t("5369"),i=t.n(u);for(var o in u)["default"].indexOf(o)<0&&function(e){t.d(n,e,(function(){return u[e]}))}(o);n["default"]=i.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uni_modules/uni-id-pages/components/uni-id-pages-bind-mobile/uni-id-pages-bind-mobile-create-component',
    {
        'uni_modules/uni-id-pages/components/uni-id-pages-bind-mobile/uni-id-pages-bind-mobile-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("93f9"))
        })
    },
    [['uni_modules/uni-id-pages/components/uni-id-pages-bind-mobile/uni-id-pages-bind-mobile-create-component']]
]);

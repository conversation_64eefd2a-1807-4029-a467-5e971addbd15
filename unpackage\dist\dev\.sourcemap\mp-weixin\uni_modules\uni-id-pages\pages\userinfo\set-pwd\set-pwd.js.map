{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/userinfo/set-pwd/set-pwd.vue?9944", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/userinfo/set-pwd/set-pwd.vue?f50f", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/userinfo/set-pwd/set-pwd.vue?abd5", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/userinfo/set-pwd/set-pwd.vue?1d67", "uni-app:///uni_modules/uni-id-pages/pages/userinfo/set-pwd/set-pwd.vue", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/userinfo/set-pwd/set-pwd.vue?6156", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/userinfo/set-pwd/set-pwd.vue?41ed"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "customUI", "name", "data", "uniIdRedirectUrl", "loginType", "logo", "focusNewPassword", "focusNewPassword2", "allowSkip", "formData", "code", "<PERSON><PERSON>a", "newPassword", "newPassword2", "rules", "computed", "userInfo", "onReady", "onLoad", "methods", "submit", "title", "icon", "then", "uniIdCo", "password", "uni", "content", "showCancel", "success", "mutations", "console", "skip"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACkL;AAClL,gBAAgB,gLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,8RAEN;AACP,KAAK;AACL;AACA,aAAa,oSAEN;AACP,KAAK;AACL;AACA,aAAa,iVAEN;AACP,KAAK;AACL;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7DA;AAAA;AAAA;AAAA;AAA+qB,CAAgB,goBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACqCnsB;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;EACAC;AACA;AAAA,eACA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IAAA;IACA;IACA;IAEA;MACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACA;QACA;UACAC;UACAC;QACA;MACA;MAEA,2BACAC;QACAC;UACAC;UACAf;UACAC;QACA;UACAe;YACAC;YACAC;YACAC;cACAC;gBACA3B;gBACAC;cACA;YACA;UACA;QACA;UACAsB;YACAC;YACAC;UACA;QACA;MACA;QACA;UACA;QACA;UACAG;QACA;MACA;QACA;MACA;IACA;IACAC;MACAF;QACA3B;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChIA;AAAA;AAAA;AAAA;AAA8yC,CAAgB,mpCAAG,EAAC,C;;;;;;;;;;;ACAl0C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-id-pages/pages/userinfo/set-pwd/set-pwd.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './uni_modules/uni-id-pages/pages/userinfo/set-pwd/set-pwd.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./set-pwd.vue?vue&type=template&id=8dcd6296&scoped=true&\"\nvar renderjs\nimport script from \"./set-pwd.vue?vue&type=script&lang=js&\"\nexport * from \"./set-pwd.vue?vue&type=script&lang=js&\"\nimport style0 from \"./set-pwd.vue?vue&type=style&index=0&id=8dcd6296&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"8dcd6296\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-id-pages/pages/userinfo/set-pwd/set-pwd.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./set-pwd.vue?vue&type=template&id=8dcd6296&scoped=true&\"", "var components\ntry {\n  components = {\n    uniForms: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-forms/components/uni-forms/uni-forms\" */ \"@/uni_modules/uni-forms/components/uni-forms/uni-forms.vue\"\n      )\n    },\n    uniFormsItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-forms/components/uni-forms-item/uni-forms-item\" */ \"@/uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.vue\"\n      )\n    },\n    uniEasyinput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput\" */ \"@/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue\"\n      )\n    },\n    uniIdPagesSmsForm: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-id-pages/components/uni-id-pages-sms-form/uni-id-pages-sms-form\" */ \"@/uni_modules/uni-id-pages/components/uni-id-pages-sms-form/uni-id-pages-sms-form.vue\"\n      )\n    },\n    uniPopupCaptcha: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-captcha/components/uni-popup-captcha/uni-popup-captcha\" */ \"@/uni_modules/uni-captcha/components/uni-popup-captcha/uni-popup-captcha.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.focusNewPassword = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.focusNewPassword2 = false\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./set-pwd.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./set-pwd.vue?vue&type=script&lang=js&\"", "<!-- 设置密码 -->\r\n<template>\r\n  <view class=\"uni-content\">\r\n    <match-media :min-width=\"690\">\r\n      <view class=\"login-logo\">\r\n        <image :src=\"logo\"></image>\r\n      </view>\r\n      <!-- 顶部文字 -->\r\n      <text class=\"title title-box \">设置密码</text>\r\n    </match-media>\r\n\r\n    <uni-forms class=\"set-password-form\" ref=\"form\" :value=\"formData\" err-show-type=\"toast\">\r\n      <text class=\"tip\">输入密码</text>\r\n      <uni-forms-item name=\"newPassword\">\r\n        <uni-easyinput :focus=\"focusNewPassword\" @blur=\"focusNewPassword = false\" class=\"input-box\"\r\n                       type=\"password\" :inputBorder=\"false\" v-model=\"formData.newPassword\" placeholder=\"请输入密码\">\r\n        </uni-easyinput>\r\n      </uni-forms-item>\r\n      <text class=\"tip\">再次输入密码</text>\r\n      <uni-forms-item name=\"newPassword2\">\r\n        <uni-easyinput :focus=\"focusNewPassword2\" @blur=\"focusNewPassword2 = false\" class=\"input-box\"\r\n                       type=\"password\" :inputBorder=\"false\" v-model=\"formData.newPassword2\" placeholder=\"请再次输入新密码\">\r\n        </uni-easyinput>\r\n      </uni-forms-item>\r\n      <uni-id-pages-sms-form v-model=\"formData.code\" type=\"set-pwd-by-sms\" ref=\"smsCode\" :phone=\"userInfo.mobile\">\r\n      </uni-id-pages-sms-form>\r\n      <view class=\"link-box\">\r\n        <button class=\"uni-btn send-btn\" type=\"primary\" @click=\"submit\">确认</button>\r\n        <button v-if=\"allowSkip\" class=\"uni-btn send-btn\" type=\"default\" @click=\"skip\">跳过</button>\r\n      </view>\r\n\r\n    </uni-forms>\r\n    <uni-popup-captcha @confirm=\"submit\" v-model=\"formData.captcha\" scene=\"set-pwd-by-sms\" ref=\"popup\"></uni-popup-captcha>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport passwordMod from '@/uni_modules/uni-id-pages/common/password.js'\r\nimport {store, mutations} from '@/uni_modules/uni-id-pages/common/store.js'\r\nimport config from '@/uni_modules/uni-id-pages/config.js'\r\n\r\nconst uniIdCo = uniCloud.importObject(\"uni-id-co\", {\r\n  customUI:true\r\n})\r\nexport default {\r\n  name: \"set-pwd.vue\",\r\n  data () {\r\n    return {\r\n      uniIdRedirectUrl: '',\r\n      loginType: '',\r\n      logo: '/static/logo.png',\r\n      focusNewPassword: false,\r\n      focusNewPassword2: false,\r\n      allowSkip: false,\r\n      formData: {\r\n        code: \"\",\r\n        captcha: \"\",\r\n        newPassword: \"\",\r\n        newPassword2: \"\"\r\n      },\r\n      rules: passwordMod.getPwdRules('newPassword', 'newPassword2')\r\n    }\r\n  },\r\n  computed: {\r\n    userInfo () {\r\n      return store.userInfo\r\n    }\r\n  },\r\n  onReady() {\r\n    this.$refs.form.setRules(this.rules)\r\n  },\r\n  onLoad (e) {\r\n    this.uniIdRedirectUrl = e.uniIdRedirectUrl\r\n    this.loginType = e.loginType\r\n\r\n    if (config.setPasswordAfterLogin && config.setPasswordAfterLogin?.allowSkip) {\r\n      this.allowSkip = true\r\n    }\r\n  },\r\n  methods: {\r\n    submit () {\r\n      if(! /^\\d{6}$/.test(this.formData.code)){\r\n        this.$refs.smsCode.focusSmsCodeInput = true\r\n        return uni.showToast({\r\n          title: '验证码格式不正确',\r\n          icon: 'none'\r\n        });\r\n      }\r\n\r\n      this.$refs.form.validate()\r\n          .then(res => {\r\n            uniIdCo.setPwd({\r\n              password: this.formData.newPassword,\r\n              code: this.formData.code,\r\n              captcha: this.formData.captcha\r\n            }).then(e => {\r\n              uni.showModal({\r\n                content: '密码设置成功',\r\n                showCancel: false,\r\n                success: () => {\r\n                  mutations.loginBack({\r\n                    uniIdRedirectUrl: this.uniIdRedirectUrl,\r\n                    loginType: this.loginType\r\n                  })\r\n                }\r\n              });\r\n            }).catch(e => {\r\n              uni.showModal({\r\n                content: e.message,\r\n                showCancel: false\r\n              });\r\n            })\r\n          }).catch(e => {\r\n            if (e.errCode == 'uni-id-captcha-required') {\r\n              this.$refs.popup.open()\r\n            } else {\r\n              console.log(e.errMsg);\r\n            }\r\n          }).finally(e => {\r\n            this.formData.captcha = ''\r\n          })\r\n    },\r\n    skip () {\r\n      mutations.loginBack({\r\n\t\tuniIdRedirectUrl: this.uniIdRedirectUrl,\r\n\t  })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import \"@/uni_modules/uni-id-pages/common/login-page.scss\";\r\n\r\n.uni-btn[type=\"default\"] {\r\n  color: inherit!important;\r\n}\r\n\r\n.uni-content ::v-deep .uni-forms-item {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.popup-captcha {\r\n  /* #ifndef APP-NVUE */\r\n  display: flex;\r\n  /* #endif */\r\n  padding: 20rpx;\r\n  background-color: #FFF;\r\n  border-radius: 2px;\r\n  flex-direction: column;\r\n  position: relative;\r\n}\r\n\r\n.popup-captcha .title {\r\n  font-weight: normal;\r\n  padding: 0;\r\n  padding-bottom: 15px;\r\n  color: #666;\r\n}\r\n\r\n.popup-captcha .close {\r\n  position: absolute;\r\n  bottom: -40px;\r\n  margin-left: -13px;\r\n  left: 50%;\r\n}\r\n\r\n.popup-captcha .uni-btn {\r\n  margin: 0;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./set-pwd.vue?vue&type=style&index=0&id=8dcd6296&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./set-pwd.vue?vue&type=style&index=0&id=8dcd6296&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753757591991\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
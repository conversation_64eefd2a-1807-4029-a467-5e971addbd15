const db = uniCloud.database()
const dbCmd = db.command

module.exports = {
	_before: function () {
		// 这里是云函数的前置方法，可以在这里统一处理一些逻辑
	},

	/**
	 * 获取房源列表
	 * @param {Object} params 查询参数
	 * @param {String} params.keyword 关键词搜索
	 * @param {String} params.city 城市
	 * @param {String} params.district 区县
	 * @param {Number} params.minPrice 最低价格
	 * @param {Number} params.maxPrice 最高价格
	 * @param {String} params.houseType 房屋类型
	 * @param {Array} params.facilities 设施要求
	 * @param {Number} params.pageSize 每页数量
	 * @param {Number} params.pageNum 页码
	 * @param {String} params.orderBy 排序方式
	 */
	async getHouseList(params = {}) {
		const {
			keyword = '',
			city = '',
			district = '',
			minPrice = 0,
			maxPrice = 999999,
			houseType = '',
			facilities = [],
			pageSize = 10,
			pageNum = 1,
			orderBy = 'create_date desc'
		} = params

		// 构建查询条件
		let whereCondition = {
			status: '已发布'
		}

		// 关键词搜索
		if (keyword) {
			whereCondition = dbCmd.and([
				whereCondition,
				dbCmd.or([
					{
						title: new RegExp(keyword, 'i')
					},
					{
						description: new RegExp(keyword, 'i')
					},
					{
						'address.street': new RegExp(keyword, 'i')
					}
				])
			])
		}

		// 城市筛选
		if (city) {
			whereCondition['address.city'] = city
		}

		// 区县筛选
		if (district) {
			whereCondition['address.district'] = district
		}

		// 价格范围
		if (minPrice > 0 || maxPrice < 999999) {
			whereCondition.rent_price = dbCmd.and([
				dbCmd.gte(minPrice),
				dbCmd.lte(maxPrice)
			])
		}

		// 房屋类型
		if (houseType) {
			whereCondition.house_type = houseType
		}

		// 设施筛选
		if (facilities.length > 0) {
			whereCondition.facilities = dbCmd.all(facilities)
		}

		try {
			// 查询总数
			const countResult = await db.collection('rental-houses')
				.where(whereCondition)
				.count()

			// 查询数据
			const dataResult = await db.collection('rental-houses')
				.where(whereCondition)
				.field({
					title: true,
					rent_price: true,
					deposit: true,
					house_type: true,
					area: true,
					address: true,
					facilities: true,
					images: true,
					contact_info: true,
					rent_type: true,
					available_date: true,
					view_count: true,
					favorite_count: true,
					rating: true,
					create_date: true
				})
				.orderBy(orderBy)
				.skip((pageNum - 1) * pageSize)
				.limit(pageSize)
				.get()

			return {
				code: 0,
				message: 'success',
				data: {
					list: dataResult.data,
					total: countResult.total,
					pageNum: pageNum,
					pageSize: pageSize,
					totalPages: Math.ceil(countResult.total / pageSize)
				}
			}
		} catch (error) {
			return {
				code: -1,
				message: error.message,
				data: null
			}
		}
	},

	/**
	 * 获取房源详情
	 * @param {String} houseId 房源ID
	 */
	async getHouseDetail(houseId) {
		if (!houseId) {
			return {
				code: -1,
				message: '房源ID不能为空',
				data: null
			}
		}

		try {
			// 获取房源详情
			const houseResult = await db.collection('rental-houses')
				.doc(houseId)
				.get()

			if (houseResult.data.length === 0) {
				return {
					code: -1,
					message: '房源不存在',
					data: null
				}
			}

			const house = houseResult.data[0]

			// 增加浏览次数
			await db.collection('rental-houses')
				.doc(houseId)
				.update({
					view_count: dbCmd.inc(1)
				})

			// 获取房东信息
			const landlordResult = await db.collection('uni-id-users')
				.doc(house.landlord_id)
				.field({
					nickname: true,
					avatar: true,
					mobile: true
				})
				.get()

			house.landlord_info = landlordResult.data[0] || {}

			return {
				code: 0,
				message: 'success',
				data: house
			}
		} catch (error) {
			return {
				code: -1,
				message: error.message,
				data: null
			}
		}
	},

	/**
	 * 创建预约
	 * @param {Object} appointmentData 预约数据
	 */
	async createAppointment(appointmentData) {
		const {
			house_id,
			appointment_date,
			contact_phone,
			contact_name,
			message = ''
		} = appointmentData

		// 参数验证
		if (!house_id || !appointment_date || !contact_phone || !contact_name) {
			return {
				code: -1,
				message: '必填参数不能为空',
				data: null
			}
		}

		// 验证手机号格式
		const phoneRegex = /^1[3-9]\d{9}$/
		if (!phoneRegex.test(contact_phone)) {
			return {
				code: -1,
				message: '手机号格式不正确',
				data: null
			}
		}

		try {
			// 获取房源信息
			const houseResult = await db.collection('rental-houses')
				.doc(house_id)
				.field({
					landlord_id: true,
					title: true,
					status: true
				})
				.get()

			if (houseResult.data.length === 0) {
				return {
					code: -1,
					message: '房源不存在',
					data: null
				}
			}

			const house = houseResult.data[0]
			if (house.status !== '已发布') {
				return {
					code: -1,
					message: '该房源暂不可预约',
					data: null
				}
			}

			// 检查是否已有相同时间的预约
			const existingAppointment = await db.collection('rental-appointments')
				.where({
					house_id: house_id,
					tenant_id: this.getUniIdToken().uid,
					appointment_date: appointment_date,
					status: dbCmd.in(['待确认', '已确认'])
				})
				.get()

			if (existingAppointment.data.length > 0) {
				return {
					code: -1,
					message: '您已预约过该时间段',
					data: null
				}
			}

			// 创建预约记录
			const appointmentResult = await db.collection('rental-appointments')
				.add({
					house_id: house_id,
					tenant_id: this.getUniIdToken().uid,
					landlord_id: house.landlord_id,
					appointment_date: new Date(appointment_date),
					contact_phone: contact_phone,
					contact_name: contact_name,
					message: message,
					status: '待确认',
					create_date: new Date()
				})

			// 发送消息通知房东
			await this.sendMessage({
				from_user_id: this.getUniIdToken().uid,
				to_user_id: house.landlord_id,
				house_id: house_id,
				appointment_id: appointmentResult.id,
				type: '预约消息',
				title: '新的看房预约',
				content: `您有一个新的看房预约，房源：${house.title}，预约时间：${appointment_date}`
			})

			return {
				code: 0,
				message: '预约成功',
				data: {
					appointment_id: appointmentResult.id
				}
			}
		} catch (error) {
			return {
				code: -1,
				message: error.message,
				data: null
			}
		}
	},

	/**
	 * 收藏/取消收藏房源
	 * @param {String} houseId 房源ID
	 * @param {Boolean} isFavorite 是否收藏
	 */
	async toggleFavorite(houseId, isFavorite = true) {
		if (!houseId) {
			return {
				code: -1,
				message: '房源ID不能为空',
				data: null
			}
		}

		const userId = this.getUniIdToken().uid
		if (!userId) {
			return {
				code: -1,
				message: '用户未登录',
				data: null
			}
		}

		try {
			if (isFavorite) {
				// 添加收藏
				// 先检查是否已收藏
				const existingFavorite = await db.collection('rental-favorites')
					.where({
						house_id: houseId,
						user_id: userId
					})
					.get()

				if (existingFavorite.data.length > 0) {
					return {
						code: -1,
						message: '已收藏过该房源',
						data: null
					}
				}

				// 添加收藏记录
				await db.collection('rental-favorites')
					.add({
						house_id: houseId,
						user_id: userId,
						create_date: new Date()
					})

				// 增加房源收藏数
				await db.collection('rental-houses')
					.doc(houseId)
					.update({
						favorite_count: dbCmd.inc(1)
					})

				return {
					code: 0,
					message: '收藏成功',
					data: null
				}
			} else {
				// 取消收藏
				const deleteResult = await db.collection('rental-favorites')
					.where({
						house_id: houseId,
						user_id: userId
					})
					.remove()

				if (deleteResult.deleted > 0) {
					// 减少房源收藏数
					await db.collection('rental-houses')
						.doc(houseId)
						.update({
							favorite_count: dbCmd.inc(-1)
						})

					return {
						code: 0,
						message: '取消收藏成功',
						data: null
					}
				} else {
					return {
						code: -1,
						message: '未收藏该房源',
						data: null
					}
				}
			}
		} catch (error) {
			return {
				code: -1,
				message: error.message,
				data: null
			}
		}
	},

	/**
	 * 获取用户收藏列表
	 * @param {Number} pageSize 每页数量
	 * @param {Number} pageNum 页码
	 */
	async getFavoriteList(pageSize = 10, pageNum = 1) {
		const userId = this.getUniIdToken().uid
		if (!userId) {
			return {
				code: -1,
				message: '用户未登录',
				data: null
			}
		}

		try {
			// 查询总数
			const countResult = await db.collection('rental-favorites')
				.where({
					user_id: userId
				})
				.count()

			// 联表查询收藏的房源信息
			const dataResult = await db.collection('rental-favorites')
				.aggregate()
				.match({
					user_id: userId
				})
				.lookup({
					from: 'rental-houses',
					localField: 'house_id',
					foreignField: '_id',
					as: 'house_info'
				})
				.unwind('$house_info')
				.project({
					_id: 1,
					house_id: 1,
					create_date: 1,
					'house_info.title': 1,
					'house_info.rent_price': 1,
					'house_info.house_type': 1,
					'house_info.area': 1,
					'house_info.address': 1,
					'house_info.images': 1,
					'house_info.status': 1
				})
				.sort({
					create_date: -1
				})
				.skip((pageNum - 1) * pageSize)
				.limit(pageSize)
				.end()

			return {
				code: 0,
				message: 'success',
				data: {
					list: dataResult.data,
					total: countResult.total,
					pageNum: pageNum,
					pageSize: pageSize,
					totalPages: Math.ceil(countResult.total / pageSize)
				}
			}
		} catch (error) {
			return {
				code: -1,
				message: error.message,
				data: null
			}
		}
	},

	/**
	 * 获取用户预约列表
	 * @param {Number} pageSize 每页数量
	 * @param {Number} pageNum 页码
	 * @param {String} status 预约状态
	 */
	async getUserAppointments(pageSize = 10, pageNum = 1, status = '') {
		const userId = this.getUniIdToken().uid
		if (!userId) {
			return {
				code: -1,
				message: '用户未登录',
				data: null
			}
		}

		try {
			let whereCondition = {
				tenant_id: userId
			}

			if (status) {
				whereCondition.status = status
			}

			// 查询总数
			const countResult = await db.collection('rental-appointments')
				.where(whereCondition)
				.count()

			// 联表查询预约的房源信息
			const dataResult = await db.collection('rental-appointments')
				.aggregate()
				.match(whereCondition)
				.lookup({
					from: 'rental-houses',
					localField: 'house_id',
					foreignField: '_id',
					as: 'house_info'
				})
				.unwind('$house_info')
				.project({
					_id: 1,
					house_id: 1,
					appointment_date: 1,
					contact_phone: 1,
					contact_name: 1,
					message: 1,
					status: 1,
					create_date: 1,
					'house_info.title': 1,
					'house_info.address': 1,
					'house_info.images': 1,
					'house_info.contact_info': 1
				})
				.sort({
					create_date: -1
				})
				.skip((pageNum - 1) * pageSize)
				.limit(pageSize)
				.end()

			return {
				code: 0,
				message: 'success',
				data: {
					list: dataResult.data,
					total: countResult.total,
					pageNum: pageNum,
					pageSize: pageSize,
					totalPages: Math.ceil(countResult.total / pageSize)
				}
			}
		} catch (error) {
			return {
				code: -1,
				message: error.message,
				data: null
			}
		}
	},

	/**
	 * 发送消息
	 * @param {Object} messageData 消息数据
	 */
	async sendMessage(messageData) {
		try {
			await db.collection('rental-messages')
				.add({
					...messageData,
					is_read: false,
					create_date: new Date()
				})
			return true
		} catch (error) {
			console.error('发送消息失败：', error)
			return false
		}
	}
}

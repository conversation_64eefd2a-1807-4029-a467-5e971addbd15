{"bsonType": "object", "required": ["title", "rent_price", "house_type", "area", "address", "landlord_id"], "permission": {"read": true, "create": "'CREATE_RENTAL_HOUSES' in auth.permission", "update": "'UPDATE_RENTAL_HOUSES' in auth.permission", "delete": "'DELETE_RENTAL_HOUSES' in auth.permission"}, "properties": {"_id": {"description": "房源ID，系统自动生成"}, "title": {"bsonType": "string", "title": "房源标题", "description": "房源标题", "maxLength": 100, "trim": "both"}, "description": {"bsonType": "string", "title": "房源描述", "description": "详细描述房源信息", "maxLength": 2000, "trim": "both"}, "rent_price": {"bsonType": "number", "title": "租金", "description": "月租金（元）", "minimum": 0}, "deposit": {"bsonType": "number", "title": "押金", "description": "押金金额（元）", "minimum": 0, "defaultValue": 0}, "house_type": {"bsonType": "string", "title": "房屋类型", "description": "房屋类型：1室1厅、2室1厅等", "enum": ["1室1厅", "2室1厅", "2室2厅", "3室1厅", "3室2厅", "4室2厅", "单间", "合租", "整租"]}, "area": {"bsonType": "number", "title": "面积", "description": "房屋面积（平方米）", "minimum": 0}, "floor": {"bsonType": "string", "title": "楼层", "description": "所在楼层，如：3/6（3楼，共6楼）", "maxLength": 20}, "orientation": {"bsonType": "string", "title": "朝向", "description": "房屋朝向", "enum": ["东", "南", "西", "北", "东南", "东北", "西南", "西北", "南北"]}, "decoration": {"bsonType": "string", "title": "装修情况", "description": "装修程度", "enum": ["毛坯", "简装", "精装", "豪装"]}, "address": {"bsonType": "object", "title": "地址信息", "description": "详细地址信息", "properties": {"province": {"bsonType": "string", "title": "省份", "maxLength": 20}, "city": {"bsonType": "string", "title": "城市", "maxLength": 20}, "district": {"bsonType": "string", "title": "区县", "maxLength": 20}, "street": {"bsonType": "string", "title": "详细地址", "maxLength": 200}, "longitude": {"bsonType": "number", "title": "经度", "description": "地理位置经度"}, "latitude": {"bsonType": "number", "title": "纬度", "description": "地理位置纬度"}}}, "facilities": {"bsonType": "array", "title": "配套设施", "description": "房屋配套设施列表", "items": {"bsonType": "string", "enum": ["空调", "洗衣机", "冰箱", "热水器", "电视", "宽带", "衣柜", "床", "沙发", "餐桌", "微波炉", "电梯", "停车位", "阳台", "独立卫生间"]}}, "images": {"bsonType": "array", "title": "房源图片", "description": "房源图片列表", "items": {"bsonType": "object", "properties": {"url": {"bsonType": "string", "title": "图片URL"}, "name": {"bsonType": "string", "title": "图片名称"}, "type": {"bsonType": "string", "title": "图片类型", "enum": ["封面", "客厅", "卧室", "厨房", "卫生间", "阳台", "其他"]}}}}, "landlord_id": {"bsonType": "string", "title": "房东ID", "description": "房东用户ID，关联uni-id-users表", "foreignKey": "uni-id-users._id"}, "contact_info": {"bsonType": "object", "title": "联系方式", "description": "房东联系方式", "properties": {"name": {"bsonType": "string", "title": "联系人姓名", "maxLength": 20}, "phone": {"bsonType": "string", "title": "联系电话", "pattern": "^1[3-9]\\d{9}$"}, "wechat": {"bsonType": "string", "title": "微信号", "maxLength": 50}}}, "status": {"bsonType": "string", "title": "房源状态", "description": "房源当前状态", "enum": ["待审核", "已发布", "已出租", "已下架", "已删除"], "defaultValue": "待审核"}, "rent_type": {"bsonType": "string", "title": "出租方式", "description": "出租类型", "enum": ["整租", "合租", "短租"], "defaultValue": "整租"}, "available_date": {"bsonType": "timestamp", "title": "可入住时间", "description": "房源可入住的时间"}, "view_count": {"bsonType": "int", "title": "浏览次数", "description": "房源被浏览的次数", "defaultValue": 0}, "favorite_count": {"bsonType": "int", "title": "收藏次数", "description": "房源被收藏的次数", "defaultValue": 0}, "rating": {"bsonType": "number", "title": "评分", "description": "房源平均评分（1-5分）", "minimum": 1, "maximum": 5, "defaultValue": 5}, "create_date": {"bsonType": "timestamp", "title": "创建时间", "description": "房源发布时间", "forceDefaultValue": {"$env": "now"}}, "update_date": {"bsonType": "timestamp", "title": "更新时间", "description": "房源信息最后更新时间", "forceDefaultValue": {"$env": "now"}}, "audit_info": {"bsonType": "object", "title": "审核信息", "description": "房源审核相关信息", "properties": {"audit_status": {"bsonType": "string", "title": "审核状态", "enum": ["待审核", "审核通过", "审核拒绝"], "defaultValue": "待审核"}, "audit_user_id": {"bsonType": "string", "title": "审核人ID", "description": "审核人员ID"}, "audit_date": {"bsonType": "timestamp", "title": "审核时间", "description": "审核完成时间"}, "audit_remark": {"bsonType": "string", "title": "审核备注", "description": "审核意见或备注", "maxLength": 500}}}}}
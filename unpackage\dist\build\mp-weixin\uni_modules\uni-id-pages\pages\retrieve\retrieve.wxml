<view class="uni-content"><match-media min-width="{{690}}"><view class="login-logo"><image src="{{logo}}"></image></view><text class="title title-box">通过手机验证码找回密码</text></match-media><uni-forms class="vue-ref" vue-id="0eb87486-1" value="{{formData}}" err-show-type="toast" data-ref="form" bind:__l="__l" vue-slots="{{['default']}}"><uni-forms-item vue-id="{{('0eb87486-2')+','+('0eb87486-1')}}" name="phone" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput class="input-box" vue-id="{{('0eb87486-3')+','+('0eb87486-2')}}" focus="{{focusPhone}}" disabled="{{lock}}" type="number" inputBorder="{{false}}" maxlength="11" placeholder="请输入手机号" value="{{formData.phone}}" data-event-opts="{{[['^blur',[['e0']]],['^input',[['__set_model',['$0','phone','$event',[]],['formData']]]]]}}" bind:blur="__e" bind:input="__e" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item vue-id="{{('0eb87486-4')+','+('0eb87486-1')}}" name="code" bind:__l="__l" vue-slots="{{['default']}}"><uni-id-pages-sms-form bind:input="__e" class="vue-ref" vue-id="{{('0eb87486-5')+','+('0eb87486-4')}}" phone="{{formData.phone}}" type="reset-pwd-by-sms" data-ref="shortCode" value="{{formData.code}}" data-event-opts="{{[['^input',[['__set_model',['$0','code','$event',[]],['formData']]]]]}}" bind:__l="__l"></uni-id-pages-sms-form></uni-forms-item><uni-forms-item vue-id="{{('0eb87486-6')+','+('0eb87486-1')}}" name="password" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput class="input-box" vue-id="{{('0eb87486-7')+','+('0eb87486-6')}}" focus="{{focusPassword}}" type="password" inputBorder="{{false}}" placeholder="请输入新密码" value="{{formData.password}}" data-event-opts="{{[['^blur',[['e1']]],['^input',[['__set_model',['$0','password','$event',[]],['formData']]]]]}}" bind:blur="__e" bind:input="__e" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item vue-id="{{('0eb87486-8')+','+('0eb87486-1')}}" name="password2" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput class="input-box" vue-id="{{('0eb87486-9')+','+('0eb87486-8')}}" focus="{{focusPassword2}}" type="password" inputBorder="{{false}}" placeholder="请再次输入新密码" value="{{formData.password2}}" data-event-opts="{{[['^blur',[['e2']]],['^input',[['__set_model',['$0','password2','$event',[]],['formData']]]]]}}" bind:blur="__e" bind:input="__e" bind:__l="__l"></uni-easyinput></uni-forms-item><button class="uni-btn send-btn-box" type="primary" data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" bindtap="__e">提交</button><match-media min-width="{{690}}"><view class="link-box"><text data-event-opts="{{[['tap',[['retrieveByEmail',['$event']]]]]}}" class="link" bindtap="__e">通过邮箱验证码找回密码</text><view></view><text data-event-opts="{{[['tap',[['backLogin',['$event']]]]]}}" class="link" bindtap="__e">返回登录</text></view></match-media></uni-forms><uni-popup-captcha class="vue-ref" vue-id="0eb87486-10" scene="reset-pwd-by-sms" data-ref="popup" value="{{formData.captcha}}" data-event-opts="{{[['^confirm',[['submit']]],['^input',[['__set_model',['$0','captcha','$event',[]],['formData']]]]]}}" bind:confirm="__e" bind:input="__e" bind:__l="__l"></uni-popup-captcha></view>
{"bsonType": "object", "required": ["name"], "permission": {"read": true, "create": "'CREATE_RENTAL_CATEGORIES' in auth.permission", "update": "'UPDATE_RENTAL_CATEGORIES' in auth.permission", "delete": "'DELETE_RENTAL_CATEGORIES' in auth.permission"}, "properties": {"_id": {"description": "分类ID，系统自动生成"}, "name": {"bsonType": "string", "title": "分类名称", "description": "房源分类名称", "maxLength": 50, "trim": "both"}, "description": {"bsonType": "string", "title": "分类描述", "description": "分类详细描述", "maxLength": 200, "trim": "both"}, "icon": {"bsonType": "string", "title": "分类图标", "description": "分类图标URL或图标名称", "maxLength": 200}, "sort": {"bsonType": "int", "title": "排序", "description": "分类排序，数字越小越靠前", "defaultValue": 0}, "enable": {"bsonType": "bool", "title": "是否启用", "description": "分类是否启用", "defaultValue": true}, "create_date": {"bsonType": "timestamp", "title": "创建时间", "description": "分类创建时间", "forceDefaultValue": {"$env": "now"}}}}
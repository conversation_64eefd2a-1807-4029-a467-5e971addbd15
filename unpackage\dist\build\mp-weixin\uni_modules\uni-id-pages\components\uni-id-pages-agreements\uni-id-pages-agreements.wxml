<block wx:if="{{$root.g0}}"><view class="root data-v-4a6ffee8"><block wx:if="{{needAgreements}}"><checkbox-group data-event-opts="{{[['change',[['setAgree',['$event']]]]]}}" bindchange="__e" class="data-v-4a6ffee8"><label class="checkbox-box data-v-4a6ffee8"><checkbox style="transform:scale(0.5);margin-right:-6px;" checked="{{isAgree}}" class="data-v-4a6ffee8"></checkbox><text class="text data-v-4a6ffee8">同意</text></label></checkbox-group><view class="content data-v-4a6ffee8"><block wx:for="{{$root.l0}}" wx:for-item="agreement" wx:for-index="index" wx:key="index"><view class="item data-v-4a6ffee8"><text data-event-opts="{{[['tap',[['navigateTo',['$0'],[[['agreements','',index]]]]]]]}}" class="agreement text data-v-4a6ffee8" bindtap="__e">{{agreement.$orig.title}}</text><block wx:if="{{agreement.m0}}"><text class="text and data-v-4a6ffee8" space="nbsp">和</text></block></view></block></view></block><block wx:if="{{needAgreements||needPopupAgreements}}"><uni-popup vue-id="264990d6-1" type="center" data-ref="popupAgreement" class="data-v-4a6ffee8 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('264990d6-2')+','+('264990d6-1')}}" confirmText="同意" data-event-opts="{{[['^confirm',[['popupConfirm']]]]}}" bind:confirm="__e" class="data-v-4a6ffee8" bind:__l="__l" vue-slots="{{['default']}}"><view class="content data-v-4a6ffee8"><text class="text data-v-4a6ffee8">请先阅读并同意</text><block wx:for="{{$root.l1}}" wx:for-item="agreement" wx:for-index="index" wx:key="index"><view class="item data-v-4a6ffee8"><text data-event-opts="{{[['tap',[['navigateTo',['$0'],[[['agreements','',index]]]]]]]}}" class="agreement text data-v-4a6ffee8" bindtap="__e">{{agreement.$orig.title}}</text><block wx:if="{{agreement.m1}}"><text class="text and data-v-4a6ffee8" space="nbsp">和</text></block></view></block></view></uni-popup-dialog></uni-popup></block></view></block>
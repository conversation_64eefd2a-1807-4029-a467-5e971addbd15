{"bsonType": "object", "required": ["permission_id", "permission_name"], "properties": {"_id": {"description": "ID，系统自动生成"}, "permission_id": {"bsonType": "string", "description": "权限唯一标识", "title": "权限标识"}, "permission_name": {"bsonType": "string", "description": "权限名称", "title": "权限名称"}, "comment": {"bsonType": "string", "description": "权限备注", "title": "备注"}, "create_date": {"bsonType": "timestamp", "description": "创建时间", "forceDefaultValue": {"$env": "now"}}}}
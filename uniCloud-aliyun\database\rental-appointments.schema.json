{"bsonType": "object", "required": ["house_id", "tenant_id", "appointment_date", "contact_phone"], "permission": {"read": true, "create": true, "update": "'UPDATE_RENTAL_APPOINTMENTS' in auth.permission", "delete": "'DELETE_RENTAL_APPOINTMENTS' in auth.permission"}, "properties": {"_id": {"description": "预约ID，系统自动生成"}, "house_id": {"bsonType": "string", "title": "房源ID", "description": "预约的房源ID", "foreignKey": "rental-houses._id"}, "tenant_id": {"bsonType": "string", "title": "租客ID", "description": "预约人用户ID", "foreignKey": "uni-id-users._id"}, "landlord_id": {"bsonType": "string", "title": "房东ID", "description": "房东用户ID", "foreignKey": "uni-id-users._id"}, "appointment_date": {"bsonType": "timestamp", "title": "预约时间", "description": "预约看房的时间"}, "contact_phone": {"bsonType": "string", "title": "联系电话", "description": "预约人联系电话", "pattern": "^1[3-9]\\d{9}$"}, "contact_name": {"bsonType": "string", "title": "联系人姓名", "description": "预约人姓名", "maxLength": 20, "trim": "both"}, "message": {"bsonType": "string", "title": "预约留言", "description": "预约时的留言信息", "maxLength": 500, "trim": "both"}, "status": {"bsonType": "string", "title": "预约状态", "description": "预约当前状态", "enum": ["待确认", "已确认", "已完成", "已取消", "已拒绝"], "defaultValue": "待确认"}, "confirm_date": {"bsonType": "timestamp", "title": "确认时间", "description": "房东确认预约的时间"}, "complete_date": {"bsonType": "timestamp", "title": "完成时间", "description": "看房完成的时间"}, "cancel_reason": {"bsonType": "string", "title": "取消原因", "description": "预约取消的原因", "maxLength": 200}, "rating": {"bsonType": "object", "title": "评价信息", "description": "看房后的评价", "properties": {"house_rating": {"bsonType": "int", "title": "房源评分", "description": "对房源的评分（1-5分）", "minimum": 1, "maximum": 5}, "landlord_rating": {"bsonType": "int", "title": "房东评分", "description": "对房东的评分（1-5分）", "minimum": 1, "maximum": 5}, "comment": {"bsonType": "string", "title": "评价内容", "description": "详细评价内容", "maxLength": 500}, "rating_date": {"bsonType": "timestamp", "title": "评价时间", "description": "评价提交时间"}}}, "create_date": {"bsonType": "timestamp", "title": "创建时间", "description": "预约创建时间", "forceDefaultValue": {"$env": "now"}}, "update_date": {"bsonType": "timestamp", "title": "更新时间", "description": "预约信息最后更新时间", "forceDefaultValue": {"$env": "now"}}}}
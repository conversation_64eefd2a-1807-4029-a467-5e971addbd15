<view class="content"><button data-event-opts="{{[['tap',[['toUserInfoPage',['$event']]]]]}}" style="margin:20px 0 20px 0;" bindtap="__e">个人资料</button><uni-forms-item vue-id="8dd740cc-1" label="切换登录方式" labelWidth="70" bind:__l="__l" vue-slots="{{['default']}}"><uni-data-checkbox bind:input="__e" vue-id="{{('8dd740cc-2')+','+('8dd740cc-1')}}" multiple="{{false}}" localdata="{{loginTypeOption}}" mode="button" value="{{loginType}}" data-event-opts="{{[['^input',[['__set_model',['','loginType','$event',[]]]]]]}}" bind:__l="__l"></uni-data-checkbox></uni-forms-item><button data-event-opts="{{[['tap',[['toLogin',['$event']]]]]}}" bindtap="__e">前往登录</button></view>
{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/userinfo/realname-verify/realname-verify.vue?8ae2", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/userinfo/realname-verify/realname-verify.vue?52ef", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/userinfo/realname-verify/realname-verify.vue?3f9f", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/userinfo/realname-verify/realname-verify.vue?8988", "uni-app:///uni_modules/uni-id-pages/pages/userinfo/realname-verify/realname-verify.vue", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/userinfo/realname-verify/realname-verify.vue?12bb", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/userinfo/realname-verify/realname-verify.vue?497b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "mixins", "data", "realName", "idCard", "certifyId", "verifyFail", "verifyFailCode", "verifyFailTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "computed", "userInfo", "certifyIdNext", "isCertify", "isDev", "onLoad", "methods", "getCertifyId", "uni", "content", "showCancel", "title", "icon", "key", "metaInfo", "uniIdCo", "res", "startFacialRecognitionVerify", "getFrvAuthResult", "customUI", "mask", "errCode", "rest", "console", "success", "mutations", "realNameAuth", "retry"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,uBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA4H;AAC5H;AACmE;AACL;AACc;;;AAG5E;AACkL;AAClL,gBAAgB,gLAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,0FAAM;AACR,EAAE,mGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,8FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uPAEN;AACP,KAAK;AACL;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,8RAEN;AACP,KAAK;AACL;AACA,aAAa,oSAEN;AACP,KAAK;AACL;AACA,aAAa,6VAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1DA;AAAA;AAAA;AAAA;AAAurB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC0C3sB;AACA;AAEA;AAGA;AAEA;AACA;AAAA,eACA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,iCAGAC;kBACAC;kBACAC;gBACA;cAAA;gBAAA,MAYA,sCACA,6BACA;kBAAA;kBAAA;gBAAA;gBAEAF;kBACAG;kBACAC;gBACA;gBAAA;cAAA;gBAIAJ;kBACAK;kBACArB;oBACAC;oBACAC;kBACA;gBACA;gBAEAoB;gBAAA;gBAAA,OAEAC;kBACAtB;kBACAC;kBACAoB;gBACA;cAAA;gBAJAE;gBAMA;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC,uEA0DA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAH;kBACAI;gBACA;gBAAA;gBAEAX;kBACAG;kBACAS;gBACA;gBAAA;gBAAA,OACAL;kBACApB;gBACA;cAAA;gBAFAqB;gBAKAK,UAEAL,IAFAK,SACAC,8CACAN;gBAEA;kBACAO;gBACA;gBAEAf;kBACAC;kBACAC;kBACAc;oBACAC;sBACAC;oBACA;oBACA;kBACA;gBACA;gBAEAlB;kBACAK;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;gBACA;gBACAU;cAAA;gBAAA;gBAEAf;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAmB;MACA;QACA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxPA;AAAA;AAAA;AAAA;AAA8xC,CAAgB,moCAAG,EAAC,C;;;;;;;;;;;ACAlzC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-id-pages/pages/userinfo/realname-verify/realname-verify.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './uni_modules/uni-id-pages/pages/userinfo/realname-verify/realname-verify.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./realname-verify.vue?vue&type=template&id=927da16a&\"\nvar renderjs\nimport script from \"./realname-verify.vue?vue&type=script&lang=js&\"\nexport * from \"./realname-verify.vue?vue&type=script&lang=js&\"\nimport style0 from \"./realname-verify.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-id-pages/pages/userinfo/realname-verify/realname-verify.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./realname-verify.vue?vue&type=template&id=927da16a&\"", "var components\ntry {\n  components = {\n    uniList: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-list/components/uni-list/uni-list\" */ \"@/uni_modules/uni-list/components/uni-list/uni-list.vue\"\n      )\n    },\n    uniListItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-list/components/uni-list-item/uni-list-item\" */ \"@/uni_modules/uni-list/components/uni-list-item/uni-list-item.vue\"\n      )\n    },\n    uniForms: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-forms/components/uni-forms/uni-forms\" */ \"@/uni_modules/uni-forms/components/uni-forms/uni-forms.vue\"\n      )\n    },\n    uniFormsItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-forms/components/uni-forms-item/uni-forms-item\" */ \"@/uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.vue\"\n      )\n    },\n    uniEasyinput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput\" */ \"@/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue\"\n      )\n    },\n    uniIdPagesAgreements: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-id-pages/components/uni-id-pages-agreements/uni-id-pages-agreements\" */ \"@/uni_modules/uni-id-pages/components/uni-id-pages-agreements/uni-id-pages-agreements.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./realname-verify.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./realname-verify.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view>\r\n    <template v-if=\"isCertify\">\r\n      <uni-list>\r\n        <uni-list-item class=\"item\" title=\"姓名\" :rightText=\"userInfo.realNameAuth.realName\"></uni-list-item>\r\n        <uni-list-item class=\"item\" title=\"身份证号码\" :rightText=\"userInfo.realNameAuth.identity\"></uni-list-item>\r\n      </uni-list>\r\n    </template>\r\n    <template v-else>\r\n      <view class=\"uni-content\">\r\n        <template v-if=\"verifyFail\">\r\n          <view class=\"face-icon\">\r\n            <image src=\"./face-verify-icon.svg\" class=\"face-icon-image\" />\r\n          </view>\r\n          <view class=\"error-title\">{{verifyFailTitle}}</view>\r\n          <view class=\"error-description\">{{verifyFailContent}}</view>\r\n          <button type=\"primary\" @click=\"retry\" v-if=\"verifyFailCode !== 10013\">重新开始验证</button>\r\n          <button type=\"primary\" @click=\"retry\" v-else>返回</button>\r\n          <view class=\"dev-tip\" v-if=\"isDev\">请在控制台查看详细错误（此提示仅在开发环境展示）</view>\r\n        </template>\r\n        <template v-else>\r\n          <text class=\"title\">实名认证</text>\r\n          <uni-forms>\r\n            <uni-forms-item name=\"realName\">\r\n              <uni-easyinput placeholder=\"姓名\" class=\"input-box\" v-model=\"realName\" :clearable=\"false\">\r\n              </uni-easyinput>\r\n            </uni-forms-item>\r\n            <uni-forms-item name=\"idCard\">\r\n              <uni-easyinput placeholder=\"身份证号码\" class=\"input-box\" v-model=\"idCard\" :clearable=\"false\">\r\n              </uni-easyinput>\r\n            </uni-forms-item>\r\n          </uni-forms>\r\n          <uni-id-pages-agreements scope=\"realNameVerify\" ref=\"agreements\" style=\"margin-bottom: 20px;\">\r\n          </uni-id-pages-agreements>\r\n          <button type=\"primary\" :disabled=\"!certifyIdNext\" @click=\"getCertifyId\">确定</button>\r\n        </template>\r\n      </view>\r\n    </template>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport checkIdCard from '@/uni_modules/uni-id-pages/common/check-id-card.js';\r\nimport mixin from '@/uni_modules/uni-id-pages/common/login-page.mixin.js';\r\n\r\nimport {\r\n  store,\r\n  mutations\r\n} from '@/uni_modules/uni-id-pages/common/store.js'\r\n\r\nconst uniIdCo = uniCloud.importObject('uni-id-co')\r\nconst tempFrvInfoKey = 'uni-id-pages-temp-frv'\r\nexport default {\r\n  mixins: [mixin],\r\n  data() {\r\n    return {\r\n      realName: '',\r\n      idCard: '',\r\n      certifyId: '',\r\n      verifyFail: false,\r\n      verifyFailCode: 0,\r\n      verifyFailTitle: '',\r\n      verifyFailContent: ''\r\n    }\r\n  },\r\n  computed: {\r\n    userInfo() {\r\n      return store.userInfo\r\n    },\r\n    certifyIdNext() {\r\n      return Boolean(this.realName) && Boolean(this.idCard) && (this.needAgreements && this.agree)\r\n    },\r\n    isCertify() {\r\n      return this.userInfo.realNameAuth && this.userInfo.realNameAuth.authStatus === 2\r\n    },\r\n    isDev() {\r\n      return process.env.NODE_ENV === 'development'\r\n    }\r\n  },\r\n  onLoad() {\r\n    const tempFrvInfo = uni.getStorageSync(tempFrvInfoKey);\r\n    if (tempFrvInfo) {\r\n      this.realName = tempFrvInfo.realName\r\n      this.idCard = tempFrvInfo.idCard\r\n    }\r\n  },\r\n  methods: {\r\n    async getCertifyId() {\r\n      if (!this.certifyIdNext) return\r\n\r\n      // #ifndef APP\r\n      return uni.showModal({\r\n        content: \"暂不支持实名认证\",\r\n        showCancel: false\r\n      })\r\n      // #endif\r\n\r\n      if (!checkIdCard(this.idCard)) {\r\n        uni.showToast({\r\n          title: \"身份证不合法\",\r\n          icon: \"none\"\r\n        })\r\n        return\r\n      }\r\n\r\n      if (\r\n          typeof this.realName !== 'string' ||\r\n          this.realName.length < 2 ||\r\n          !/^[\\u4e00-\\u9fa5]{1,10}(·?[\\u4e00-\\u9fa5]{1,10}){0,5}$/.test(this.realName)\r\n      ) {\r\n        uni.showToast({\r\n          title: \"姓名只能是汉字\",\r\n          icon: \"none\"\r\n        })\r\n        return\r\n      }\r\n\r\n      uni.setStorage({\r\n        key: tempFrvInfoKey,\r\n        data: {\r\n          realName: this.realName,\r\n          idCard: this.idCard\r\n        }\r\n      });\r\n\r\n      const metaInfo = uni.getFacialRecognitionMetaInfo()\r\n\r\n      const res = await uniIdCo.getFrvCertifyId({\r\n        realName: this.realName,\r\n        idCard: this.idCard,\r\n        metaInfo\r\n      })\r\n\r\n      this.certifyId = res.certifyId\r\n\r\n      this.startFacialRecognitionVerify()\r\n    },\r\n    startFacialRecognitionVerify() {\r\n\r\n      // #ifdef APP\r\n      uni.startFacialRecognitionVerify({\r\n        certifyId: this.certifyId,\r\n        progressBarColor: \"#2979ff\",\r\n        success: () => {\r\n          this.verifyFail = false\r\n          this.getFrvAuthResult()\r\n        },\r\n        fail: (e) => {\r\n          let title = \"验证失败\"\r\n          let content\r\n\r\n          console.log(\r\n              `[frv-debug] certifyId auth error: certifyId -> ${this.certifyId}, error -> ${JSON.stringify(e, null, 4)}`\r\n          )\r\n\r\n          switch (e.errCode) {\r\n            case 10001:\r\n              content = '认证ID为空'\r\n              break\r\n            case 10010:\r\n              title = '刷脸异常'\r\n              content = e.cause.message || '错误代码: 10010'\r\n              break\r\n            case 10011:\r\n              title = '验证中断'\r\n              content = e.cause.message || '错误代码: 10011'\r\n              break\r\n            case 10012:\r\n              content = '网络异常'\r\n              break\r\n            case 10013:\r\n              this.verifyFailCode = e.errCode\r\n              this.verifyFailContent = e.cause.message || '错误代码: 10013'\r\n              this.getFrvAuthResult()\r\n\r\n              console.log(\r\n                  `[frv-debug] 刷脸失败, certifyId -> ${this.certifyId}, 如在开发环境请检查用户的姓名、身份证号与刷脸用户是否为同一用户。如遇到认证ID已使用请检查opendb-frv-logs表中certifyId状态`\r\n              )\r\n              return\r\n            case 10020:\r\n              content = '设备设置时间异常'\r\n              break\r\n            default:\r\n              title = ''\r\n              content = `验证未知错误 (${e.errCode})`\r\n              break\r\n          }\r\n\r\n          this.verifyFail = true\r\n          this.verifyFailCode = e.errCode\r\n          this.verifyFailTitle = title\r\n          this.verifyFailContent = content\r\n        }\r\n      })\r\n      // #endif\r\n    },\r\n    async getFrvAuthResult() {\r\n      const uniIdCo = uniCloud.importObject('uni-id-co', {\r\n        customUI: true\r\n      })\r\n      try {\r\n        uni.showLoading({\r\n          title: \"验证中...\",\r\n          mask: false\r\n        })\r\n        const res = await uniIdCo.getFrvAuthResult({\r\n          certifyId: this.certifyId\r\n        })\r\n\r\n        const {\r\n          errCode,\r\n          ...rest\r\n        } = res\r\n\r\n        if (this.verifyFailContent) {\r\n          console.log(`[frv-debug] 客户端刷脸失败，由实人认证服务查询具体原因，原因：${this.verifyFailContent}`)\r\n        }\r\n\r\n        uni.showModal({\r\n          content: \"实名认证成功\",\r\n          showCancel: false,\r\n          success: () => {\r\n            mutations.setUserInfo({\r\n              realNameAuth: rest\r\n            })\r\n            this.verifyFail = false\r\n          }\r\n        })\r\n\r\n        uni.removeStorage({\r\n          key: tempFrvInfoKey\r\n        })\r\n      } catch (e) {\r\n        this.verifyFail = true\r\n        this.verifyFailTitle = e.errMsg\r\n        console.error(JSON.stringify(e));\r\n      } finally {\r\n        uni.hideLoading()\r\n      }\r\n    },\r\n    retry() {\r\n      if (this.verifyFailCode !== 10013) {\r\n        this.getCertifyId()\r\n      } else {\r\n        this.verifyFail = false\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n@import \"@/uni_modules/uni-id-pages/common/login-page.scss\";\r\n\r\n.checkbox-box,\r\n.uni-label-pointer {\r\n  align-items: center;\r\n  display: flex;\r\n  flex-direction: row;\r\n}\r\n\r\n.item {\r\n  flex-direction: row;\r\n}\r\n\r\n.text {\r\n  line-height: 26px;\r\n}\r\n\r\n.checkbox-box ::v-deep .uni-checkbox-input {\r\n  border-radius: 100%;\r\n}\r\n\r\n.checkbox-box ::v-deep .uni-checkbox-input.uni-checkbox-input-checked {\r\n  border-color: $uni-color-primary;\r\n  color: #FFFFFF !important;\r\n  background-color: $uni-color-primary;\r\n}\r\n\r\n.agreements {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.face-icon {\r\n  width: 100px;\r\n  height: 100px;\r\n  margin: 50px auto 30px;\r\n}\r\n\r\n.face-icon-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: block;\r\n}\r\n\r\n.error-title {\r\n  font-size: 18px;\r\n  text-align: center;\r\n  font-weight: bold;\r\n}\r\n\r\n.error-description {\r\n  font-size: 13px;\r\n  color: #999999;\r\n  margin: 10px 0 20px;\r\n  text-align: center;\r\n}\r\n\r\n.dev-tip {\r\n  margin-top: 20px;\r\n  font-size: 13px;\r\n  color: #999;\r\n  text-align: center;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./realname-verify.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./realname-verify.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753757592038\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
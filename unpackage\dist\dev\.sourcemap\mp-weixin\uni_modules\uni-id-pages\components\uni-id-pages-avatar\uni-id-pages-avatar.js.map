{"version": 3, "sources": ["webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/components/uni-id-pages-avatar/uni-id-pages-avatar.vue?9225", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/components/uni-id-pages-avatar/uni-id-pages-avatar.vue?bbdf", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/components/uni-id-pages-avatar/uni-id-pages-avatar.vue?d11a", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/components/uni-id-pages-avatar/uni-id-pages-avatar.vue?f8a2", "uni-app:///uni_modules/uni-id-pages/components/uni-id-pages-avatar/uni-id-pages-avatar.vue", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/components/uni-id-pages-avatar/uni-id-pages-avatar.vue?7297", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/components/uni-id-pages-avatar/uni-id-pages-avatar.vue?9d09"], "names": ["data", "isPC", "props", "width", "type", "default", "height", "border", "mounted", "computed", "<PERSON><PERSON><PERSON><PERSON>", "userInfo", "avatar_file", "methods", "setAvatarFile", "mutations", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avatarUrl", "extname", "name", "url", "cloudPath", "uni", "title", "mask", "uniCloud", "filePath", "fileType", "fileID", "console", "uploadAvatarImg"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AACuE;AACL;AACa;;;AAG/E;AAC+K;AAC/K,gBAAgB,gLAAU;AAC1B,EAAE,yFAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAA4qB,CAAgB,4oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACShsB;;;;;;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AALA,gBAMA;EACAA;IACA;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACA;IACAC;MACAF;MACAC;QACA;MACA;IACA;IACAE;MACAH;MACAC;QACA;MACA;IACA;EACA;EACAG;IAAA;MAAA;QAAA;UAAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAIA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACAC;MACA;MACAC;QAAAH;MAAA;IACA;IACAI;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBACAL;kBACAM;kBACAC;kBACAC;gBACA,GACA;gBACAC;gBACAT;gBAAA;gBAEAU;kBACAC;kBACAC;gBACA;gBAAA;gBAAA,OAGAC;kBACAC;kBACAL;kBACAM;gBACA;cAAA;gBAAA;gBALAC;gBAMAhB;gBACAU;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAO;cAAA;gBAEAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAEA;IAwEA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AC9KA;AAAA;AAAA;AAAA;AAAu9B,CAAgB,u4BAAG,EAAC,C;;;;;;;;;;;ACA3+B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-id-pages/components/uni-id-pages-avatar/uni-id-pages-avatar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-id-pages-avatar.vue?vue&type=template&id=b2994a1e&\"\nvar renderjs\nimport script from \"./uni-id-pages-avatar.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-id-pages-avatar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-id-pages-avatar.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-id-pages/components/uni-id-pages-avatar/uni-id-pages-avatar.vue\"\nexport default component.exports", "export * from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-id-pages-avatar.vue?vue&type=template&id=b2994a1e&\"", "var components\ntry {\n  components = {\n    cloudImage: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-id-pages/components/cloud-image/cloud-image\" */ \"@/uni_modules/uni-id-pages/components/cloud-image/cloud-image.vue\"\n      )\n    },\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-id-pages-avatar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-id-pages-avatar.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<button open-type=\"chooseAvatar\" @chooseavatar=\"bindchooseavatar\" @click=\"uploadAvatarImg\" class=\"box\" :class=\"{'showBorder':border}\"  :style=\"{width,height,lineHeight:height}\">\n\t\t<cloud-image v-if=\"avatar_file\" :src=\"avatar_file.url\" :width=\"width\" :height=\"height\"></cloud-image>\r\n\t\t<uni-icons v-else :style=\"{width,height,lineHeight:height}\" class=\"chooseAvatar\" type=\"plusempty\" size=\"30\"\r\n\t\t\tcolor=\"#dddddd\"></uni-icons>\r\n\t</button>\r\n</template>\r\n\r\n<script>\r\n\timport {\n\t\tstore,\n\t\tmutations\n\t} from '@/uni_modules/uni-id-pages/common/store.js'\r\n\t/**\r\n\t* uni-id-pages-avatar \r\n\t* @description 用户头像组件\r\n\t* @property {String} width\t图片的宽，默认为：50px\r\n\t* @property {String} height\t图片的高，默认为：50px\r\n\t*/\r\n\texport default {\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tisPC: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\t//头像图片宽\r\n\t\t\twidth: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn \"50px\"\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t//头像图片高\r\n\t\t\theight: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn \"50px\"\r\n\t\t\t\t}\r\n\t\t\t},\n\t\t\tborder:{\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault () {\n\t\t\t\t\treturn false\n\t\t\t\t}\n\t\t\t}\r\n\t\t},\r\n\t\tasync mounted() {\r\n\t\t\t// #ifdef H5\r\n\t\t\tthis.isPC = !['ios', 'android'].includes(uni.getSystemInfoSync().platform);\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\thasLogin() {\n\t\t\t\treturn store.hasLogin\n\t\t\t},\n\t\t\tuserInfo() {\n\t\t\t\treturn store.userInfo\n\t\t\t},\n\t\t\tavatar_file() {\n\t\t\t\treturn store.userInfo.avatar_file\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tsetAvatarFile(avatar_file) {\r\n\t\t\t\t// 使用 clientDB 提交数据\n\t\t\t\tmutations.updateUserInfo({avatar_file})\r\n\t\t\t},\n\t\t\tasync bindchooseavatar(res){\n\t\t\t\tlet avatarUrl = res.detail.avatarUrl\n\t\t\t\tlet avatar_file = {\n\t\t\t\t\textname: avatarUrl.split('.')[avatarUrl.split('.').length - 1],\n\t\t\t\t\tname:'',\n\t\t\t\t\turl:''\n\t\t\t\t}\n\t\t\t\t//上传到服务器\n\t\t\t\tlet cloudPath = this.userInfo._id + '' + Date.now()\n\t\t\t\tavatar_file.name = cloudPath\n\t\t\t\ttry{\n\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\ttitle: \"更新中\",\n\t\t\t\t\t\tmask: true\n\t\t\t\t\t});\n\t\t\t\t\tlet {\n\t\t\t\t\t\tfileID\n\t\t\t\t\t} = await uniCloud.uploadFile({\n\t\t\t\t\t\tfilePath:avatarUrl,\n\t\t\t\t\t\tcloudPath,\n\t\t\t\t\t\tfileType: \"image\"\n\t\t\t\t\t});\n\t\t\t\t\tavatar_file.url = fileID\n\t\t\t\t\tuni.hideLoading()\n\t\t\t\t}catch(e){\n\t\t\t\t\tconsole.error(e);\n\t\t\t\t}\n\t\t\t\tconsole.log('avatar_file',avatar_file);\n\t\t\t\tthis.setAvatarFile(avatar_file)\n\t\t\t},\r\n\t\t\tuploadAvatarImg(res) {\n\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\treturn false // 微信小程序走 bindchooseavatar方法\n\t\t\t\t// #endif\n\t\t\t\t\n\t\t\t\t// #ifndef MP-WEIXIN\n\t\t\t\tif(!this.hasLogin){\n\t\t\t\t\treturn uni.navigateTo({\n\t\t\t\t\t\turl:'/uni_modules/uni-id-pages/pages/login/login-withoutpwd'\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t\tconst crop = {\n\t\t\t\t\tquality: 100,\n\t\t\t\t\twidth: 600,\n\t\t\t\t\theight: 600,\n\t\t\t\t\tresize: true\n\t\t\t\t};\n\t\t\t\tuni.chooseImage({\n\t\t\t\t\tcount: 1,\n\t\t\t\t\tcrop,\n\t\t\t\t\tsuccess: async (res) => {\n\t\t\t\t\t\tlet tempFile = res.tempFiles[0],\n\t\t\t\t\t\t\tavatar_file = {\n\t\t\t\t\t\t\t\t// #ifdef H5\n\t\t\t\t\t\t\t\textname: tempFile.name.split('.')[tempFile.name.split('.').length - 1],\n\t\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t\t\t// #ifndef H5\n\t\t\t\t\t\t\t\textname: tempFile.path.split('.')[tempFile.path.split('.').length - 1]\n\t\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tfilePath = res.tempFilePaths[0]\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t//非app端剪裁头像，app端用内置的原生裁剪\n\t\t\t\t\t\t// #ifndef APP-PLUS\n\t\t\t\t\t\tfilePath = await new Promise((callback) => {\n\t\t\t\t\t\t\t// #ifdef H5\n\t\t\t\t\t\t\tif (!this.isPC) {\n\t\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\t\turl: '/uni_modules/uni-id-pages/pages/userinfo/cropImage/cropImage?path=' +\n\t\t\t\t\t\t\t\t\t\tfilePath + `&options=${JSON.stringify(crop)}`,\n\t\t\t\t\t\t\t\t\tanimationType: \"fade-in\",\n\t\t\t\t\t\t\t\t\tevents: {\n\t\t\t\t\t\t\t\t\t\tsuccess: url => {\n\t\t\t\t\t\t\t\t\t\t\tcallback(url)\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\tcomplete(e) {\n\t\t\t\t\t\t\t\t\t\t// console.log(e);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t})\n\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t\n\t\t\t\t\t\tlet cloudPath = this.userInfo._id + '' + Date.now()\n\t\t\t\t\t\tavatar_file.name = cloudPath\n\t\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\t\ttitle: \"更新中\",\n\t\t\t\t\t\t\tmask: true\n\t\t\t\t\t\t});\n\t\t\t\t\t\tlet {\n\t\t\t\t\t\t\tfileID\n\t\t\t\t\t\t} = await uniCloud.uploadFile({\n\t\t\t\t\t\t\tfilePath,\n\t\t\t\t\t\t\tcloudPath,\n\t\t\t\t\t\t\tfileType: \"image\"\n\t\t\t\t\t\t});\n\t\t\t\t\t\tavatar_file.url = fileID\n\t\t\t\t\t\tuni.hideLoading()\n\t\t\t\t\t\tthis.setAvatarFile(avatar_file)\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\n\t/* #ifndef APP-NVUE */\n\t.box{\n\t\toverflow: hidden;\n\t}\n\t/* #endif */\n\t.box{\n\t\tpadding: 0;\n\t}\n\t\r\n\t.chooseAvatar {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: inline-block;\n\t\tbox-sizing: border-box;\r\n\t\t/* #endif */\r\n\t\tborder-radius: 10px;\r\n\t\ttext-align: center;\n\t\tpadding: 1px;\n\t}\n\t.showBorder{\n\t\tborder: solid 1px #ddd;\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-id-pages-avatar.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-id-pages-avatar.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753757591205\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
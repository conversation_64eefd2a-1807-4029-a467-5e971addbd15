{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/userinfo/bind-mobile/bind-mobile.vue?b404", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/userinfo/bind-mobile/bind-mobile.vue?1b9f", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/userinfo/bind-mobile/bind-mobile.vue?0e09", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/userinfo/bind-mobile/bind-mobile.vue?b9ba", "uni-app:///uni_modules/uni-id-pages/pages/userinfo/bind-mobile/bind-mobile.vue", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/userinfo/bind-mobile/bind-mobile.vue?200b", "webpack:///D:/web/project/租房小程序/uni_modules/uni-id-pages/pages/userinfo/bind-mobile/bind-mobile.vue?864c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "formData", "mobile", "code", "<PERSON><PERSON>a", "focusMobile", "logo", "computed", "tipText", "onLoad", "onReady", "methods", "submit", "title", "icon", "duration", "uniIdCo", "uni", "mutations", "console"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACc;;;AAGxE;AACkL;AAClL,gBAAgB,gLAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,oSAEN;AACP,KAAK;AACL;AACA,aAAa,iVAEN;AACP,KAAK;AACL;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChDA;AAAA;AAAA;AAAA;AAAmrB,CAAgB,ooBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACqBvsB;;;;;;;;;;;;;;;;;;;;;eAIA;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;EACAC;EACAC;EAEAC;IACA;AACA;AACA;IACAC;MAAA;MACA;QACA;QACA;UACAC;UACAC;UACAC;QACA;MACA;MACA;QACA;QACA;UACAF;UACAC;UACAC;QACA;MACA;MAEA;MACAC;QACAC;UACAJ;UACAC;UACAC;QACA;QAKA;QAEAG;QACAD;MACA;QACAE;QACA;UACA;QACA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5FA;AAAA;AAAA;AAAA;AAA0xC,CAAgB,+nCAAG,EAAC,C;;;;;;;;;;;ACA9yC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-id-pages/pages/userinfo/bind-mobile/bind-mobile.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './uni_modules/uni-id-pages/pages/userinfo/bind-mobile/bind-mobile.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./bind-mobile.vue?vue&type=template&id=7ad8c6f5&\"\nvar renderjs\nimport script from \"./bind-mobile.vue?vue&type=script&lang=js&\"\nexport * from \"./bind-mobile.vue?vue&type=script&lang=js&\"\nimport style0 from \"./bind-mobile.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-id-pages/pages/userinfo/bind-mobile/bind-mobile.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./bind-mobile.vue?vue&type=template&id=7ad8c6f5&\"", "var components\ntry {\n  components = {\n    uniEasyinput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput\" */ \"@/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue\"\n      )\n    },\n    uniIdPagesSmsForm: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-id-pages/components/uni-id-pages-sms-form/uni-id-pages-sms-form\" */ \"@/uni_modules/uni-id-pages/components/uni-id-pages-sms-form/uni-id-pages-sms-form.vue\"\n      )\n    },\n    uniPopupCaptcha: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-captcha/components/uni-popup-captcha/uni-popup-captcha\" */ \"@/uni_modules/uni-captcha/components/uni-popup-captcha/uni-popup-captcha.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.focusMobile = false\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./bind-mobile.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./bind-mobile.vue?vue&type=script&lang=js&\"", "<!-- 绑定手机号码页 -->\r\n<template>\r\n\t<view class=\"uni-content\">\r\n\t\t<match-media :min-width=\"690\">\r\n\t\t\t<view class=\"login-logo\">\r\n\t\t\t\t<image :src=\"logo\"></image>\r\n\t\t\t</view>\r\n\t\t\t<!-- 顶部文字 -->\r\n\t\t\t<text class=\"title title-box\">绑定手机号</text>\r\n\t\t</match-media>\r\n\t\t<!-- 登录框 (选择手机号所属国家和地区需要另行实现) -->\r\n\t\t<uni-easyinput clearable :focus=\"focusMobile\" @blur=\"focusMobile = false\" type=\"number\" class=\"input-box\" :inputBorder=\"false\" v-model=\"formData.mobile\"\r\n\t\t\tmaxlength=\"11\" placeholder=\"请输入手机号\"></uni-easyinput>\r\n\t\t<uni-id-pages-sms-form ref=\"smsForm\" type=\"bind-mobile-by-sms\" v-model=\"formData.code\" :phone=\"formData.mobile\">\r\n\t\t</uni-id-pages-sms-form>\r\n\t\t<button class=\"uni-btn send-btn-box\" type=\"primary\" @click=\"submit\">提交</button>\r\n\t\t<uni-popup-captcha @confirm=\"submit\" v-model=\"formData.captcha\" scene=\"bind-mobile-by-sms\" ref=\"popup\">\r\n\t\t</uni-popup-captcha>\r\n\t</view>\r\n</template>\r\n<script>\r\n\timport {\r\n\t\tstore,\r\n\t\tmutations\r\n\t} from '@/uni_modules/uni-id-pages/common/store.js'\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tformData: {\r\n\t\t\t\t\tmobile: \"\",\r\n\t\t\t\t\tcode: \"\",\r\n\t\t\t\t\tcaptcha: \"\"\r\n\t\t\t\t},\r\n\t\t\t\tfocusMobile:true,\r\n\t\t\t\tlogo: \"/static/logo.png\"\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\ttipText() {\r\n\t\t\t\treturn `验证码已通过短信发送至 ${this.formData.mobile}。密码为6 - 20位`\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(event) {},\r\n\t\tonReady() {},\r\n\r\n\t\tmethods: {\r\n\t\t\t/**\r\n\t\t\t * 完成并提交\r\n\t\t\t */\r\n\t\t\tsubmit() {\r\n\t\t\t\tif(! /^1\\d{10}$/.test(this.formData.mobile)){\r\n\t\t\t\t\tthis.focusMobile = true \r\n\t\t\t\t\treturn uni.showToast({\r\n\t\t\t\t\t\ttitle: '手机号码格式不正确',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 3000\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\tif(! /^\\d{6}$/.test(this.formData.code)){\r\n\t\t\t\t\tthis.$refs.smsForm.focusSmsCodeInput = true \r\n\t\t\t\t\treturn uni.showToast({\r\n\t\t\t\t\t\ttitle: '验证码格式不正确',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 3000\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tconst uniIdCo = uniCloud.importObject(\"uni-id-co\")\r\n\t\t\t\tuniIdCo.bindMobileBySms(this.formData).then(e => {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: e.errMsg,\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 3000\r\n\t\t\t\t\t});\r\n\t\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\t\tconst eventChannel = this.$scope.eventChannel; // 兼容APP-NVUE\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\t\tconst eventChannel = this.getOpenerEventChannel();\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\tmutations.setUserInfo(this.formData)\r\n\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t}).catch(e => {\r\n\t\t\t\t\tconsole.log(e);\r\n\t\t\t\t\tif (e.errCode == 'uni-id-captcha-required') {\r\n\t\t\t\t\t\tthis.$refs.popup.open()\r\n\t\t\t\t\t}\r\n\t\t\t\t}).finally(e => {\r\n\t\t\t\t\tthis.formData.captcha = \"\"\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t@import \"@/uni_modules/uni-id-pages/common/login-page.scss\";\r\n\r\n\t.uni-content {\r\n\t\tpadding: 0;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tpadding: 50rpx;\r\n\t\tpadding-top: 10px;\r\n\t}\r\n\t\r\n\t\r\n\t@media screen and (min-width: 690px) {\r\n\t\t.uni-content{\r\n\t\t\tpadding: 30px 40px 40px;\r\n\t\t}\r\n\t}\r\n\r\n\t/* #ifndef APP-NVUE  || VUE3 */\r\n\t.uni-content ::v-deep .uni-easyinput__content {}\r\n\r\n\t/* #endif */\r\n\t.input-box {\r\n\t\twidth: 100%;\r\n\t\tmargin-top: 16px;\r\n\t\tbackground-color: #f9f9f9;\r\n\t\tborder-radius: 6rpx;\r\n\t\tflex-direction: row;\r\n\t\tflex-wrap: nowrap;\r\n\t\tmargin-bottom: 10px;\r\n\t}\r\n\r\n\t.send-btn-box {\r\n\t\tmargin-top: 15px;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./bind-mobile.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./bind-mobile.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753757592010\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
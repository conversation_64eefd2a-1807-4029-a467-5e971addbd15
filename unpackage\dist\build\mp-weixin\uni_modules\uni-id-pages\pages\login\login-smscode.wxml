<view class="uni-content data-v-4dc54d79"><view class="login-logo data-v-4dc54d79"><image src="{{logo}}" class="data-v-4dc54d79"></image></view><text class="title data-v-4dc54d79">请输入验证码</text><text class="tip data-v-4dc54d79">先输入图形验证码，再获取短信验证码</text><uni-forms vue-id="781fb2d0-1" class="data-v-4dc54d79" bind:__l="__l" vue-slots="{{['default']}}"><uni-id-pages-sms-form bind:input="__e" vue-id="{{('781fb2d0-2')+','+('781fb2d0-1')}}" focusCaptchaInput="{{true}}" type="login-by-sms" phone="{{phone}}" data-ref="smsCode" value="{{code}}" data-event-opts="{{[['^input',[['__set_model',['','code','$event',[]]]]]]}}" class="data-v-4dc54d79 vue-ref" bind:__l="__l"></uni-id-pages-sms-form><button class="uni-btn send-btn data-v-4dc54d79" type="primary" data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" bindtap="__e">登录</button></uni-forms><uni-popup-captcha vue-id="781fb2d0-3" scene="login-by-sms" data-ref="popup" value="{{captcha}}" data-event-opts="{{[['^confirm',[['submit']]],['^input',[['__set_model',['','captcha','$event',[]]]]]]}}" bind:confirm="__e" bind:input="__e" class="data-v-4dc54d79 vue-ref" bind:__l="__l"></uni-popup-captcha></view>